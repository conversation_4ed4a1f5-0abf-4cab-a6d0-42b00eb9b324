{"499998-policy.flap": {"499998-policy.flap@@@0": {"indexable.columns": {"severity": {}, "policy.id": {}, "object.id": {}, "instance": {}}, "severity": 0, "type": 8}}, "499999-event.history": {"499999-event.history@@@0": {"indexable.columns": {"event.source": {}, "event.category": {}, "event.source.type": {}, "event.severity": {}}, "message": 0, "type": 11}}, "500001-trap": {"500001-trap@@@0": {"indexable.columns": {"trap.severity": {}, "trap.oid": {}, "event.source": {}}, "trap.message": 0, "type": 13}}, "500003-policy": {"500003-policy@@@0": {"indexable.columns": {"object.id": {}, "instance": {}, "severity": {}, "policy.id": {}}, "severity": 0, "type": 14}}, "500004-policy": {"500004-policy@@@0": {"indexable.columns": {"severity": {}, "policy.id": {}, "policy.type": {}, "event.source": {}}, "severity": 0, "type": 15}}, "500005-policy": {"500005-policy@@@0": {"indexable.columns": {"severity": {}, "policy.id": {}, "policy.type": {}, "event.source": {}}, "severity": 0, "type": 15}}, "500010-policy": {"500010-policy@@@0": {"indexable.columns": {"severity": {}, "policy.id": {}, "policy.type": {}, "event.source": {}}, "severity": 0, "type": 15}}, "500027-policy": {"500027-policy@@@0": {"indexable.columns": {"severity": {}, "policy.id": {}, "netroute.id": {}, "policy.type": {}}, "severity": 0, "type": 15}}, "500028-policy.flap": {"500028-policy.flap@@@0": {"indexable.columns": {"severity": {}, "policy.id": {}, "netroute.id": {}, "policy.type": {}}, "severity": 0, "type": 8}}, "500011-log.stat": {"500011-log.stat@@@0": {"indexable.columns": {"event.source": {}, "event.category": {}, "event.source.type": {}}, "log.volume.bytes": 1, "log.volume.bytes.per.sec": 1, "logs.per.sec": 1, "type": 11}}, "500018-flow": {"500018-flow@@@0": {"indexable.columns": {"event.source": {}}, "flow.volume.bytes": 1, "flow.volume.bytes.per.sec": 1, "flows.per.sec": 1, "type": 12}}}