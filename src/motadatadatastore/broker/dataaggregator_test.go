/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/* Change Logs:
* Date                   Author          		Notes
* 2025-03-05			 <PERSON><PERSON><PERSON>-5190  Updated constants from datastore package to utils package to match SonarQube Standard
 */

package broker

import (
	"bytes"
	"fmt"
	"github.com/dolthub/swiss"
	"github.com/stretchr/testify/assert"
	"math/rand"
	"motadatadatastore/codec"
	"motadatadatastore/utils"
	"os"
	"testing"
	"time"
)

func TestWriteEventAggregationBatch(t *testing.T) {

	assertions := assert.New(t)

	dataAggregatorFlushTimeSeconds = 1

	utils.CleanUpStores()

	_ = os.RemoveAll(utils.EventDir)

	utils.Create(utils.TempDir)

	utils.Create(utils.TempDir + utils.HyphenSeparator + utils.Aggregations)

	utils.Create(utils.EventDir + utils.PathSeparator + utils.VerticalFormat)

	utils.Create(utils.EventDir + utils.PathSeparator + utils.HorizontalFormat)

	utils.Create(utils.EventDir + utils.PathSeparator + utils.HorizontalFormat + utils.HyphenSeparator + utils.Aggregations)

	stringFields := make(map[string][]string)

	numericFields := make(map[string][]int64)

	dataTypes := make(map[string]codec.DataType)

	storeType := utils.Log

	tick := utils.INT64ToStringValue(time.Now().Unix())

	plugin := "60005-fortinet.traffic"

	view := "60005-fortinet.traffic@@@1"

	interval := 5

	//indexable columns
	dataTypes[utils.EventSource] = codec.String

	dataTypes[utils.EventCategory] = codec.String

	dataTypes["fortinet.traffic.log.level"] = codec.Int64

	indexableColumns := []string{utils.EventSource, utils.EventCategory, "fortinet.traffic.log.level"}

	utils.SortStringValues(indexableColumns)

	//aggregated columns

	dataTypes["fortinet.traffic.volume.bytes"] = codec.Int64

	dataTypes["fortinet.traffic.source.ip"] = codec.String

	aggregationColumns := []string{"fortinet.traffic.volume.bytes", "fortinet.traffic.source.ip"}

	//string Columns

	stringFields[utils.EventSource] = []string{"************", "************", "************", "************"}

	stringFields["fortinet.traffic.source.ip"] = []string{"************", "************", "************", "************"}

	stringFields[utils.EventCategory] = []string{"fortinet.traffic", "fortinet.traffic", "fortinet.traffic", "fortinet.traffic"}

	//numeric fields

	numericFields["fortinet.traffic.volume.bytes"] = []int64{1234, 234, 456, utils.DummyINT64Value}

	numericFields["fortinet.traffic.log.level"] = []int64{1, 2, utils.DummyINT64Value, utils.DummyINT64Value}

	event := DataAggregationEvent{

		stringFields:       stringFields,
		numericFields:      numericFields,
		aggregationColumns: aggregationColumns,
		indexableColumns:   indexableColumns,
		dataTypes:          dataTypes,
		storeType:          storeType,
		tick:               tick,
		plugin:             plugin,
		view:               view,
		interval:           interval,
		batchSize:          4,
	}

	aggregator := NewDataAggregator(0)

	aggregator.tokenizer = &utils.Tokenizer{

		Tokens: make([]string, utils.TokenizerLength),
	}

	for i := range aggregator.buffers {

		aggregator.buffers[i] = bytes.NewBuffer(make([]byte, utils.MaxBlobBytes))

		aggregator.buffers[i].Reset()
	}

	aggregator.aggregate(event)

	time.Sleep(time.Second * 3)

	bufferBytes, err := utils.ReadLogFile("Data Aggregator", "broker")

	assertions.NotNil(len(bufferBytes))

	assertions.Nil(err)

	assertions.True(len(aggregator.indexableColumns) > 0)

	assertions.True(len(aggregator.aggregationColumns) > 0)

}

func TestWriteEventAggregationBatchType1(t *testing.T) {

	assertions := assert.New(t)

	utils.CleanUpStores()

	_ = os.RemoveAll(utils.EventDir)

	utils.Create(utils.TempDir)

	utils.Create(utils.TempDir + utils.HyphenSeparator + utils.Aggregations)

	utils.Create(utils.EventDir + utils.PathSeparator + utils.VerticalFormat)

	utils.Create(utils.EventDir + utils.PathSeparator + utils.HorizontalFormat)

	utils.Create(utils.EventDir + utils.PathSeparator + utils.HorizontalFormat + utils.HyphenSeparator + utils.Aggregations)

	stringFields := make(map[string][]string)

	numericFields := make(map[string][]int64)

	dataTypes := make(map[string]codec.DataType)

	storeType := utils.Log

	tick := utils.INT64ToStringValue(time.Now().Unix())

	plugin := "60005-fortinet.traffic"

	view := "60005-fortinet.traffic@@@1"

	interval := 5

	//indexable columns
	dataTypes[utils.EventSource] = codec.String

	dataTypes[utils.EventCategory] = codec.String

	dataTypes["fortinet.traffic.log.level"] = codec.Int64

	indexableColumns := []string{utils.EventSource, utils.EventCategory, "fortinet.traffic.log.level"}

	utils.SortStringValues(indexableColumns)

	//aggregated columns

	dataTypes["fortinet.traffic.volume.bytes"] = codec.Int64

	dataTypes["fortinet.traffic.source.ip"] = codec.String

	aggregationColumns := []string{"fortinet.traffic.volume.bytes", "fortinet.traffic.source.ip"}

	//string Columns

	stringFields[utils.EventSource] = []string{"************", "************", "************", "************"}

	stringFields["fortinet.traffic.source.ip"] = []string{"************", "************", "************", "************"}

	stringFields[utils.EventCategory] = []string{"fortinet.traffic", "fortinet.traffic", "fortinet.traffic", "fortinet.traffic"}

	//numeric fields

	numericFields["fortinet.traffic.volume.bytes"] = []int64{1234, 234, 456, utils.DummyINT64Value}

	numericFields["fortinet.traffic.log.level"] = []int64{1, 2, utils.DummyINT64Value, utils.DummyINT64Value}

	event := DataAggregationEvent{

		stringFields:       stringFields,
		numericFields:      numericFields,
		aggregationColumns: aggregationColumns,
		indexableColumns:   indexableColumns,
		dataTypes:          dataTypes,
		storeType:          storeType,
		tick:               tick,
		plugin:             plugin,
		view:               view,
		interval:           interval,
		batchSize:          4,
	}

	aggregator := NewDataAggregator(0)

	aggregator.tokenizer = &utils.Tokenizer{

		Tokens: make([]string, utils.TokenizerLength),
	}

	for i := range aggregator.buffers {

		aggregator.buffers[i] = bytes.NewBuffer(make([]byte, utils.MaxBlobBytes))

		aggregator.buffers[i].Reset()
	}

	aggregator.aggregate(event)

	time.Sleep(time.Second * 3)

	bufferBytes, err := utils.ReadLogFile("Data Aggregator", "broker")

	assertions.NotNil(len(bufferBytes))

	assertions.Nil(err)

	assertions.True(len(aggregator.indexableColumns) > 0)

	assertions.True(len(aggregator.aggregationColumns) > 0)

}

// indexable column 1 - string indexable column 2 -string , again aggregating indexable column 1 - int64 , indexable column 2 - int64
func TestWriteEventAggregationBatchType2(t *testing.T) {

	assertions := assert.New(t)

	utils.CleanUpStores()

	_ = os.RemoveAll(utils.EventDir)

	utils.Create(utils.TempDir)

	utils.Create(utils.TempDir + utils.HyphenSeparator + utils.Aggregations)

	utils.Create(utils.EventDir + utils.PathSeparator + utils.VerticalFormat)

	utils.Create(utils.EventDir + utils.PathSeparator + utils.HorizontalFormat)

	utils.Create(utils.EventDir + utils.PathSeparator + utils.HorizontalFormat + utils.HyphenSeparator + utils.Aggregations)

	stringFields := make(map[string][]string)

	numericFields := make(map[string][]int64)

	dataTypes := make(map[string]codec.DataType)

	storeType := utils.Log

	tick := utils.INT64ToStringValue(time.Now().Unix())

	plugin := "60005-fortinet.traffic"

	view := "60005-fortinet.traffic@@@1"

	interval := 5

	//indexable columns
	dataTypes[utils.EventSource] = codec.String

	dataTypes[utils.EventCategory] = codec.String

	dataTypes["fortinet.traffic.log.level"] = codec.String

	dataTypes["fortinet.traffic.log.id"] = codec.String

	indexableColumns := []string{utils.EventSource, utils.EventCategory, "fortinet.traffic.log.level", "fortinet.traffic.log.id"}

	utils.SortStringValues(indexableColumns)

	//aggregated columns

	dataTypes["fortinet.traffic.volume.bytes"] = codec.Int64

	dataTypes["fortinet.traffic.source.ip"] = codec.String

	aggregationColumns := []string{"fortinet.traffic.volume.bytes", "fortinet.traffic.source.ip"}

	//string Columns

	stringFields[utils.EventSource] = []string{"************", "************", "************", "************"}

	stringFields["fortinet.traffic.source.ip"] = []string{"************", "************", "************", "************"}

	stringFields[utils.EventCategory] = []string{"fortinet.traffic", "fortinet.traffic", "fortinet.traffic", "fortinet.traffic"}

	stringFields["fortinet.traffic.log.level"] = []string{"1", "2", "3", "4"}

	stringFields["fortinet.traffic.log.id"] = []string{"id1", "id2", "id3", "id4"}

	//numeric fields

	numericFields["fortinet.traffic.volume.bytes"] = []int64{1234, 234, 456, utils.DummyINT64Value}

	event := DataAggregationEvent{

		stringFields:       stringFields,
		numericFields:      numericFields,
		aggregationColumns: aggregationColumns,
		indexableColumns:   indexableColumns,
		dataTypes:          dataTypes,
		storeType:          storeType,
		tick:               tick,
		plugin:             plugin,
		view:               view,
		interval:           interval,
		batchSize:          4,
	}

	aggregator := NewDataAggregator(0)

	aggregator.tokenizer = &utils.Tokenizer{

		Tokens: make([]string, utils.TokenizerLength),
	}

	for i := range aggregator.buffers {

		aggregator.buffers[i] = bytes.NewBuffer(make([]byte, utils.MaxBlobBytes))

		aggregator.buffers[i].Reset()
	}

	aggregator.aggregate(event)

	// again sending batch

	testDataTypes := make(map[string]codec.DataType)

	for key, value := range dataTypes {

		testDataTypes[key] = value
	}

	testDataTypes["fortinet.traffic.log.level"] = codec.Int64

	testDataTypes["fortinet.traffic.log.id"] = codec.Int64

	delete(stringFields, "fortinet.traffic.log.level")

	delete(stringFields, "fortinet.traffic.log.id")

	numericFields["fortinet.traffic.log.level"] = []int64{1, 2, utils.DummyINT64Value, utils.DummyINT64Value}

	numericFields["fortinet.traffic.log.id"] = []int64{1, 2, utils.DummyINT64Value, utils.DummyINT64Value}

	aggregationEvent := DataAggregationEvent{

		stringFields:       stringFields,
		numericFields:      numericFields,
		aggregationColumns: aggregationColumns,
		indexableColumns:   indexableColumns,
		dataTypes:          testDataTypes,
		storeType:          storeType,
		tick:               tick,
		plugin:             plugin,
		view:               view,
		interval:           interval,
		batchSize:          4,
	}

	aggregator.aggregate(aggregationEvent)

	time.Sleep(time.Second * 3)

	bufferBytes, err := utils.ReadLogFile("Data Aggregator", "broker")

	assertions.NotNil(len(bufferBytes))

	assertions.Nil(err)

	assertions.True(len(aggregator.indexableColumns) > 0)

	assertions.True(len(aggregator.aggregationColumns) > 0)

}

// indexable column 1 - int64 indexable column 2 -int64 , again aggregating indexable column 1 - string , indexable column 2 - string
func TestWriteEventAggregationBatchType3(t *testing.T) {

	assertions := assert.New(t)

	utils.CleanUpStores()

	_ = os.RemoveAll(utils.EventDir)

	utils.Create(utils.TempDir)

	utils.Create(utils.TempDir + utils.HyphenSeparator + utils.Aggregations)

	utils.Create(utils.EventDir + utils.PathSeparator + utils.VerticalFormat)

	utils.Create(utils.EventDir + utils.PathSeparator + utils.HorizontalFormat)

	utils.Create(utils.EventDir + utils.PathSeparator + utils.HorizontalFormat + utils.HyphenSeparator + utils.Aggregations)

	stringFields := make(map[string][]string)

	numericFields := make(map[string][]int64)

	dataTypes := make(map[string]codec.DataType)

	storeType := utils.Log

	tick := utils.INT64ToStringValue(time.Now().Unix())

	plugin := "60005-fortinet.traffic.2"

	view := "60005-fortinet.traffic@@@1"

	interval := 5

	//indexable columns
	dataTypes[utils.EventSource] = codec.String

	dataTypes[utils.EventCategory] = codec.String

	dataTypes["fortinet.traffic.log.level"] = codec.Int64

	dataTypes["fortinet.traffic.log.id"] = codec.Int64

	indexableColumns := []string{utils.EventSource, utils.EventCategory, "fortinet.traffic.log.level", "fortinet.traffic.log.id"}

	utils.SortStringValues(indexableColumns)

	//aggregated columns

	dataTypes["fortinet.traffic.volume.bytes"] = codec.Int64

	dataTypes["fortinet.traffic.source.ip"] = codec.String

	aggregationColumns := []string{"fortinet.traffic.volume.bytes", "fortinet.traffic.source.ip"}

	//string Columns

	stringFields[utils.EventSource] = []string{"************", "************", "************", "************"}

	stringFields["fortinet.traffic.source.ip"] = []string{"************", "************", "************", "************"}

	stringFields[utils.EventCategory] = []string{"fortinet.traffic", "fortinet.traffic", "fortinet.traffic", "fortinet.traffic"}

	//numeric fields

	numericFields["fortinet.traffic.log.level"] = []int64{1, 2, 3, 4}

	numericFields["fortinet.traffic.log.id"] = []int64{1, 2, 3, 4}

	numericFields["fortinet.traffic.volume.bytes"] = []int64{1234, 234, 456, utils.DummyINT64Value}

	event := DataAggregationEvent{

		stringFields:       stringFields,
		numericFields:      numericFields,
		aggregationColumns: aggregationColumns,
		indexableColumns:   indexableColumns,
		dataTypes:          dataTypes,
		storeType:          storeType,
		tick:               tick,
		plugin:             plugin,
		view:               view,
		interval:           interval,
		batchSize:          4,
	}

	aggregator := NewDataAggregator(0)

	aggregator.tokenizer = &utils.Tokenizer{

		Tokens: make([]string, utils.TokenizerLength),
	}

	for i := range aggregator.buffers {

		aggregator.buffers[i] = bytes.NewBuffer(make([]byte, utils.MaxBlobBytes))

		aggregator.buffers[i].Reset()
	}

	aggregator.aggregate(event)

	// again sending batch

	testDataTypes := make(map[string]codec.DataType)

	for key, value := range dataTypes {

		testDataTypes[key] = value
	}

	testDataTypes["fortinet.traffic.log.level"] = codec.String

	testDataTypes["fortinet.traffic.log.id"] = codec.String

	delete(numericFields, "fortinet.traffic.log.level")

	delete(numericFields, "fortinet.traffic.log.id")

	stringFields["fortinet.traffic.log.level"] = []string{"1", "2", "3", "4"}

	stringFields["fortinet.traffic.log.id"] = []string{"id1", "id2", "id3", "id4"}

	aggregationEvent := DataAggregationEvent{

		stringFields:       stringFields,
		numericFields:      numericFields,
		aggregationColumns: aggregationColumns,
		indexableColumns:   indexableColumns,
		dataTypes:          testDataTypes,
		storeType:          storeType,
		tick:               tick,
		plugin:             plugin,
		view:               view,
		interval:           interval,
		batchSize:          4,
	}

	aggregator.aggregate(aggregationEvent)

	time.Sleep(time.Second * 3)

	bufferBytes, err := utils.ReadLogFile("Data Aggregator", "broker")

	assertions.NotNil(len(bufferBytes))

	assertions.Nil(err)

	assertions.True(len(aggregator.indexableColumns) > 0)

	assertions.True(len(aggregator.aggregationColumns) > 0)

}

// indexable column 1 - int64 indexable column 2 -string , again aggregating indexable column 1 - string , indexable column 2 - int64
func TestWriteEventAggregationBatchType4(t *testing.T) {

	assertions := assert.New(t)

	utils.CleanUpStores()

	_ = os.RemoveAll(utils.EventDir)

	utils.Create(utils.TempDir)

	utils.Create(utils.TempDir + utils.HyphenSeparator + utils.Aggregations)

	utils.Create(utils.EventDir + utils.PathSeparator + utils.VerticalFormat)

	utils.Create(utils.EventDir + utils.PathSeparator + utils.HorizontalFormat)

	utils.Create(utils.EventDir + utils.PathSeparator + utils.HorizontalFormat + utils.HyphenSeparator + utils.Aggregations)

	stringFields := make(map[string][]string)

	numericFields := make(map[string][]int64)

	dataTypes := make(map[string]codec.DataType)

	storeType := utils.Log

	tick := utils.INT64ToStringValue(time.Now().Unix())

	plugin := "60005-fortinet.traffic.2"

	view := "60005-fortinet.traffic@@@1"

	interval := 5

	//indexable columns
	dataTypes[utils.EventSource] = codec.String

	dataTypes[utils.EventCategory] = codec.String

	dataTypes["fortinet.traffic.log.level"] = codec.Int64

	dataTypes["fortinet.traffic.log.id"] = codec.String

	indexableColumns := []string{utils.EventSource, utils.EventCategory, "fortinet.traffic.log.level", "fortinet.traffic.log.id"}

	utils.SortStringValues(indexableColumns)

	//aggregated columns

	dataTypes["fortinet.traffic.volume.bytes"] = codec.Int64

	dataTypes["fortinet.traffic.source.ip"] = codec.String

	aggregationColumns := []string{"fortinet.traffic.volume.bytes", "fortinet.traffic.source.ip"}

	//string Columns

	stringFields[utils.EventSource] = []string{"************", "************", "************", "************"}

	stringFields["fortinet.traffic.source.ip"] = []string{"************", "************", "************", "************"}

	stringFields[utils.EventCategory] = []string{"fortinet.traffic", "fortinet.traffic", "fortinet.traffic", "fortinet.traffic"}

	//numeric fields

	numericFields["fortinet.traffic.log.level"] = []int64{1, 2, 3, 4}

	stringFields["fortinet.traffic.log.id"] = []string{"id1", "id2", "id3", "id4"}

	numericFields["fortinet.traffic.volume.bytes"] = []int64{1234, 234, 456, utils.DummyINT64Value}

	event := DataAggregationEvent{

		stringFields:       stringFields,
		numericFields:      numericFields,
		aggregationColumns: aggregationColumns,
		indexableColumns:   indexableColumns,
		dataTypes:          dataTypes,
		storeType:          storeType,
		tick:               tick,
		plugin:             plugin,
		view:               view,
		interval:           interval,
		batchSize:          4,
	}

	aggregator := NewDataAggregator(0)

	aggregator.tokenizer = &utils.Tokenizer{

		Tokens: make([]string, utils.TokenizerLength),
	}

	for i := range aggregator.buffers {

		aggregator.buffers[i] = bytes.NewBuffer(make([]byte, utils.MaxBlobBytes))

		aggregator.buffers[i].Reset()
	}

	aggregator.aggregate(event)

	// again sending batch

	testDataTypes := make(map[string]codec.DataType)

	for key, value := range dataTypes {

		testDataTypes[key] = value
	}

	testDataTypes["fortinet.traffic.log.level"] = codec.String

	testDataTypes["fortinet.traffic.log.id"] = codec.Int64

	delete(numericFields, "fortinet.traffic.log.level")

	delete(numericFields, "fortinet.traffic.log.id")

	stringFields["fortinet.traffic.log.level"] = []string{"1", "2", "3", "4"}

	numericFields["fortinet.traffic.log.id"] = []int64{1, 2, 3, 4}

	aggregationEvent := DataAggregationEvent{

		stringFields:       stringFields,
		numericFields:      numericFields,
		aggregationColumns: aggregationColumns,
		indexableColumns:   indexableColumns,
		dataTypes:          testDataTypes,
		storeType:          storeType,
		tick:               tick,
		plugin:             plugin,
		view:               view,
		interval:           interval,
		batchSize:          4,
	}

	aggregator.aggregate(aggregationEvent)

	time.Sleep(time.Second * 3)

	bufferBytes, err := utils.ReadLogFile("Data Aggregator", "broker")

	assertions.NotNil(len(bufferBytes))

	assertions.Nil(err)

	assertions.True(len(aggregator.indexableColumns) > 0)

	assertions.True(len(aggregator.aggregationColumns) > 0)

}

func TestWriteEventAggregationBatchTypeMaxRecords2(t *testing.T) {

	utils.CleanUpStores()

	assertions := assert.New(t)

	_ = os.RemoveAll(utils.EventDir)

	utils.Create(utils.TempDir)

	utils.Create(utils.TempDir + utils.HyphenSeparator + utils.Aggregations)

	utils.Create(utils.EventDir + utils.PathSeparator + utils.VerticalFormat)

	utils.Create(utils.EventDir + utils.PathSeparator + utils.HorizontalFormat)

	utils.Create(utils.EventDir + utils.PathSeparator + utils.HorizontalFormat + utils.HyphenSeparator + utils.Aggregations)

	records := 5

	stringFields := make(map[string][]string)

	numericFields := make(map[string][]int64)

	dataTypes := make(map[string]codec.DataType)

	storeType := utils.Flow

	tick := utils.INT64ToStringValue(time.Now().Unix())

	plugin := "60005-fortinet.traffic"

	view := "60005-fortinet.traffic@@@1"

	interval := 5

	//indexable columns
	dataTypes[utils.EventSource] = codec.String

	dataTypes[utils.EventCategory] = codec.String

	dataTypes["fortinet.traffic.log.level"] = codec.Int64

	indexableColumns := []string{utils.EventSource, utils.EventCategory, "fortinet.traffic.log.level"}

	utils.SortStringValues(indexableColumns)

	//aggregated columns

	dataTypes["fortinet.traffic.volume.bytes"] = codec.Int64

	dataTypes["fortinet.traffic.source.ip"] = codec.String

	aggregationColumns := []string{"fortinet.traffic.volume.bytes", "fortinet.traffic.source.ip"}

	//string Columns

	for index := 0; index < records; index++ {

		stringFields[utils.EventSource] = append(stringFields[utils.EventSource], "10.20.40."+codec.INTToStringValue(index))

		stringFields["fortinet.traffic.source.ip"] = append(stringFields["fortinet.traffic.source.ip"], "172.16.40."+codec.INTToStringValue(index))

		stringFields[utils.EventCategory] = append(stringFields[utils.EventCategory], "fortinet.traffic")

	}

	//numeric fields

	for index := 0; index < records; index++ {

		numericFields["fortinet.traffic.volume.bytes"] = append(numericFields["fortinet.traffic.volume.bytes"], int64(rand.Intn(108)))

		numericFields["fortinet.traffic.log.level"] = append(numericFields["fortinet.traffic.log.level"], int64(rand.Intn(10)))
	}

	event := DataAggregationEvent{

		stringFields:       stringFields,
		numericFields:      numericFields,
		aggregationColumns: aggregationColumns,
		indexableColumns:   indexableColumns,
		dataTypes:          dataTypes,
		storeType:          storeType,
		tick:               tick,
		plugin:             plugin,
		view:               view,
		interval:           interval,
		batchSize:          records,
	}

	aggregator := NewDataAggregator(0)

	aggregator.tokenizer = &utils.Tokenizer{

		Tokens: make([]string, utils.TokenizerLength),
	}

	for i := range aggregator.buffers {

		aggregator.buffers[i] = bytes.NewBuffer(make([]byte, utils.MaxBlobBytes))

		aggregator.buffers[i].Reset()
	}

	aggregator.aggregate(event)

	time.Sleep(time.Second * 3)

	bufferBytes, err := utils.ReadLogFile("Data Aggregator", "broker")

	assertions.NotNil(len(bufferBytes))

	assertions.Nil(err)

	assertions.True(len(aggregator.indexableColumns) > 0)

	assertions.True(len(aggregator.aggregationColumns) > 0)

}

func TestDataAggregatorMove(t *testing.T) {

	assertions := assert.New(t)

	dataAggregatorFlushTimeSeconds = 1

	utils.CleanUpStores()

	_ = os.RemoveAll(utils.EventDir)

	utils.Create(utils.TempDir)

	utils.Create(utils.TempDir + utils.HyphenSeparator + utils.Aggregations)

	utils.Create(utils.EventDir + utils.PathSeparator + utils.VerticalFormat)

	utils.Create(utils.EventDir + utils.PathSeparator + utils.HorizontalFormat)

	utils.Create(utils.EventDir + utils.PathSeparator + utils.HorizontalFormat + utils.HyphenSeparator + utils.Aggregations)

	stringFields := make(map[string][]string)

	numericFields := make(map[string][]int64)

	dataTypes := make(map[string]codec.DataType)

	storeType := utils.Log

	tick := utils.INT64ToStringValue(time.Now().Unix())

	plugin := "60005-fortinet.traffic"

	view := "60005-fortinet.traffic@@@1"

	interval := 5

	//indexable columns
	dataTypes[utils.EventSource] = codec.String

	dataTypes[utils.EventCategory] = codec.String

	dataTypes["fortinet.traffic.log.level"] = codec.Int64

	indexableColumns := []string{utils.EventSource, utils.EventCategory, "fortinet.traffic.log.level"}

	utils.SortStringValues(indexableColumns)

	//aggregated columns

	dataTypes["fortinet.traffic.volume.bytes"] = codec.Int64

	dataTypes["fortinet.traffic.source.ip"] = codec.String

	aggregationColumns := []string{"fortinet.traffic.volume.bytes", "fortinet.traffic.source.ip"}

	//string Columns

	stringFields[utils.EventSource] = []string{"************", "************", "************", "************"}

	stringFields["fortinet.traffic.source.ip"] = []string{"************", "************", "************", "************"}

	stringFields[utils.EventCategory] = []string{"fortinet.traffic", "fortinet.traffic", "fortinet.traffic", "fortinet.traffic"}

	//numeric fields

	numericFields["fortinet.traffic.volume.bytes"] = []int64{1234, 234, 456, utils.DummyINT64Value}

	numericFields["fortinet.traffic.log.level"] = []int64{1, 2, utils.DummyINT64Value, utils.DummyINT64Value}

	event := DataAggregationEvent{

		stringFields:       stringFields,
		numericFields:      numericFields,
		aggregationColumns: aggregationColumns,
		indexableColumns:   indexableColumns,
		dataTypes:          dataTypes,
		storeType:          storeType,
		tick:               tick,
		plugin:             plugin,
		view:               view,
		interval:           interval,
		batchSize:          4,
	}

	aggregator := NewDataAggregator(0)

	aggregator.shutdown = true

	aggregator.Start()

	aggregator.tokenizer = &utils.Tokenizer{

		Tokens: make([]string, utils.TokenizerLength),
	}

	for i := range aggregator.buffers {

		aggregator.buffers[i] = bytes.NewBuffer(make([]byte, utils.MaxBlobBytes))

		aggregator.buffers[i].Reset()
	}

	aggregator.aggregate(event)

	assertions.True(len(aggregator.indexableColumns) > 0)

	assertions.True(len(aggregator.aggregationColumns) > 0)

	for key := range aggregator.trackers {

		err := aggregator.move(key, true)

		if err != nil {

			dataAggregatorLogger.Error(fmt.Sprintf("error %v occurred while writing/moving buffer for key %v in aggregator %v", err, key, aggregator.id))
		}
	}

	bufferBytes, err := utils.ReadLogFile("Data Aggregator", "broker")

	assertions.NotNil(len(bufferBytes))

	assertions.Nil(err)

	partitions, err := os.ReadDir(utils.EventDir + utils.PathSeparator + utils.HorizontalFormat + utils.HyphenSeparator + utils.Aggregations)

	assertions.NoError(err)

	assertions.Len(partitions, 1)

	for _, partition := range partitions {

		files, err := os.ReadDir(utils.EventDir + utils.PathSeparator + utils.HorizontalFormat + utils.HyphenSeparator + utils.Aggregations + utils.PathSeparator + partition.Name())

		assertions.NoError(err)

		assertions.Len(files, 1)

	}

}

func TestDatastoreAggregatorV1(t *testing.T) {

	assertions := assert.New(t)

	dataAggregator := NewDataAggregator(0)

	dataAggregator.aggregate(DataAggregationEvent{
		batchSize: 0,
	})

	dataAggregator.trackers = nil

	dataAggregator.Start()

	dataAggregator.Requests <- DataAggregationEvent{
		batchSize: 1,
	}

	time.Sleep(time.Second)

	utils.AssertLogMessage(assertions, "Data Aggregator", "broker", "!!!STACK TRACE for data aggregator")

}

func TestDatastoreAggregatorV2(t *testing.T) {

	assertions := assert.New(t)

	records := 5

	stringFields := make(map[string][]string)

	numericFields := make(map[string][]int64)

	dataTypes := make(map[string]codec.DataType)

	storeType := utils.Flow

	tick := utils.INT64ToStringValue(time.Now().Unix())

	plugin := "60005-fortinet.traffic"

	view := "60005-fortinet.traffic@@@1"

	interval := 5

	//indexable columns
	dataTypes[utils.EventSource] = codec.String

	dataTypes[utils.EventCategory] = codec.String

	dataTypes["fortinet.traffic.log.level"] = codec.Int64

	indexableColumns := []string{utils.EventSource, utils.EventCategory, "fortinet.traffic.log.level"}

	utils.SortStringValues(indexableColumns)

	//aggregated columns

	dataTypes["fortinet.traffic.volume.bytes"] = codec.Int64

	dataTypes["fortinet.traffic.source.ip"] = codec.String

	aggregationColumns := []string{"fortinet.traffic.volume.bytes", "fortinet.traffic.source.ip"}

	//string Columns

	for index := 0; index < records; index++ {

		stringFields[utils.EventSource] = append(stringFields[utils.EventSource], "10.20.40."+codec.INTToStringValue(index))

		stringFields["fortinet.traffic.source.ip"] = append(stringFields["fortinet.traffic.source.ip"], "172.16.40."+codec.INTToStringValue(index))

		stringFields[utils.EventCategory] = append(stringFields[utils.EventCategory], "fortinet.traffic")

	}

	//numeric fields

	for index := 0; index < records; index++ {

		numericFields["fortinet.traffic.volume.bytes"] = append(numericFields["fortinet.traffic.volume.bytes"], int64(rand.Intn(108)))

		numericFields["fortinet.traffic.log.level"] = append(numericFields["fortinet.traffic.log.level"], int64(rand.Intn(10)))
	}

	event := DataAggregationEvent{

		stringFields:       stringFields,
		numericFields:      numericFields,
		aggregationColumns: aggregationColumns,
		indexableColumns:   indexableColumns,
		dataTypes:          dataTypes,
		storeType:          storeType,
		tick:               tick,
		plugin:             plugin,
		view:               view,
		interval:           interval,
		batchSize:          records,
	}

	aggregator := NewDataAggregator(0)

	aggregator.tokenizer = &utils.Tokenizer{

		Tokens: make([]string, utils.TokenizerLength),
	}

	for i := range aggregator.buffers {

		aggregator.buffers[i] = bytes.NewBuffer(make([]byte, utils.MaxBlobBytes))

		aggregator.buffers[i].Reset()
	}

	os.RemoveAll(tempDir)

	os.Create(tempDir)

	aggregator.aggregate(event)

	utils.GlobalShutdown = true

	aggregator.trackers["dummy"] = 0

	aggregator.valuesByOrdinal["dummy"] = swiss.NewMap[int32, string](0)

	aggregator.ordinalsByValue["dummy"] = swiss.NewMap[string, int32](0)

	aggregator.aggregations["dummy"] = swiss.NewMap[string, int64](0)

	aggregator.entries = make([]string, 1)

	aggregator.flush()

	utils.GlobalShutdown = false

	utils.MaxDataAggregationGroups = 5

	indexableColumns = []string{utils.EventSource, utils.EventCategory, "fortinet.traffic.log.level"}

	aggregationColumns = []string{"fortinet.traffic.volume.bytes", "fortinet.traffic.source.ip"}

	event = DataAggregationEvent{

		stringFields:       stringFields,
		numericFields:      numericFields,
		aggregationColumns: aggregationColumns,
		indexableColumns:   indexableColumns,
		dataTypes:          dataTypes,
		storeType:          storeType,
		tick:               tick,
		plugin:             plugin,
		view:               view,
		interval:           interval,
		batchSize:          records,
	}

	aggregator.aggregate(event)

	os.RemoveAll(tempDir)

	os.MkdirAll(tempDir, 0755)

	utils.AssertLogMessage(assertions, "Data Aggregator", "broker", "occurred while writing/moving buffer for key")

}
