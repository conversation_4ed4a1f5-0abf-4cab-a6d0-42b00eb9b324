/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/* Change Logs:
* Date                   Author          		Notes
* 2025-03-05			 <PERSON><PERSON><PERSON> Shah			Motadata-5190  Added sonarignore to allow snake case naming and added nested comment for unimplemented functions
 */

/*
 * Package bp128 implements the Binary Packing 128 (BP128) algorithm for efficient integer compression.
 *
 * BP128 is a SIMD-optimized compression technique that packs integers using the minimum number of bits
 * required to represent them. It works by:
 * 1. Grouping integers into blocks of 128 values
 * 2. Determining the minimum number of bits needed to represent all values in the block
 * 3. Packing the values using exactly that many bits per value
 * 4. Optionally applying delta encoding for better compression of sorted or slowly changing values
 *
 * This file contains function declarations for unpacking integers that have been compressed using BP128.
 * The actual implementations are provided in architecture-specific assembly files for maximum performance.
 *
 * The naming convention for functions is:
 * - unpack[32|64]_N: Unpacks values that were packed using N bits each
 * - dunpack[32|64]_N: Unpacks delta-encoded values that were packed using N bits each
 *
 * The BP128 algorithm achieves high performance through:
 * - Vectorized operations using SIMD instructions
 * - Minimal branching in the critical path
 * - Cache-friendly memory access patterns
 * - Bit-level parallelism
 */

// sonarignore - This directive is used to ignore snake_case naming convention warnings
package bp128

// unpack32_0 unpacks a block of 128 integers that were packed using 0 bits each.
// Since 0 bits means all values are 0, this function simply writes zeros to the output.
//
// Parameters:
//   - in: Pointer to the input buffer containing packed data (not used in this function)
//   - out: Pointer to the output buffer where unpacked integers will be written
//   - offset: Offset in the output buffer (in 4-byte integers)
//   - seed: Pointer to a seed value for differential coding (not used in this function)
//
// This is a reference function that serves as a template for the other unpack functions.
// The actual implementation is in architecture-specific assembly files.
func unpack32_0(in *byte, out uintptr, offset int, seed *byte) {
	//this is a reference function
}

// The following functions unpack blocks of 128 integers that were packed using N bits each,
// where N is the number in the function name (1-32).
//
// Each function has the same parameters:
//   - in: Pointer to the input buffer containing packed data
//   - out: Pointer to the output buffer where unpacked integers will be written
//   - offset: Offset in the output buffer (in 4-byte integers)
//   - seed: Pointer to a seed value for differential coding (used only in certain implementations)
//
// The actual implementations are in architecture-specific assembly files for maximum performance.
// Each function is optimized for its specific bit width to achieve the best possible throughput.

func unpack32_1(in *byte, out uintptr, offset int, seed *byte)
func unpack32_2(in *byte, out uintptr, offset int, seed *byte)
func unpack32_3(in *byte, out uintptr, offset int, seed *byte)
func unpack32_4(in *byte, out uintptr, offset int, seed *byte)
func unpack32_5(in *byte, out uintptr, offset int, seed *byte)
func unpack32_6(in *byte, out uintptr, offset int, seed *byte)
func unpack32_7(in *byte, out uintptr, offset int, seed *byte)
func unpack32_8(in *byte, out uintptr, offset int, seed *byte)
func unpack32_9(in *byte, out uintptr, offset int, seed *byte)
func unpack32_10(in *byte, out uintptr, offset int, seed *byte)
func unpack32_11(in *byte, out uintptr, offset int, seed *byte)
func unpack32_12(in *byte, out uintptr, offset int, seed *byte)
func unpack32_13(in *byte, out uintptr, offset int, seed *byte)
func unpack32_14(in *byte, out uintptr, offset int, seed *byte)
func unpack32_15(in *byte, out uintptr, offset int, seed *byte)
func unpack32_16(in *byte, out uintptr, offset int, seed *byte)
func unpack32_17(in *byte, out uintptr, offset int, seed *byte)
func unpack32_18(in *byte, out uintptr, offset int, seed *byte)
func unpack32_19(in *byte, out uintptr, offset int, seed *byte)
func unpack32_20(in *byte, out uintptr, offset int, seed *byte)
func unpack32_21(in *byte, out uintptr, offset int, seed *byte)
func unpack32_22(in *byte, out uintptr, offset int, seed *byte)
func unpack32_23(in *byte, out uintptr, offset int, seed *byte)
func unpack32_24(in *byte, out uintptr, offset int, seed *byte)
func unpack32_25(in *byte, out uintptr, offset int, seed *byte)
func unpack32_26(in *byte, out uintptr, offset int, seed *byte)
func unpack32_27(in *byte, out uintptr, offset int, seed *byte)
func unpack32_28(in *byte, out uintptr, offset int, seed *byte)
func unpack32_29(in *byte, out uintptr, offset int, seed *byte)
func unpack32_30(in *byte, out uintptr, offset int, seed *byte)
func unpack32_31(in *byte, out uintptr, offset int, seed *byte)
func unpack32_32(in *byte, out uintptr, offset int, seed *byte)

// unpack64_0 unpacks a block of 128 integers that were packed using 0 bits each.
// Since 0 bits means all values are 0, this function simply writes zeros to the output.
// This version works with 64-bit integers.
//
// Parameters:
//   - in: Pointer to the input buffer containing packed data (not used in this function)
//   - out: Pointer to the output buffer where unpacked integers will be written
//   - offset: Offset in the output buffer (in 8-byte integers)
//   - seed: Pointer to a seed value for differential coding (not used in this function)
//
// This is a reference function that serves as a template for the other unpack64 functions.
// The actual implementation is in architecture-specific assembly files.
func unpack64_0(in *byte, out uintptr, offset int, seed *byte) {
	//this is a reference function
}

// The following functions unpack blocks of 128 integers that were packed using N bits each,
// where N is the number in the function name (1-64).
// These functions work with 64-bit integers, allowing for larger values than the unpack32 functions.
//
// Each function has the same parameters:
//   - in: Pointer to the input buffer containing packed data
//   - out: Pointer to the output buffer where unpacked integers will be written
//   - offset: Offset in the output buffer (in 8-byte integers)
//   - seed: Pointer to a seed value for differential coding (used only in certain implementations)
//
// The actual implementations are in architecture-specific assembly files for maximum performance.
// Each function is optimized for its specific bit width to achieve the best possible throughput.

func unpack64_1(in *byte, out uintptr, offset int, seed *byte)
func unpack64_2(in *byte, out uintptr, offset int, seed *byte)
func unpack64_3(in *byte, out uintptr, offset int, seed *byte)
func unpack64_4(in *byte, out uintptr, offset int, seed *byte)
func unpack64_5(in *byte, out uintptr, offset int, seed *byte)
func unpack64_6(in *byte, out uintptr, offset int, seed *byte)
func unpack64_7(in *byte, out uintptr, offset int, seed *byte)
func unpack64_8(in *byte, out uintptr, offset int, seed *byte)
func unpack64_9(in *byte, out uintptr, offset int, seed *byte)
func unpack64_10(in *byte, out uintptr, offset int, seed *byte)
func unpack64_11(in *byte, out uintptr, offset int, seed *byte)
func unpack64_12(in *byte, out uintptr, offset int, seed *byte)
func unpack64_13(in *byte, out uintptr, offset int, seed *byte)
func unpack64_14(in *byte, out uintptr, offset int, seed *byte)
func unpack64_15(in *byte, out uintptr, offset int, seed *byte)
func unpack64_16(in *byte, out uintptr, offset int, seed *byte)
func unpack64_17(in *byte, out uintptr, offset int, seed *byte)
func unpack64_18(in *byte, out uintptr, offset int, seed *byte)
func unpack64_19(in *byte, out uintptr, offset int, seed *byte)
func unpack64_20(in *byte, out uintptr, offset int, seed *byte)
func unpack64_21(in *byte, out uintptr, offset int, seed *byte)
func unpack64_22(in *byte, out uintptr, offset int, seed *byte)
func unpack64_23(in *byte, out uintptr, offset int, seed *byte)
func unpack64_24(in *byte, out uintptr, offset int, seed *byte)
func unpack64_25(in *byte, out uintptr, offset int, seed *byte)
func unpack64_26(in *byte, out uintptr, offset int, seed *byte)
func unpack64_27(in *byte, out uintptr, offset int, seed *byte)
func unpack64_28(in *byte, out uintptr, offset int, seed *byte)
func unpack64_29(in *byte, out uintptr, offset int, seed *byte)
func unpack64_30(in *byte, out uintptr, offset int, seed *byte)
func unpack64_31(in *byte, out uintptr, offset int, seed *byte)
func unpack64_32(in *byte, out uintptr, offset int, seed *byte)
func unpack64_33(in *byte, out uintptr, offset int, seed *byte)
func unpack64_34(in *byte, out uintptr, offset int, seed *byte)
func unpack64_35(in *byte, out uintptr, offset int, seed *byte)
func unpack64_36(in *byte, out uintptr, offset int, seed *byte)
func unpack64_37(in *byte, out uintptr, offset int, seed *byte)
func unpack64_38(in *byte, out uintptr, offset int, seed *byte)
func unpack64_39(in *byte, out uintptr, offset int, seed *byte)
func unpack64_40(in *byte, out uintptr, offset int, seed *byte)
func unpack64_41(in *byte, out uintptr, offset int, seed *byte)
func unpack64_42(in *byte, out uintptr, offset int, seed *byte)
func unpack64_43(in *byte, out uintptr, offset int, seed *byte)
func unpack64_44(in *byte, out uintptr, offset int, seed *byte)
func unpack64_45(in *byte, out uintptr, offset int, seed *byte)
func unpack64_46(in *byte, out uintptr, offset int, seed *byte)
func unpack64_47(in *byte, out uintptr, offset int, seed *byte)
func unpack64_48(in *byte, out uintptr, offset int, seed *byte)
func unpack64_49(in *byte, out uintptr, offset int, seed *byte)
func unpack64_50(in *byte, out uintptr, offset int, seed *byte)
func unpack64_51(in *byte, out uintptr, offset int, seed *byte)
func unpack64_52(in *byte, out uintptr, offset int, seed *byte)
func unpack64_53(in *byte, out uintptr, offset int, seed *byte)
func unpack64_54(in *byte, out uintptr, offset int, seed *byte)
func unpack64_55(in *byte, out uintptr, offset int, seed *byte)
func unpack64_56(in *byte, out uintptr, offset int, seed *byte)
func unpack64_57(in *byte, out uintptr, offset int, seed *byte)
func unpack64_58(in *byte, out uintptr, offset int, seed *byte)
func unpack64_59(in *byte, out uintptr, offset int, seed *byte)
func unpack64_60(in *byte, out uintptr, offset int, seed *byte)
func unpack64_61(in *byte, out uintptr, offset int, seed *byte)
func unpack64_62(in *byte, out uintptr, offset int, seed *byte)
func unpack64_63(in *byte, out uintptr, offset int, seed *byte)
func unpack64_64(in *byte, out uintptr, offset int, seed *byte)

// dunpack32_0 unpacks a block of 128 delta-encoded integers that were packed using 0 bits each.
// Since 0 bits means all values are 0, this function simply writes the seed value to the output.
// The "d" prefix indicates delta encoding, where each value is the difference from the previous value.
//
// Parameters:
//   - in: Pointer to the input buffer containing packed data (not used in this function)
//   - out: Pointer to the output buffer where unpacked integers will be written
//   - offset: Offset in the output buffer (in 4-byte integers)
//   - seed: Pointer to a seed value that is used as the starting point for delta decoding
//
// This is a reference function that serves as a template for the other dunpack functions.
// The actual implementation is in architecture-specific assembly files.
func dunpack32_0(in *byte, out uintptr, offset int, seed *byte) {
	//this is a reference function
}

// The following functions unpack blocks of 128 delta-encoded integers that were packed using N bits each,
// where N is the number in the function name (1-32).
// Delta encoding stores differences between consecutive values rather than the values themselves,
// which often results in smaller numbers that can be packed with fewer bits.
//
// Each function has the same parameters:
//   - in: Pointer to the input buffer containing packed data
//   - out: Pointer to the output buffer where unpacked integers will be written
//   - offset: Offset in the output buffer (in 4-byte integers)
//   - seed: Pointer to a seed value that is used as the starting point for delta decoding
//
// The actual implementations are in architecture-specific assembly files for maximum performance.
// Each function is optimized for its specific bit width to achieve the best possible throughput.

func dunpack32_1(in *byte, out uintptr, offset int, seed *byte)
func dunpack32_2(in *byte, out uintptr, offset int, seed *byte)
func dunpack32_3(in *byte, out uintptr, offset int, seed *byte)
func dunpack32_4(in *byte, out uintptr, offset int, seed *byte)
func dunpack32_5(in *byte, out uintptr, offset int, seed *byte)
func dunpack32_6(in *byte, out uintptr, offset int, seed *byte)
func dunpack32_7(in *byte, out uintptr, offset int, seed *byte)
func dunpack32_8(in *byte, out uintptr, offset int, seed *byte)
func dunpack32_9(in *byte, out uintptr, offset int, seed *byte)
func dunpack32_10(in *byte, out uintptr, offset int, seed *byte)
func dunpack32_11(in *byte, out uintptr, offset int, seed *byte)
func dunpack32_12(in *byte, out uintptr, offset int, seed *byte)
func dunpack32_13(in *byte, out uintptr, offset int, seed *byte)
func dunpack32_14(in *byte, out uintptr, offset int, seed *byte)
func dunpack32_15(in *byte, out uintptr, offset int, seed *byte)
func dunpack32_16(in *byte, out uintptr, offset int, seed *byte)
func dunpack32_17(in *byte, out uintptr, offset int, seed *byte)
func dunpack32_18(in *byte, out uintptr, offset int, seed *byte)
func dunpack32_19(in *byte, out uintptr, offset int, seed *byte)
func dunpack32_20(in *byte, out uintptr, offset int, seed *byte)
func dunpack32_21(in *byte, out uintptr, offset int, seed *byte)
func dunpack32_22(in *byte, out uintptr, offset int, seed *byte)
func dunpack32_23(in *byte, out uintptr, offset int, seed *byte)
func dunpack32_24(in *byte, out uintptr, offset int, seed *byte)
func dunpack32_25(in *byte, out uintptr, offset int, seed *byte)
func dunpack32_26(in *byte, out uintptr, offset int, seed *byte)
func dunpack32_27(in *byte, out uintptr, offset int, seed *byte)
func dunpack32_28(in *byte, out uintptr, offset int, seed *byte)
func dunpack32_29(in *byte, out uintptr, offset int, seed *byte)
func dunpack32_30(in *byte, out uintptr, offset int, seed *byte)
func dunpack32_31(in *byte, out uintptr, offset int, seed *byte)
func dunpack32_32(in *byte, out uintptr, offset int, seed *byte)

// dunpack64_0 unpacks a block of 128 delta-encoded integers that were packed using 0 bits each.
// Since 0 bits means all values are 0, this function simply writes the seed value to the output.
// This version works with 64-bit integers, allowing for larger values than the dunpack32 functions.
// The "d" prefix indicates delta encoding, where each value is the difference from the previous value.
//
// Parameters:
//   - in: Pointer to the input buffer containing packed data (not used in this function)
//   - out: Pointer to the output buffer where unpacked integers will be written
//   - offset: Offset in the output buffer (in 8-byte integers)
//   - seed: Pointer to a seed value that is used as the starting point for delta decoding
//
// This is a reference function that serves as a template for the other dunpack64 functions.
// The actual implementation is in architecture-specific assembly files.
func dunpack64_0(in *byte, out uintptr, offset int, seed *byte) {
	//this is a reference function
}

// The following functions unpack blocks of 128 delta-encoded integers that were packed using N bits each,
// where N is the number in the function name (1-64).
// These functions work with 64-bit integers, allowing for larger values than the dunpack32 functions.
// Delta encoding stores differences between consecutive values rather than the values themselves,
// which often results in smaller numbers that can be packed with fewer bits.
//
// Each function has the same parameters:
//   - in: Pointer to the input buffer containing packed data
//   - out: Pointer to the output buffer where unpacked integers will be written
//   - offset: Offset in the output buffer (in 8-byte integers)
//   - seed: Pointer to a seed value that is used as the starting point for delta decoding
//
// The actual implementations are in architecture-specific assembly files for maximum performance.
// Each function is optimized for its specific bit width to achieve the best possible throughput.

func dunpack64_1(in *byte, out uintptr, offset int, seed *byte)
func dunpack64_2(in *byte, out uintptr, offset int, seed *byte)
func dunpack64_3(in *byte, out uintptr, offset int, seed *byte)
func dunpack64_4(in *byte, out uintptr, offset int, seed *byte)
func dunpack64_5(in *byte, out uintptr, offset int, seed *byte)
func dunpack64_6(in *byte, out uintptr, offset int, seed *byte)
func dunpack64_7(in *byte, out uintptr, offset int, seed *byte)
func dunpack64_8(in *byte, out uintptr, offset int, seed *byte)
func dunpack64_9(in *byte, out uintptr, offset int, seed *byte)
func dunpack64_10(in *byte, out uintptr, offset int, seed *byte)
func dunpack64_11(in *byte, out uintptr, offset int, seed *byte)
func dunpack64_12(in *byte, out uintptr, offset int, seed *byte)
func dunpack64_13(in *byte, out uintptr, offset int, seed *byte)
func dunpack64_14(in *byte, out uintptr, offset int, seed *byte)
func dunpack64_15(in *byte, out uintptr, offset int, seed *byte)
func dunpack64_16(in *byte, out uintptr, offset int, seed *byte)
func dunpack64_17(in *byte, out uintptr, offset int, seed *byte)
func dunpack64_18(in *byte, out uintptr, offset int, seed *byte)
func dunpack64_19(in *byte, out uintptr, offset int, seed *byte)
func dunpack64_20(in *byte, out uintptr, offset int, seed *byte)
func dunpack64_21(in *byte, out uintptr, offset int, seed *byte)
func dunpack64_22(in *byte, out uintptr, offset int, seed *byte)
func dunpack64_23(in *byte, out uintptr, offset int, seed *byte)
func dunpack64_24(in *byte, out uintptr, offset int, seed *byte)
func dunpack64_25(in *byte, out uintptr, offset int, seed *byte)
func dunpack64_26(in *byte, out uintptr, offset int, seed *byte)
func dunpack64_27(in *byte, out uintptr, offset int, seed *byte)
func dunpack64_28(in *byte, out uintptr, offset int, seed *byte)
func dunpack64_29(in *byte, out uintptr, offset int, seed *byte)
func dunpack64_30(in *byte, out uintptr, offset int, seed *byte)
func dunpack64_31(in *byte, out uintptr, offset int, seed *byte)
func dunpack64_32(in *byte, out uintptr, offset int, seed *byte)
func dunpack64_33(in *byte, out uintptr, offset int, seed *byte)
func dunpack64_34(in *byte, out uintptr, offset int, seed *byte)
func dunpack64_35(in *byte, out uintptr, offset int, seed *byte)
func dunpack64_36(in *byte, out uintptr, offset int, seed *byte)
func dunpack64_37(in *byte, out uintptr, offset int, seed *byte)
func dunpack64_38(in *byte, out uintptr, offset int, seed *byte)
func dunpack64_39(in *byte, out uintptr, offset int, seed *byte)
func dunpack64_40(in *byte, out uintptr, offset int, seed *byte)
func dunpack64_41(in *byte, out uintptr, offset int, seed *byte)
func dunpack64_42(in *byte, out uintptr, offset int, seed *byte)
func dunpack64_43(in *byte, out uintptr, offset int, seed *byte)
func dunpack64_44(in *byte, out uintptr, offset int, seed *byte)
func dunpack64_45(in *byte, out uintptr, offset int, seed *byte)
func dunpack64_46(in *byte, out uintptr, offset int, seed *byte)
func dunpack64_47(in *byte, out uintptr, offset int, seed *byte)
func dunpack64_48(in *byte, out uintptr, offset int, seed *byte)
func dunpack64_49(in *byte, out uintptr, offset int, seed *byte)
func dunpack64_50(in *byte, out uintptr, offset int, seed *byte)
func dunpack64_51(in *byte, out uintptr, offset int, seed *byte)
func dunpack64_52(in *byte, out uintptr, offset int, seed *byte)
func dunpack64_53(in *byte, out uintptr, offset int, seed *byte)
func dunpack64_54(in *byte, out uintptr, offset int, seed *byte)
func dunpack64_55(in *byte, out uintptr, offset int, seed *byte)
func dunpack64_56(in *byte, out uintptr, offset int, seed *byte)
func dunpack64_57(in *byte, out uintptr, offset int, seed *byte)
func dunpack64_58(in *byte, out uintptr, offset int, seed *byte)
func dunpack64_59(in *byte, out uintptr, offset int, seed *byte)
func dunpack64_60(in *byte, out uintptr, offset int, seed *byte)
func dunpack64_61(in *byte, out uintptr, offset int, seed *byte)
func dunpack64_62(in *byte, out uintptr, offset int, seed *byte)
func dunpack64_63(in *byte, out uintptr, offset int, seed *byte)
func dunpack64_64(in *byte, out uintptr, offset int, seed *byte)
