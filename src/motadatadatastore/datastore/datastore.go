/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/* Change Logs:
* Date                   Author          		Notes
* 2025-03-05			 <PERSON><PERSON><PERSON>			Motadata-5190  Updated Constants according to SonarQube standard
* 2025-03-05             Vedant Dokania         Motadata-5451  Status Flap New datastore type introduced and new map for resolving ordinals
* 2025-03-21			 <PERSON>haval <PERSON>ra			<PERSON>ata-5452  Added NetRouteMetric Plugins And NetRouteMetric DatastoreType in GetDatastoreType
* 2025-04-02			 <PERSON><PERSON><PERSON>-4859  Trimmed Blob Values if having More the MaxBlobBytes size
* 2025-04-09			 <PERSON><PERSON><PERSON>			Motadata-4859  Refactoring "fmt.Sprintf" to update go version to 1.24
* 2025-05-02             Vedant Dokania         Motadata-6080  Instead of removing whole plugin from indexable , remove particular columns from indexable
* 2025-05-05			 Swapnil A. Dave		MOTADATA-6078 passed defaultblobpool const as the function parameter in memory pool initialization
* 2025-05-08  			 Hardik Vala			MOTADATA-6073 Updated trim behaviour for Encoding String Values, discard default indexer column
* 2025-05-10             Dhaval Bera            MOTADATA-6100  Added Unmap function to release memory-mapped resources

 */

package datastore

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/kelindar/bitmap"
	cmap "github.com/orcaman/concurrent-map"
	cp "github.com/otiai10/copy"
	"math"
	. "motadatadatastore/codec"
	. "motadatadatastore/storage"
	"motadatadatastore/utils"
	"os"
	"runtime"
	"strings"
	"sync"
	"time"
)

type BlobEvent struct {
	Store *Store

	WaitGroup *sync.WaitGroup

	Tokenizer *utils.Tokenizer

	DiskIOEvent DiskIOEvent

	Decoder Decoder

	Encoder Encoder

	Values []string

	KeyBytes, ValueBytes []byte

	Padding int

	Encoding Encoding
}

// plugins
const (
	NetRouteStatusPlugin string = "500024-netroute.availability"

	NetRouteMetricPlugin string = "500025-netroute.availability"

	NetRouteEventPlugin string = "500026-netroute.availability"

	EventSearchPlugin string = "499999-event.history"

	MetricPolicyFlapPlugin string = "499998-policy.flap"

	FlowPlugin string = "500000-flow"

	TrapPolicyPlugin string = "500010-policy"

	TrapFlapPlugin string = "500001-trap.flap"

	LogStatPlugin string = "500011-log.stat"

	FlowStatPlugin string = "500018-flow"

	RunbookWorklogPlugin string = "500022-runbook.worklog"

	CompliancePlugin string = "500023-compliance"

	CacheStatPlugin int = 500015

	QueryStatPlugin int = 500016
)
const (

	//metric related common const

	Monitor = "monitor"

	NetRoute = "netroute"

	Instance = "instance"

	InstanceType = "instance.type"

	Duration = "duration"

	Status = "status"

	Reason = "reason"

	Timestamp = "timestamp"

	Time = "time"

	Mappings = "mappings"

	FlowTopColumn = "volume.bytes^sum"

	IntegerColumn = 1

	FloatingColumn = 2

	StringColumn = 0

	//event related common const

	HorizontalStore = "1"

	VerticalStore = "0"

	DefaultKeyPart = "^0"

	Object = "object"

	Objects = "objects"

	DatastoreType = "datastore.type"
)

const ( //availability constants

	UpTime = "uptime"

	DownTime = "downtime"

	UnknownTime = "unknowntime"

	SuspendTime = "suspendtime"

	DisableTime = "disabletime"

	UnreachableTime = "unreachabletime"

	MaintenanceTime = "maintenancetime"

	//
	Up = "UP"

	Down = "DOWN"

	Unknown = "UNKNOWN"

	Suspend = "SUSPEND"

	Disable = "DISABLE"

	Unreachable = "UNREACHABLE"

	Maintenance = "MAINTENANCE"

	SuffixPercent = ".percent"

	ObjectStatusFlapHistory = "status.flap.history"
)

var dataStoreLogger = utils.NewLogger("Datastore", "system")

// common maps used in the database
var (
	stores cmap.ConcurrentMap // store particular store object {"store.name": Store{}}

	floatingColumns cmap.ConcurrentMap // checks that if column is qualified in floating columns then store that value in floating data type

	garbageColumns cmap.ConcurrentMap // checks that if values is numeric and column is qualified in garbage columns then store that value in string to maintain the integrity between system defined and user defined plugin values

	indexablePlugins cmap.ConcurrentMap //

	invalidIndexableColumns cmap.ConcurrentMap

	verticalAggregations cmap.ConcurrentMap

	horizontalAggregations cmap.ConcurrentMap

	searchableColumns cmap.ConcurrentMap

	blobColumns cmap.ConcurrentMap

	aggregationContexts map[string]bitmap.Bitmap

	// key - pluginId + group separator + column
	horizontalAggregationFields map[string]struct{}

	encoders map[string]Encoding // encoding type column wise

	stopWords map[string]struct{}

	verticalAggregationConfigs utils.MotadataMap

	horizontalAggregationConfigs utils.MotadataMap

	shadowCounters map[string]string

	alteredShadowCounters map[string]string

	configs utils.MotadataMap

	StatusFlapOrdinals map[string]int

	ResolvedStatusFlapOrdinals map[int]string

	LogDefaultColumns = utils.MotadataMap{
		utils.EventSource:     utils.Empty,
		utils.EventSourceType: utils.Empty,
		utils.EventPatternId:  utils.Empty,
		utils.EventCategory:   utils.Empty,
		utils.EventSeverity:   utils.Empty,
	}
)

var (
	lock = sync.Mutex{}

	rwLock = sync.RWMutex{}
)

var dataStoreEncoder Encoder

var configDir = utils.CurrentDir + utils.PathSeparator + utils.ConfigDir

func Init() {

	pool := utils.NewMemoryPool(3, utils.MaxPoolLength, true, utils.DefaultBlobPools)

	dataStoreEncoder = NewEncoder(pool) //do not use anywhere else

	stores = cmap.New()

	verticalAggregations = cmap.New()

	indexablePlugins = cmap.New()

	invalidIndexableColumns = cmap.New()

	searchableColumns = cmap.New()

	blobColumns = cmap.New()

	floatingColumns = cmap.New()

	garbageColumns = cmap.New()

	horizontalAggregations = cmap.New()

	configs = make(utils.MotadataMap)

	populateStores()

	populateStopWords()

	populateConfigs()
}

func populateConfigs() {

	if _, err := os.Stat(configDir + utils.PathSeparator + utils.TempPatch + utils.ColumnConfigFile); err == nil {

		_ = os.Remove(configDir + utils.PathSeparator + utils.TempPatch + utils.ColumnConfigFile)
	}

	bytes, err := os.ReadFile(configDir + utils.PathSeparator + utils.ColumnConfigFile)

	if err == nil {

		_ = json.Unmarshal(bytes, &configs)

	} else {

		configs = make(utils.MotadataMap)

		dataStoreLogger.Error(fmt.Sprintf(utils.ErrorLocateFile, utils.ColumnConfigFile))
	}

	floatingColumns = configs.GetMapValue(utils.FloatingColumns).ToConcurrentMap()

	garbageColumns = configs.GetMapValue(utils.GarbageColumns).ToConcurrentMap()

	searchableColumns = configs.GetMapValue(utils.SearchableColumns).ToConcurrentMap()

	blobColumns = configs.GetMapValue(utils.BlobColumns).ToConcurrentMap()

	indexablePlugins = configs.GetMapValue(utils.IndexableColumns).ToConcurrentMap()

	/*
		add more here if any more of horizontal data is written vertically
	*/

	StatusFlapOrdinals = make(map[string]int)

	StatusFlapOrdinals[Up] = 1

	StatusFlapOrdinals[Down] = 2

	StatusFlapOrdinals[Unknown] = 3

	StatusFlapOrdinals[Suspend] = 4

	StatusFlapOrdinals[Disable] = 5

	StatusFlapOrdinals[Unreachable] = 6

	StatusFlapOrdinals[Maintenance] = 7

	ResolvedStatusFlapOrdinals = make(map[int]string)

	ResolvedStatusFlapOrdinals[1] = Up

	ResolvedStatusFlapOrdinals[2] = Down

	ResolvedStatusFlapOrdinals[3] = Unknown

	ResolvedStatusFlapOrdinals[4] = Suspend

	ResolvedStatusFlapOrdinals[5] = Disable

	ResolvedStatusFlapOrdinals[6] = Unreachable

	ResolvedStatusFlapOrdinals[7] = Maintenance

	horizontalAggregationFields = map[string]struct{}{

		"500001-trap###trap.enterprise":    {},
		"500001-trap###trap.enterprise.id": {},
		"500001-trap###trap.oid":           {},
		"500001-trap###trap.name":          {},
		"500001-trap###trap.version":       {},
		"500001-trap###trap.severity":      {},
		"500001-trap###trap.vendor":        {},
		"500001-trap###trap.message":       {},
		"500001-trap###trap.raw.message":   {},
		"500005-policy###message":          {},
		"500005-policy###severity":         {},
		"500005-policy###event.field":      {},
		"500005-policy###id":               {},
		"500004-policy###event.field":      {},
		"500004-policy###message":          {},
		"500004-policy###severity":         {},
		"500004-policy###id":               {},
		"500010-policy###message":          {},
		"500010-policy###severity":         {},
		"500010-policy###id":               {},
	}

	shadowCounters = map[string]string{

		"interface~traffic.bits.per.sec":     "interface~traffic.bytes.per.sec",
		"interface~in.traffic.bits.per.sec":  "interface~in.traffic.bytes.per.sec",
		"interface~out.traffic.bits.per.sec": "interface~out.traffic.bytes.per.sec",
		"interface~speed.bits.per.sec":       "interface~speed.bytes.per.sec",
	}

	//used to replace the columns in packing , sorting column and post filter
	alteredShadowCounters = map[string]string{

		"interface~traffic.bytes.per.sec^min":   "interface~traffic.bits.per.sec^min",
		"interface~traffic.bytes.per.sec^max":   "interface~traffic.bits.per.sec^max",
		"interface~traffic.bytes.per.sec^sum":   "interface~traffic.bits.per.sec^sum",
		"interface~traffic.bytes.per.sec^count": "interface~traffic.bits.per.sec^count",
		"interface~traffic.bytes.per.sec^last":  "interface~traffic.bits.per.sec^last",
		"interface~traffic.bytes.per.sec^avg":   "interface~traffic.bits.per.sec^avg",

		"interface~in.traffic.bytes.per.sec^min":   "interface~in.traffic.bits.per.sec^min",
		"interface~in.traffic.bytes.per.sec^max":   "interface~in.traffic.bits.per.sec^max",
		"interface~in.traffic.bytes.per.sec^sum":   "interface~in.traffic.bits.per.sec^sum",
		"interface~in.traffic.bytes.per.sec^count": "interface~in.traffic.bits.per.sec^count",
		"interface~in.traffic.bytes.per.sec^last":  "interface~in.traffic.bits.per.sec^last",
		"interface~in.traffic.bytes.per.sec^avg":   "interface~in.traffic.bits.per.sec^avg",

		"interface~out.traffic.bytes.per.sec^min":   "interface~out.traffic.bits.per.sec^min",
		"interface~out.traffic.bytes.per.sec^max":   "interface~out.traffic.bits.per.sec^max",
		"interface~out.traffic.bytes.per.sec^sum":   "interface~out.traffic.bits.per.sec^sum",
		"interface~out.traffic.bytes.per.sec^count": "interface~out.traffic.bits.per.sec^count",
		"interface~out.traffic.bytes.per.sec^last":  "interface~out.traffic.bits.per.sec^last",
		"interface~out.traffic.bytes.per.sec^avg":   "interface~out.traffic.bits.per.sec^avg",

		"interface~speed.bytes.per.sec^min":   "interface~speed.bits.per.sec^min",
		"interface~speed.bytes.per.sec^max":   "interface~speed.bits.per.sec^max",
		"interface~speed.bytes.per.sec^sum":   "interface~speed.bits.per.sec^sum",
		"interface~speed.bytes.per.sec^count": "interface~speed.bits.per.sec^count",
		"interface~speed.bytes.per.sec^last":  "interface~speed.bits.per.sec^last",
		"interface~speed.bytes.per.sec^avg":   "interface~speed.bits.per.sec^avg",
	}

	encoders = map[string]Encoding{}

	for column, encoding := range configs.GetMapValue(utils.ColumnEncoders) {

		encoders[column] = Encoding(encoding.(float64))
	}

	bytes, err = os.ReadFile(configDir + utils.PathSeparator + utils.VerticalAggregations)

	if err == nil {

		_ = json.Unmarshal(bytes, &verticalAggregationConfigs)

		verticalAggregations = verticalAggregationConfigs.ToConcurrentMap()

	} else {

		verticalAggregationConfigs = make(utils.MotadataMap)

		dataStoreLogger.Error(fmt.Sprintf(utils.ErrorLocateFile, utils.VerticalAggregations))
	}

	if _, err = os.Stat(configDir + utils.PathSeparator + utils.TempPatch + utils.HorizontalAggregations); err == nil {

		_ = os.Remove(configDir + utils.PathSeparator + utils.TempPatch + utils.HorizontalAggregations)
	}

	bytes, err = os.ReadFile(configDir + utils.PathSeparator + utils.HorizontalAggregations)

	if err == nil {

		_ = json.Unmarshal(bytes, &horizontalAggregationConfigs)

		horizontalAggregations = horizontalAggregationConfigs.ToConcurrentMap()

	} else {

		horizontalAggregationConfigs = make(utils.MotadataMap)

		dataStoreLogger.Error(fmt.Sprintf(utils.ErrorLocateFile, utils.HorizontalAggregations))
	}

	if _, err = os.Stat(utils.JobDir + utils.PathSeparator + utils.TempPatch + utils.AggregationContexts); err != nil {

		_ = os.Remove(utils.JobDir + utils.PathSeparator + utils.TempPatch + utils.AggregationContexts)
	}

	bytes, err = os.ReadFile(utils.JobDir + utils.PathSeparator + utils.AggregationContexts)

	if err == nil {

		_ = json.Unmarshal(bytes, &aggregationContexts)

	} else {

		aggregationContexts = make(map[string]bitmap.Bitmap)

		dataStoreLogger.Error(fmt.Sprintf(utils.ErrorLocateFile, utils.AggregationContexts))
	}

}

func IsEventMetadataField(column string) bool {

	return column == utils.EventCategory || column == utils.EventSourceType || column == utils.EventPatternId || column == utils.Message || column == utils.EventSource || column == utils.EventSeverity
}

func AddColumnEncoder(column string, encoder Encoding) {

	encoders[column] = encoder
}

func IsHorizontalAggregationField(field string) bool {

	rwLock.RLock()

	defer rwLock.RUnlock()

	_, found := horizontalAggregationFields[field]

	return found

}

func AddHorizontalAggregationField(field string) {

	rwLock.Lock()

	defer rwLock.Unlock()

	horizontalAggregationFields[field] = struct{}{}
}

func populateStopWords() {

	if stopWords == nil || len(stopWords) == 0 {

		stopWords = map[string]struct{}{}

		bytes, _ := os.ReadFile(utils.CurrentDir + utils.PathSeparator + utils.StopWordFile)

		if bytes != nil && len(bytes) > 0 {

			for _, word := range strings.Split(string(bytes), " ") {

				stopWords[strings.ToLower(word)] = struct{}{}
			}

			dataStoreLogger.Info("stop words loaded for indexing...")
		}
	}
}

func populateStores() {

	dataStoreDir := utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir

	_, err := os.Stat(dataStoreDir)

	if !os.IsNotExist(err) {

		dirs, err := os.ReadDir(dataStoreDir)

		if err != nil {

			dataStoreLogger.Fatal(fmt.Sprintf(utils.ErrorDatastoreInit, err))

			stackTraceBytes := make([]byte, 1<<20)

			utils.ShutdownNotifications <- "Datastore" + utils.GroupSeparator + fmt.Sprintf(utils.ErrorDatastoreInit, err) + utils.GroupSeparator + fmt.Sprintf("%v", string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)]))

		}

		for i := range dirs {

			if dirs[i].IsDir() {

				storeName := dirs[i].Name()

				if strings.HasPrefix(storeName, utils.TempPatch) {

					_ = os.RemoveAll(dataStoreDir + utils.PathSeparator + storeName)

					continue

				} else if strings.HasPrefix(storeName, "patch@") {

					storeName = strings.TrimPrefix(dirs[i].Name(), "patch@")

					if _, err = os.Stat(dataStoreDir + utils.PathSeparator + storeName); err == nil {

						_ = os.RemoveAll(dataStoreDir + utils.PathSeparator + storeName)
					}

					_ = os.Rename(dataStoreDir+utils.PathSeparator+dirs[i].Name(), dataStoreDir+utils.PathSeparator+storeName)
				}

				stores.Set(storeName, nil)
			}
		}
	}

}

func Close() {

	lock.Lock()

	defer lock.Unlock()

	for _, store := range stores.Items() {

		if store != nil {

			store.(*Store).Close(dataStoreEncoder)
		}
	}

	dataStoreEncoder.MemoryPool.Unmap()

	dataStoreLogger.Info("datastore shutdown...")

}

func loadStore(name string, dataStoreType utils.DatastoreType, encoder Encoder, tokenizer *utils.Tokenizer) *Store {

	lock.Lock()

	defer lock.Unlock()

	if value, _ := stores.Get(name); value != nil {

		return value.(*Store)

	}

	store, err := OpenOrCreateStore(name, dataStoreType, encoder, tokenizer, false)

	if err != nil {

		if strings.Contains(err.Error(), "less than system pool length") {

			return nil
		}

		if strings.Contains(err.Error(), "failed to open partition") {

			storeDir := utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir + utils.PathSeparator + name

			dataStoreLogger.Fatal(fmt.Sprintf(utils.ErrorCreateStore, name, err.Error()))

			stackTraceBytes := make([]byte, 1<<20)

			corruptedStoreDir := storeDir + "-corrupted" + utils.HyphenSeparator + INT64ToStringValue(time.Now().UnixMilli())

			_ = os.MkdirAll(corruptedStoreDir, 0755)

			_ = cp.Copy(storeDir, corruptedStoreDir)

			dataStoreLogger.Error(fmt.Sprintf("%v", string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))

			store, err = OpenOrCreateStore(name, dataStoreType, encoder, tokenizer, true)

			if err != nil {

				dataStoreLogger.Fatal(fmt.Sprintf(utils.ErrorStoreReopen, name, err.Error()))

				return nil
			}

		} else {

			dataStoreLogger.Fatal(fmt.Sprintf(utils.ErrorCreateStore, name, err.Error()))

			stackTraceBytes := make([]byte, 1<<20)

			storePath := utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir + utils.PathSeparator

			err = os.Rename(storePath+name, storePath+name+"-corrupted"+utils.HyphenSeparator+INT64ToStringValue(time.Now().UnixMilli()))

			dataStoreLogger.Error(fmt.Sprintf("%v", string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))

			store, err = OpenOrCreateStore(name, dataStoreType, encoder, tokenizer, false)

			if err != nil {

				dataStoreLogger.Fatal(fmt.Sprintf(utils.ErrorStoreReopen, name, err.Error()))

				return nil
			}
		}

	}

	stores.Set(name, store)

	if utils.StoreSyncJobAddNotifications != nil {

		utils.StoreSyncJobAddNotifications <- name + utils.GroupSeparator + INTToStringValue(int(dataStoreType))
	}

	return store
}

func GetStore(name string, dataStoreType utils.DatastoreType, createOrOpen, open bool, encoder Encoder, tokenizer *utils.Tokenizer) *Store {

	var store *Store

	if !stores.Has(name) && createOrOpen {

		store = loadStore(name, dataStoreType, encoder, tokenizer)

		return store

	} else if stores.Has(name) {

		if value, _ := stores.Get(name); value == nil {

			if !open { // closed store doesn't need to open from cleanup job

				return nil
			}

			store = loadStore(name, dataStoreType, encoder, tokenizer)

		} else {

			store = value.(*Store)
		}

		return store

	}

	return store
}

func IsStoreAvailable(name string) bool {

	if stores.Has(name) {

		return true
	}

	return false
}

func IsStoreClosed(name string) bool {

	if store, found := stores.Get(name); found {

		return store == nil
	}

	return true
}

func GetTimeTickKey(key string) string {

	return key + utils.KeySeparator + Time

}

func GetDatastoreType(dataStoreType utils.DatastoreType) string {

	switch dataStoreType {

	case utils.Log:
		return "Log"
	case utils.Flow:
		return "Flow"
	case utils.Trap:
		return "Trap"
	case utils.MetricPolicy:
		return "MetricPolicy"
	case utils.EventPolicy:
		return "EventPolicy"
	case utils.Audit:
		return "Audit"
	case utils.Notification:
		return "User Notification"
	case utils.CorrelationMetric:
		return "Correlated Metric"
	case utils.PolicyResult:
		return "Policy Result"
	case utils.RunbookWorklog:
		return "Runbook Worklog"
	case utils.Index:
		return "Index"
	case utils.MetricIndex:
		return "MetricIndex"
	case utils.CorrelatedMetricIndex:
		return "CorrelatedMetricIndex"
	case utils.LogIndex:
		return "LogIndex"
	case utils.FlowIndex:
		return "FlowIndex"
	case utils.TrapIndex:
		return "TrapIndex"
	case utils.NotificationIndex:
		return "NotificationIndex"
	case utils.AuditIndex:
		return "AuditIndex"
	case utils.PolicyIndex:
		return "PolicyIndex"
	case utils.HealthMetric:
		return "HealthMetric"
	case utils.HealthMetricIndex:
		return "HealthMetricIndex"
	case utils.Compliance:
		return "Compliance"
	case utils.ObjectStatusFlapMetric:
		return "StatusFlapMetric"
	case utils.ComplianceIndex:
		return "ComplianceIndex"
	case utils.NetRouteMetric:
		return "NetRouteMetric"
	}

	return utils.Empty
}

func GetStoreName(tick int32, name, storeFormat string) string {

	if storeFormat != utils.Empty {

		return utils.SecondsToDate(tick) + utils.HyphenSeparator + storeFormat + utils.HyphenSeparator + name

	} else {

		return utils.SecondsToDate(tick) + utils.HyphenSeparator + name

	}

}

func GetStoreById(tick int64, pluginId, dataStoreType string) string {

	return GetStoreName(utils.UnixToSeconds(tick), pluginId, dataStoreType)
}

func GetMetricKey(monitor string, metric, instance string) string {

	if len(instance) == 0 {

		return monitor + utils.KeySeparator + metric
	} else {

		return monitor + utils.KeySeparator + instance + utils.KeySeparator + metric
	}
}

func GetAggregatedStoreName(tick int32, name, aggregation, dataStoreType string) string {

	return GetStoreName(tick, name+utils.HyphenSeparator+aggregation, dataStoreType)

}

func IsFloatingColumn(name string) bool {

	if floatingColumns.Has(name) {

		return true

	} else {

		return strings.HasSuffix(name, SuffixPercent) ||
			strings.HasSuffix(name, ".cost") ||
			strings.HasSuffix(name, ".amount") ||
			strings.Contains(name, "latency") ||
			strings.Contains(name, "avg")
	}
}

func IsGarbageColumn(name string) bool {

	if garbageColumns.Has(name) {

		return true

	} else {

		return strings.HasSuffix(name, ".id") ||
			strings.HasSuffix(name, ".uptime") ||
			strings.HasSuffix(name, ".version") ||
			strings.HasSuffix(name, ".uptime.seconds") ||
			strings.HasSuffix(name, ".started.time.seconds") ||
			strings.HasSuffix(name, ".started.time") ||
			strings.HasSuffix(name, ".uptime.sec") ||
			strings.HasSuffix(name, ".creation.time.seconds") ||
			strings.HasSuffix(name, ".status") ||
			strings.HasSuffix(name, ".state")
	}

}

func GetKey(tick int32, value string, part uint16) string {

	return INT32ToStringValue(tick) + utils.KeySeparator + value + utils.KeySeparator + UINT16ToStringValue(part)
}

func GetColumnEncoder(name string) Encoding {

	rwLock.RLock()

	defer rwLock.RUnlock()

	return encoders[name]
}

func FlipShadowCounter(counter string) string {

	shadowCounter, _ := shadowCounters[counter]

	return shadowCounter
}

func IsShadowCounter(counter string) bool {

	if _, ok := shadowCounters[counter]; ok {

		return true
	}

	return false
}

func AlterShadowCounter(counter string) string {

	alteredShadowCounter, _ := alteredShadowCounters[counter]

	return alteredShadowCounter
}

func AddShadowCounter(counter, shadowCounter string) {

	if shadowCounters != nil {

		shadowCounters[shadowCounter] = counter

	}
	aggregations := []string{utils.Sum, utils.Count, utils.Min, utils.Max, utils.Last, "avg"}

	for index := 0; index < len(aggregations); index++ {

		alteredShadowCounters[counter+utils.KeySeparator+aggregations[index]] = shadowCounter + utils.KeySeparator + aggregations[index]
	}

}

func IsIndexablePlugin(plugin string) bool {

	return indexablePlugins.Has(plugin)
}

// AlterIndexableColumns you cannot alter the columns (value of indexable column) as they are not thread safe
func AlterIndexableColumns(plugin string, fields map[string]interface{}, operation string) {

	var notification map[string]interface{}

	switch operation {

	case utils.Add:

		indexablePlugins.SetIfAbsent(plugin, fields)

		saveColumns(utils.IndexableColumns, plugin, fields, utils.Add)

		notification = fields

	case utils.Remove:

		columns, _ := indexablePlugins.Get(plugin)

		alteredColumns := make(map[string]interface{})

		for column := range columns.(map[string]interface{}) {

			if _, ok := fields[column]; !ok {

				alteredColumns[column] = utils.Empty
			}

		}

		if len(columns.(map[string]interface{})) == 0 {

			indexablePlugins.Remove(plugin)

			saveColumns(utils.IndexableColumns, plugin, alteredColumns, utils.Remove)

		} else {

			saveColumns(utils.IndexableColumns, plugin, alteredColumns, utils.Add)

			indexablePlugins.Set(plugin, alteredColumns)

			notification = alteredColumns

		}

	case utils.Merge:

		if indexablePlugins.Has(plugin) {

			columns, _ := indexablePlugins.Get(plugin)

			alteredColumns := make(map[string]interface{})

			for field := range fields {

				alteredColumns[field] = utils.Empty
			}

			for column := range columns.(map[string]interface{}) {

				alteredColumns[column] = utils.Empty
			}

			saveColumns(utils.IndexableColumns, plugin, alteredColumns, utils.Add)

			indexablePlugins.Set(plugin, alteredColumns)

			notification = alteredColumns

		} else {

			indexablePlugins.Set(plugin, fields)

			notification = fields
		}

	case utils.Replace:

		if indexablePlugins.Has(plugin) {

			indexablePlugins.Set(plugin, fields)

			saveColumns(utils.IndexableColumns, plugin, fields, utils.Add)

			notification = fields
		}

	}

	if notification != nil && utils.PublisherNotifications != nil {

		utils.PublisherNotifications <- utils.MotadataMap{
			utils.OperationType: utils.IndexUpdate,
			utils.EventContext: utils.MotadataMap{
				plugin: notification,
			},
		}
	}
}

func IsNonIndexableColumn(column string) bool {

	if strings.Contains(column, "bytes") ||
		strings.Contains(column, "packets") ||
		strings.Contains(column, "percent") ||
		strings.Contains(column, Duration) ||
		strings.Contains(column, "sec") ||
		strings.Contains(column, Time) ||
		strings.Contains(column, Timestamp) ||
		strings.Contains(column, "seconds") ||
		strings.Contains(column, "pid") ||
		strings.Contains(column, "cpu") ||
		strings.Contains(column, "count") ||
		strings.Contains(column, "memory") ||
		strings.Contains(column, "disk") ||
		strings.Contains(column, "incoming") ||
		strings.Contains(column, "outgoing") ||
		strings.Contains(column, "description") ||
		strings.Contains(column, "latency") ||
		strings.Contains(column, utils.Message) ||
		strings.EqualFold(column, utils.Event) {

		return true
	}

	return false
}

func IsInvalidIndexableColumn(plugin string, column string) bool {

	return invalidIndexableColumns.Has(plugin + utils.KeySeparator + column)
}

func AddInvalidIndexableColumn(plugin string, column string) {

	invalidIndexableColumns.Set(plugin+utils.KeySeparator+column, nil)

}

func AddSearchableColumn(column string) {

	save(utils.SearchableColumns, column, searchableColumns)

}

func AddGarbageColumn(column string) {

	save(utils.GarbageColumns, column, garbageColumns)

}

func AddFloatingColumn(column string) {

	save(utils.FloatingColumns, column, floatingColumns)
}

func UpdateVerticalAggregations(metric string, aggregate bool) {

	if value, found := verticalAggregations.Get(metric); !found || (value != aggregate) {

		verticalAggregations.Set(metric, aggregate)

		writeAggregationConfigs(metric, aggregate, utils.VerticalFormat, false)
	}

}

func writeAggregationConfigs(key string, value interface{}, storeFormat string, locked bool) {

	if !locked {

		rwLock.Lock()

		defer rwLock.Unlock()
	}

	var configurations utils.MotadataMap

	var file string

	if storeFormat == utils.VerticalFormat {

		configurations = verticalAggregationConfigs

		configurations[key] = value

		file = utils.VerticalAggregations
	} else {

		configurations = horizontalAggregationConfigs

		configurations[key] = value

		if len(value.(map[string]interface{})) == 0 {

			//There might be a condition in which all the views are deleted so need to remove that plugin from horizontal aggregations
			configurations.Delete(key)

		}

		file = utils.HorizontalAggregations
	}

	bytes, err := json.MarshalIndent(configurations, "", "  ")

	if err != nil {

		return
	}

	_ = os.Remove(configDir + utils.PathSeparator + utils.Temp + file)

	if err := os.WriteFile(configDir+utils.PathSeparator+utils.Temp+file, bytes, 0644); err != nil {

		dataStoreLogger.Error(fmt.Sprintf("failed to write aggregation context file : %s , reason : %s", file, err.Error()))
	} else {

		if err = os.Rename(configDir+utils.PathSeparator+utils.Temp+file, configDir+utils.PathSeparator+file); err != nil {

			dataStoreLogger.Error(fmt.Sprintf("failed to overwrite aggregation context file : %s , reason : %s", file, err.Error()))
		}
	}
}

func GetAggregationContexts() map[string]map[uint32]int {

	rwLock.RLock()

	defer rwLock.RUnlock()

	contexts := make(map[string]map[uint32]int)

	for key, positions := range aggregationContexts {

		if _, ok := contexts[key]; !ok {

			contexts[key] = make(map[uint32]int)

		}

		positions.Range(func(position uint32) {

			contexts[key][position] = 1
		})

	}

	return contexts
}

func UpdateAggregationContexts(key string, position uint32, operation string) {

	rwLock.Lock()

	defer rwLock.Unlock()

	if _, ok := aggregationContexts[key]; !ok {

		aggregationContexts[key] = bitmap.Bitmap{}

	}

	var metadata bitmap.Bitmap

	if !aggregationContexts[key].Contains(position) {

		metadata = aggregationContexts[key]

		if operation == utils.Add {

			metadata.Set(position)

		}

	} else if operation == utils.Remove {

		metadata = aggregationContexts[key]

		metadata.Remove(position)
	}

	aggregationContexts[key] = metadata

	if aggregationContexts[key].Count() == 0 {

		delete(aggregationContexts, key)
	}

	writeAggregationContexts()
}

func writeAggregationContexts() {

	bytes, _ := json.MarshalIndent(aggregationContexts, "", "  ")

	err := os.WriteFile(utils.JobDir+utils.PathSeparator+utils.AggregationContexts, bytes, 0644)

	if err != nil {

		return

	}

}

func IsAggregationContextExists(key string) bool {

	rwLock.RLock()

	defer rwLock.RUnlock()

	if _, ok := aggregationContexts[key]; ok {

		return true
	}

	return false

}

func IsAggregationContextPositionExists(key string, position uint32) bool {

	rwLock.RLock()

	defer rwLock.RUnlock()

	if metadata, ok := aggregationContexts[key]; ok && metadata.Contains(position) {

		return true

	}

	return false
}

func saveColumns(parent, child string, value interface{}, operation string) {

	rwLock.Lock()

	defer rwLock.Unlock()

	switch operation {

	case utils.Add:

		if !configs.Contains(parent) {

			configs[parent] = make(utils.MotadataMap)

		}

		configs.GetMapValue(parent)[child] = value

	case utils.Remove:

		if configs.Contains(parent) {

			configs.GetMapValue(parent).Delete(child)
		}

	}

	bytes, err := json.MarshalIndent(configs, "", "  ")

	if err != nil {

		return
	}

	err = os.WriteFile(configDir+utils.PathSeparator+utils.ColumnConfigFile, bytes, 0644)

	if err != nil {

		return

	}

}

func IsAggregationMetric(metric string) bool {

	if !utils.Aggregation {

		return false
	}

	if verticalAggregations.Has(metric) {

		enabled, _ := verticalAggregations.Get(metric)

		return enabled.(bool)
	}

	return false
}

func IsSearchableColumn(name string) bool {

	return searchableColumns.Has(name)
}

func IsBlobColumn(name string) bool {

	return blobColumns.Has(name)
}

func AddBlobColumn(column string) {

	save(utils.BlobColumns, column, blobColumns)
}

func save(key, value string, values cmap.ConcurrentMap) {

	if values.Has(value) {

		return
	}

	values.SetIfAbsent(value, struct{}{})

	rwLock.Lock()

	defer rwLock.Unlock()

	configs[key] = values.Items()

	bytes, err := json.MarshalIndent(configs, "", "  ")

	if err != nil {

		return
	}

	err = os.WriteFile(configDir+utils.PathSeparator+utils.ColumnConfigFile, bytes, 0644)

	if err != nil {

		return

	}
}

func IsHorizontalAggregationFound(plugin string) bool {

	if !utils.Aggregation {

		return false
	}
	_, found := horizontalAggregations.Get(plugin)

	return found
}

func IsHorizontalAggregationExist(plugin, aggregation string) bool {

	if !utils.Aggregation {

		return false
	}

	value, found := horizontalAggregations.Get(plugin)

	if found {

		rwLock.RLock()

		defer rwLock.RUnlock()

		return utils.ToMap(value).Contains(aggregation)
	}

	return false
}

func AddHorizontalAggregation(plugin, aggregation string, context map[string]interface{}) {

	value, found := horizontalAggregations.Get(plugin)

	aggregations := map[string]interface{}{}

	locked := false

	if found {

		rwLock.Lock()

		defer rwLock.Unlock()

		aggregations = value.(map[string]interface{})

		locked = true
	}

	aggregations[aggregation] = context

	horizontalAggregations.Set(plugin, aggregations)

	writeAggregationConfigs(plugin, aggregations, utils.HorizontalFormat, locked)

}

func RemoveHorizontalAggregation(plugin, aggregation string) {

	value, found := horizontalAggregations.Get(plugin)

	if found {

		rwLock.Lock()

		defer rwLock.Unlock()

		delete(value.(map[string]interface{}), aggregation)

		horizontalAggregations.Set(plugin, value)

		//might possible no aggregation view is there for that plugin
		if len(value.(map[string]interface{})) == 0 {

			horizontalAggregations.Remove(plugin)
		}

		writeAggregationConfigs(plugin, value, utils.HorizontalFormat, true)

	}
}

func DisableHorizontalAggregations(plugin string) {

	rwLock.Lock()

	defer rwLock.Unlock()

	_, found := horizontalAggregations.Get(plugin)

	if !found {

		return
	}

	horizontalAggregations.Remove(plugin)

}

func GetHorizontalAggregation(plugin string, aggregationColumns, indexableColumns map[string]struct{}, tokenizer *utils.Tokenizer, widgetId string, fulltextSearchingView bool) (result string) {

	if !utils.Aggregation {

		return utils.Empty
	}

	value, found := horizontalAggregations.Get(plugin)

	if found {

		rwLock.RLock()

		defer rwLock.RUnlock()

		if fulltextSearchingView {

			for aggregation := range value.(map[string]interface{}) {

				if strings.Contains(aggregation, widgetId) {

					return aggregation
				}

			}

			return utils.Empty

		}

		minIndex := math.MaxInt64

		if len(aggregationColumns) == 0 && len(indexableColumns) == 0 {

			for aggregation := range value.(map[string]interface{}) {

				utils.Split(aggregation, utils.AggregationSeparator, tokenizer)

				viewIndex := StringToINT(tokenizer.Tokens[1])

				if viewIndex < minIndex {

					minIndex = viewIndex
				}
			}

			if minIndex == math.MaxInt64 {

				minIndex = 0
			}

			return plugin + utils.AggregationSeparator + INTToStringValue(minIndex)
		}

		minCardinality := math.MaxInt64

		for aggregation, context := range value.(map[string]interface{}) {

			if strings.Contains(aggregation, utils.PartSeparator) {

				continue
			}

			valid := true

			context := utils.ToMap(context)

			for column := range aggregationColumns {

				if !context.Contains(column) {

					valid = false

					break
				}
			}

			if !valid {

				continue
			}

			columns := context.GetMapValue(utils.IndexableColumns)

			for column := range indexableColumns {

				if !columns.Contains(column) {

					valid = false

					break
				}
			}

			if !valid {

				continue
			}

			if minCardinality > len(indexableColumns) {

				minCardinality = len(indexableColumns)

				result = aggregation
			}
		}
	}

	return result
}

func GetIndexableColumns() utils.MotadataMap {

	return utils.MotadataMap{

		utils.OperationType: utils.IndexUpdate,
		utils.EventContext:  indexablePlugins.Items(),
	}

}

func GetVerticalAggregations() utils.MotadataMap {

	if verticalAggregations == nil {

		return nil
	}

	return verticalAggregations.Items()

}

func IsIndexableColumn(plugin, column string) bool {

	columns, ok := indexablePlugins.Get(plugin)

	if ok {

		_, found := columns.(map[string]interface{})[column]

		return found
	}

	return false
}

func RemoveStore(name string) {

	stores.Set(name, nil)
}

func DeleteStore(name string) {

	lock.Lock()

	defer lock.Unlock()

	stores.Remove(name)

	_ = os.RemoveAll(utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir + utils.PathSeparator + name)

	dataStoreLogger.Info(fmt.Sprintf("store %v deleted", name))

}

func IsStopWord(token string) bool {

	_, found := stopWords[token]

	return found
}

func UpdateStoreCacheMapping(storeName string, records int) {

	if store, _ := stores.Get(storeName); store != nil {

		store.(*Store).UpdateCacheMapping(records, true)
	}
}

///////  Various datatype writing and reading related methods...... //////////////

func GetINT8ColumnValues(currentDataType, previousDataType DataType, bytes []byte, encoding Encoding, key, storeName string, padding int, decoder Decoder) (int, DataType, []int8, []int16, []int32, []int64, []float64, error) {

	poolIndex, decodedValues, err := decoder.DecodeINT8Values(encoding, bytes, key, storeName, padding)

	if err != nil || poolIndex == utils.NotAvailable {

		return -1, Invalid, nil, nil, nil, nil, nil, err
	}

	if currentDataType > previousDataType {

		switch {

		case currentDataType == Int16:

			valuePoolIndex, values := decoder.MemoryPool.AcquireINT16Pool(len(decodedValues))

			values = INT8ToINT16Values(decodedValues, values)

			decoder.MemoryPool.ReleaseINT8Pool(poolIndex)

			return valuePoolIndex, currentDataType, nil, values, nil, nil, nil, err

		case currentDataType == Int24 || currentDataType == Int32:

			valuePoolIndex, values := decoder.MemoryPool.AcquireINT32Pool(len(decodedValues))

			values = INT8ToINT32Values(decodedValues, values)

			decoder.MemoryPool.ReleaseINT8Pool(poolIndex)

			return valuePoolIndex, currentDataType, nil, nil, values, nil, nil, err

		case currentDataType == Int40 || currentDataType == Int48 || currentDataType == Int56 || currentDataType == Int64:

			valuePoolIndex, values := decoder.MemoryPool.AcquireINT64Pool(len(decodedValues))

			values = INT8ToINT64Values(decodedValues, values)

			decoder.MemoryPool.ReleaseINT8Pool(poolIndex)

			return valuePoolIndex, currentDataType, nil, nil, nil, values, nil, err

		case currentDataType == Float8 || currentDataType == Float16 || currentDataType == Float64:

			valuePoolIndex, values := decoder.MemoryPool.AcquireFLOAT64Pool(len(decodedValues))

			values = INT8ToFLOAT64Values(decodedValues, values)

			decoder.MemoryPool.ReleaseINT8Pool(poolIndex)

			return valuePoolIndex, currentDataType, nil, nil, nil, nil, values, err
		}

		decoder.MemoryPool.ReleaseINT8Pool(poolIndex)

		return -1, Invalid, nil, nil, nil, nil, nil, errors.New(fmt.Sprintf(utils.ErrorUnknownDataType, key, storeName))

	}

	return poolIndex, previousDataType, decodedValues, nil, nil, nil, nil, nil
}

func GetINT16ColumnValues(currentDataType, previousDataType DataType, encoding Encoding, bytes []byte, key, storeName string, padding int, decoder Decoder) (int, DataType, []int16, []int32, []int64, []float64, error) {

	poolIndex, decodedValues, err := decoder.DecodeINT16Values(encoding, bytes, key, storeName, padding)

	if err != nil {

		return -1, Invalid, nil, nil, nil, nil, err
	}

	if currentDataType > previousDataType {

		switch {

		case currentDataType == Int24 || currentDataType == Int32:

			valuePoolIndex, values := decoder.MemoryPool.AcquireINT32Pool(len(decodedValues))

			values = INT16ToINT32Values(decodedValues, values)

			decoder.MemoryPool.ReleaseINT16Pool(poolIndex)

			return valuePoolIndex, currentDataType, nil, values, nil, nil, err

		case currentDataType == Int40 || currentDataType == Int48 || currentDataType == Int56 || currentDataType == Int64:

			valuePoolIndex, values := decoder.MemoryPool.AcquireINT64Pool(len(decodedValues))

			values = INT16ToINT64Values(decodedValues, values)

			decoder.MemoryPool.ReleaseINT16Pool(poolIndex)

			return valuePoolIndex, currentDataType, nil, nil, values, nil, err

		case currentDataType == Float8 || currentDataType == Float16 || currentDataType == Float64:

			valuePoolIndex, values := decoder.MemoryPool.AcquireFLOAT64Pool(len(decodedValues))

			values = INT16ToFLOAT64Values(decodedValues, values)

			decoder.MemoryPool.ReleaseINT16Pool(poolIndex)

			return valuePoolIndex, currentDataType, nil, nil, nil, values, err
		}

		decoder.MemoryPool.ReleaseINT16Pool(poolIndex)

		return -1, Invalid, nil, nil, nil, nil, errors.New(fmt.Sprintf(utils.ErrorUnknownDataType, key, storeName))

	}

	return poolIndex, previousDataType, decodedValues, nil, nil, nil, nil
}

func GetINT24ColumnValues(currentDataType, previousDataType DataType, encoding Encoding, bytes []byte, key, storeName string, padding int, decoder Decoder) (int, DataType, []int32, []int64, []float64, error) {

	poolIndex, decodedValues, err := decoder.DecodeINT32Values(encoding, previousDataType, bytes, key, storeName, padding)

	if err != nil {

		return -1, Invalid, nil, nil, nil, err
	}

	if currentDataType > previousDataType {

		switch {

		case currentDataType == Int32:

			return poolIndex, currentDataType, decodedValues, nil, nil, err

		case currentDataType == Int40 || currentDataType == Int48 || currentDataType == Int56 || currentDataType == Int64:

			index, values := decoder.MemoryPool.AcquireINT64Pool(len(decodedValues))

			values = INT32ToINT64Values(decodedValues, values)

			decoder.MemoryPool.ReleaseINT32Pool(poolIndex)

			return index, currentDataType, nil, values, nil, err

		case currentDataType == Float8 || currentDataType == Float16 || currentDataType == Float64:

			index, values := decoder.MemoryPool.AcquireFLOAT64Pool(len(decodedValues))

			values = INT32ToFLOAT64Values(decodedValues, values)

			decoder.MemoryPool.ReleaseINT32Pool(poolIndex)

			return index, currentDataType, nil, nil, values, err
		}

		decoder.MemoryPool.ReleaseINT32Pool(poolIndex)

		return -1, Invalid, nil, nil, nil, errors.New(fmt.Sprintf(utils.ErrorUnknownDataType, key, storeName))

	}

	return poolIndex, previousDataType, decodedValues, nil, nil, nil
}

func GetINT32ColumnValues(currentDataType, previousDataType DataType, encoding Encoding, bytes []byte, key, storeName string, padding int, decoder Decoder) (int, DataType, []int32, []int64, []float64, error) {

	poolIndex, decodedValues, err := decoder.DecodeINT32Values(encoding, previousDataType, bytes, key, storeName, padding)

	if err != nil {

		return -1, Invalid, nil, nil, nil, err
	}

	if currentDataType > previousDataType {

		switch {

		case currentDataType == Int40 || currentDataType == Int48 || currentDataType == Int56 || currentDataType == Int64:

			index, values := decoder.MemoryPool.AcquireINT64Pool(len(decodedValues))

			values = INT32ToINT64Values(decodedValues, values)

			decoder.MemoryPool.ReleaseINT32Pool(poolIndex)

			return index, currentDataType, nil, values, nil, err

		case currentDataType == Float8 || currentDataType == Float16 || currentDataType == Float64:

			index, values := decoder.MemoryPool.AcquireFLOAT64Pool(len(decodedValues))

			values = INT32ToFLOAT64Values(decodedValues, values)

			decoder.MemoryPool.ReleaseINT32Pool(poolIndex)

			return index, currentDataType, nil, nil, values, err
		}

		decoder.MemoryPool.ReleaseINT32Pool(poolIndex)

		return -1, Invalid, nil, nil, nil, errors.New(fmt.Sprintf(utils.ErrorUnknownDataType, key, storeName))

	}

	return poolIndex, previousDataType, decodedValues, nil, nil, nil
}

func GetINT40ColumnValues(currentDataType, previousDataType DataType, encoding Encoding, bytes []byte, key, storeName string, padding int, decoder Decoder) (int, DataType, []int64, []float64, error) {

	poolIndex, decodedValues, err := decoder.DecodeINT64Values(encoding, previousDataType, bytes, key, storeName, padding)

	if err != nil {

		return -1, Invalid, nil, nil, err
	}

	if currentDataType > previousDataType {

		switch {

		case currentDataType == Int48 || currentDataType == Int56 || currentDataType == Int64:

			return poolIndex, currentDataType, decodedValues, nil, err

		case currentDataType == Float8 || currentDataType == Float16 || currentDataType == Float64:

			index, values := decoder.MemoryPool.AcquireFLOAT64Pool(len(decodedValues))

			values = INT64ToFLOAT64Values(decodedValues, values)

			decoder.MemoryPool.ReleaseINT64Pool(poolIndex)

			return index, currentDataType, nil, values, err
		}

		decoder.MemoryPool.ReleaseINT64Pool(poolIndex)

		return -1, Invalid, nil, nil, errors.New(fmt.Sprintf(utils.ErrorUnknownDataType, key, storeName))

	}

	return poolIndex, previousDataType, decodedValues, nil, nil
}

func GetINT48ColumnValues(currentDataType, previousDataType DataType, encoding Encoding, bytes []byte, key, storeName string, padding int, decoder Decoder) (int, DataType, []int64, []float64, error) {

	poolIndex, decodedValues, err := decoder.DecodeINT64Values(encoding, previousDataType, bytes, key, storeName, padding)

	if err != nil {

		return -1, Invalid, nil, nil, err
	}

	if currentDataType > previousDataType {

		switch {

		case currentDataType == Int56 || currentDataType == Int64:

			return poolIndex, currentDataType, decodedValues, nil, err

		case currentDataType == Float8 || currentDataType == Float16 || currentDataType == Float64:

			index, values := decoder.MemoryPool.AcquireFLOAT64Pool(len(decodedValues))

			values = INT64ToFLOAT64Values(decodedValues, values)

			decoder.MemoryPool.ReleaseINT64Pool(poolIndex)

			return index, currentDataType, nil, values, err
		}

		decoder.MemoryPool.ReleaseINT64Pool(poolIndex)

		return -1, Invalid, nil, nil, errors.New(fmt.Sprintf(utils.ErrorUnknownDataType, key, storeName))

	}

	return poolIndex, previousDataType, decodedValues, nil, nil
}

func GetINT56ColumnValues(currentDataType, previousDataType DataType, encoding Encoding, bytes []byte, key, storeName string, padding int, decoder Decoder) (int, DataType, []int64, []float64, error) {

	poolIndex, decodedValues, err := decoder.DecodeINT64Values(encoding, previousDataType, bytes, key, storeName, padding)

	if err != nil {

		return -1, Invalid, nil, nil, err
	}

	if currentDataType > previousDataType {

		switch {

		case currentDataType == Int64:

			return poolIndex, currentDataType, decodedValues, nil, err

		case currentDataType == Float8 || currentDataType == Float16 || currentDataType == Float64:

			index, values := decoder.MemoryPool.AcquireFLOAT64Pool(len(decodedValues))

			values = INT64ToFLOAT64Values(decodedValues, values)

			decoder.MemoryPool.ReleaseINT64Pool(poolIndex)

			return index, currentDataType, nil, values, err
		}

		decoder.MemoryPool.ReleaseINT64Pool(poolIndex)

		return -1, Invalid, nil, nil, errors.New(fmt.Sprintf(utils.ErrorUnknownDataType, key, storeName))

	}

	return poolIndex, previousDataType, decodedValues, nil, nil
}

func GetINT64ColumnValues(currentDataType, previousDataType DataType, encoding Encoding, bytes []byte, key, storeName string, padding int, decoder Decoder) (int, DataType, []int64, []float64, error) {

	poolIndex, decodedValues, err := decoder.DecodeINT64Values(encoding, previousDataType, bytes, key, storeName, padding)

	if err != nil {

		return -1, Invalid, nil, nil, err
	}

	if currentDataType > previousDataType {

		switch {

		case currentDataType == Float8 || currentDataType == Float16 || currentDataType == Float64:

			index, values := decoder.MemoryPool.AcquireFLOAT64Pool(len(decodedValues))

			values = INT64ToFLOAT64Values(decodedValues, values)

			decoder.MemoryPool.ReleaseINT64Pool(poolIndex)

			return index, currentDataType, nil, values, err
		}

		decoder.MemoryPool.ReleaseINT64Pool(poolIndex)

		return -1, Invalid, nil, nil, errors.New(fmt.Sprintf(utils.ErrorUnknownDataType, key, storeName))

	}

	return poolIndex, previousDataType, decodedValues, nil, nil
}

func GetFLOAT8ColumnValues(currentDataType, previousDataType DataType, encoding Encoding, bytes []byte, key, storeName string, padding int, decoder Decoder) (int, DataType, []float64, error) {

	poolIndex, decodedValues, err := decoder.DecodeFLOAT8Values(encoding, bytes, key, storeName, padding)

	if err != nil {

		return -1, Invalid, nil, err
	}

	if currentDataType > previousDataType {

		switch {

		case currentDataType == Float16 || currentDataType == Float64:

			return poolIndex, currentDataType, decodedValues, err
		}

		decoder.MemoryPool.ReleaseFLOAT64Pool(poolIndex)

		return -1, Invalid, nil, errors.New(fmt.Sprintf(utils.ErrorUnknownDataType, key, storeName))

	} else if previousDataType > currentDataType {

		if currentDataType == Int16 {

			previousDataType = Float16

		} else if currentDataType > Int16 {

			previousDataType = Float64
		}
	}

	return poolIndex, previousDataType, decodedValues, nil
}

func GetFLOAT16ColumnValues(currentDataType, previousDataType DataType, encoding Encoding, bytes []byte, key, storeName string, padding int, decoder Decoder) (int, DataType, []float64, error) {

	poolIndex, decodedValues, err := decoder.DecodeFLOAT16Values(encoding, bytes, key, storeName, padding)

	if err != nil {

		return -1, Invalid, nil, err
	}

	if currentDataType > previousDataType {

		switch {

		case currentDataType == Float64:

			return poolIndex, currentDataType, decodedValues, err
		}

		decoder.MemoryPool.ReleaseFLOAT64Pool(poolIndex)

		return -1, Invalid, nil, errors.New(fmt.Sprintf(utils.ErrorUnknownDataType, key, storeName))

	} else if previousDataType > currentDataType {

		if currentDataType > Int16 {

			previousDataType = Float64
		}
	}

	return poolIndex, previousDataType, decodedValues, nil
}

func GetFLOAT64ColumnValues(encoding Encoding, bytes []byte, key, storeName string, padding int, decoder Decoder) (int, DataType, []float64, error) {

	poolIndex, decodedValues, err := decoder.DecodeFLOAT64Values(encoding, bytes, key, storeName, padding)

	if err != nil {

		return -1, Float64, nil, err
	}

	return poolIndex, Float64, decodedValues, nil
}

func AppendINT8Value(position int, value int8, encoding Encoding, values []int8, encoder Encoder) (int, []byte, error) {

	values = append(values, value)

	if position != -1 {

		poolIndex, swappedValues := encoder.MemoryPool.AcquireINT8Pool(len(values))

		values = utils.SwapLastINT8Value(values, swappedValues, position)

		defer encoder.MemoryPool.ReleaseINT8Pool(poolIndex)
	}

	return encoder.EncodeINT8Values(encoding, values, utils.MaxValueBytes)
}

func AppendINT32Value(position int, value int32, encoding Encoding, values []int32, encoder Encoder) (int, []byte, error) {

	values = append(values, value)

	if position != -1 {

		poolIndex, swappedValues := encoder.MemoryPool.AcquireINT32Pool(len(values))

		values = utils.SwapLastINT32Value(values, swappedValues, position)

		defer encoder.MemoryPool.ReleaseINT32Pool(poolIndex)

	}

	return encoder.EncodeINT32Values(values, encoding, Int32, 32, utils.MaxValueBytes)
}

func AppendStringValue(position int, value string, encoding Encoding, values []string, encoder Encoder, fieldName string) (int, []byte, error) {

	values = append(values, value)

	if position != -1 {

		poolIndex, swappedValues := encoder.MemoryPool.AcquireStringPool(len(values))

		values = utils.SwapLastStringValue(values, swappedValues, position)

		defer encoder.MemoryPool.ReleaseStringPool(poolIndex)

	}

	return encoder.EncodeStringValues(values, encoding, utils.MaxValueBytes, fieldName)
}

///////////////// Writer Cache Entries Related Methods //////////////////////////////////

func GetTimeTicks(bytes []byte, padding int, decoder Decoder) (int, []int32, error) {

	var ticks []int32

	index := -1

	length := 0

	offset := 0

	var err error

	length = ReadUINT32Value(bytes, &offset)

	if length > decoder.MemoryPool.GetPoolLength() {

		return -1, nil, errors.New(utils.ErrorCorrupted)
	}

	if length > RLEDeltaEncodingMaxValues {

		index, ticks, err = decoder.DecodeBP32INTValues(bytes[offset:], length, padding)

		if err != nil {

			return -1, nil, err
		}

	} else {

		index, ticks, err = decoder.DecodeRLEDeltaINT32Values(bytes[offset:], padding)

		if err != nil {

			return -1, nil, err
		}

	}

	return index, ticks, nil

}

func AppendTimeTick(tick int32, ticks []interface{}) ([]interface{}, int) {

	position := -1

	if len(ticks) > 0 {

		ticks = append(ticks, tick)

		if !utils.IsSortedINT32InterfaceValues(ticks) {

			utils.SortINT32InterfaceValues(ticks)

			position = utils.SearchINT32InterfaceValue(ticks, tick)

		}

	} else {

		ticks = append(ticks, tick)

	}

	return ticks, position
}

func AppendValue(position int, value interface{}, previousDataType, currentDataType DataType, values []interface{}) []interface{} {

	if currentDataType > previousDataType {

		if previousDataType <= Int64 {

			if currentDataType >= Float8 && currentDataType <= Float64 {

				copy(values[1:], INT64ToFLOATValues(values[1:]))
			}
		}

	} else {

		currentDataType = previousDataType
	}

	values[0] = currentDataType

	values = append(values, value)

	if position != -1 {

		copy(values[1:], utils.SwapLastValue(values[1:], position))
	}

	return values

}

/////////////// Metric and Event batches Methods /////////////////////////////

func writeBlobColumnValue(keyBytes, valueBytes []byte, records uint16, encoder Encoder, store *Store, tokenizer *utils.Tokenizer, encoding Encoding) (int, []byte, error) {

	index := utils.NotAvailable

	var encodedBytes []byte

	if encoding == Zstd {

		index, encodedBytes = encoder.EncodeZstd(valueBytes, 0)

	} else {

		index, encodedBytes = encoder.EncodeSnappy(valueBytes, 0)
	}

	offset, err := store.PutBlob(keyBytes, encodedBytes, tokenizer)

	defer encoder.MemoryPool.ReleaseBytePool(index)

	if err != nil {

		return -1, nil, errors.New(fmt.Sprintf(utils.ErrorWriteKey, string(keyBytes), store.GetName(), err))
	}

	index, encodedBytes = encoder.EncodeBlobValues(records, int64(offset), uint(len(encodedBytes)), 0)

	return index, encodedBytes, nil
}

func WriteBlobColumnValues(event BlobEvent) (int, []byte, error) {

	found, bytes, err := event.Store.Get(event.KeyBytes, event.ValueBytes, event.Encoder, event.DiskIOEvent, event.WaitGroup, event.Tokenizer, true)

	if err != nil {

		return -1, nil, errors.New(fmt.Sprintf(utils.ErrorGetKey, string(event.KeyBytes), event.Store.GetName(), err))
	}

	index, blobBytes := event.Encoder.MemoryPool.AcquireBlobPool(utils.NotAvailable)

	defer event.Encoder.MemoryPool.ReleaseBytePool(index)

	position := 0

	headerIndex, headerBytes := event.Encoder.MemoryPool.AcquireBytePool(utils.NotAvailable)

	headerByteSize := 0

	if found {

		copy(headerBytes[event.Padding:], bytes)

		headerByteSize += len(bytes) + event.Padding

		event.Encoding = GetEncoding(headerBytes[event.Padding])

	} else {

		headerBytes[event.Padding] = byte(Blob) | byte(event.Encoding)

		headerByteSize += 1 + event.Padding
	}

	records := uint16(0)

	for _, value := range event.Values {

		if position+len(value)+3 < utils.MaxBlobBytes {

			index, bytes = event.Encoder.WriteUINT24Value(uint(len(value)), 0)

			copy(blobBytes[position:], bytes)

			event.Encoder.MemoryPool.ReleaseBytePool(index)

			position += 3

			copy(blobBytes[position:], value)

			position += len(value)

			records++

		} else {

			index, bytes, err = writeBlobColumnValue(event.KeyBytes, blobBytes[:position], records, event.Encoder, event.Store, event.Tokenizer, event.Encoding)

			copy(headerBytes[headerByteSize:], bytes)

			headerByteSize += len(bytes)

			event.Encoder.MemoryPool.ReleaseBytePool(index)

			position = 0

			records = 0

			if len(value) > utils.MaxBlobBytes {

				value = value[:utils.MaxBlobBytes]
			}

			index, bytes = event.Encoder.WriteUINT24Value(uint(len(value)), 0)

			copy(blobBytes[position:], bytes)

			event.Encoder.MemoryPool.ReleaseBytePool(index)

			position += 3

			copy(blobBytes[position:], value)

			position += len(value)

			records++

		}

	}

	if records > 0 {

		index, bytes, err = writeBlobColumnValue(event.KeyBytes, blobBytes[:position], records, event.Encoder, event.Store, event.Tokenizer, event.Encoding)

		copy(headerBytes[headerByteSize:], bytes)

		headerByteSize += len(bytes)

		event.Encoder.MemoryPool.ReleaseBytePool(index)

	}

	return headerIndex, headerBytes[:headerByteSize], err
}

func GetBlobColumnValues(event BlobEvent) (int, []string, error) {

	if event.Store == nil {

		return -1, nil, errors.New("store object is nil")
	}

	index := 0

	records := ReadUINT16Value(event.ValueBytes[:2], &index)

	length := ReadUINT24Value(event.ValueBytes, &index)

	offset := ReadINTValue(event.ValueBytes[index:])

	bufferIndex, bufferBytes := event.Encoder.MemoryPool.AcquireBlobPool(length)

	defer event.Decoder.MemoryPool.ReleaseBytePool(bufferIndex)

	bufferBytes, err := event.Store.GetBlob(event.KeyBytes, bufferBytes, offset, event.Tokenizer, event.DiskIOEvent, event.WaitGroup)

	if err != nil {

		dataStoreLogger.Error(fmt.Sprintf("error %v occurred while getting blob key %v ", err, string(event.KeyBytes)))

		return -1, nil, err
	}

	if event.Encoding == Zstd {

		bufferIndex, bufferBytes, err = event.Decoder.DecodeZstd(bufferBytes)

	} else {

		bufferIndex, bufferBytes, err = event.Decoder.DecodeSnappy(bufferBytes)
	}

	if err != nil {
		return -1, nil, errors.New(fmt.Sprintf(utils.ErrorDecodeValues, event.KeyBytes, event.Store.GetName(), err))
	}

	defer event.Encoder.MemoryPool.ReleaseBytePool(bufferIndex)

	index, values := event.Encoder.MemoryPool.AcquireStringPool(records)

	position := 0

	for i := range values {

		length = ReadUINT24Value(bufferBytes, &position)

		values[i] = string(bufferBytes[position : position+length])

		position += length

	}

	return index, values, err
}

func GetColumns(currentDataType DataType, header byte, valueBytes []byte, key, storeName string, padding int, decoder Decoder) (poolIndex int, dataType DataType, int8Values []int8, int16Values []int16, int32Values []int32, int64Values []int64, float64Values []float64, stringValues []string, err error) {

	poolIndex = utils.NotAvailable

	dataType = Invalid

	switch GetDataType(header) {

	case Int8:

		poolIndex, dataType, int8Values, int16Values, int32Values, int64Values, float64Values, err = GetINT8ColumnValues(currentDataType, Int8, valueBytes, GetEncoding(header), key, storeName, padding, decoder)

	case Int16:

		poolIndex, dataType, int16Values, int32Values, int64Values, float64Values, err = GetINT16ColumnValues(currentDataType, Int16, GetEncoding(header), valueBytes, key, storeName, padding, decoder)

	case Int24:

		poolIndex, dataType, int32Values, int64Values, float64Values, err = GetINT24ColumnValues(currentDataType, Int24, GetEncoding(header), valueBytes, key, storeName, padding, decoder)

	case Int32:

		poolIndex, dataType, int32Values, int64Values, float64Values, err = GetINT32ColumnValues(currentDataType, Int32, GetEncoding(header), valueBytes, key, storeName, padding, decoder)

	case Int40:

		poolIndex, dataType, int64Values, float64Values, err = GetINT40ColumnValues(currentDataType, Int40, GetEncoding(header), valueBytes, key, storeName, padding, decoder)

	case Int48:

		poolIndex, dataType, int64Values, float64Values, err = GetINT48ColumnValues(currentDataType, Int48, GetEncoding(header), valueBytes, key, storeName, padding, decoder)

	case Int56:

		poolIndex, dataType, int64Values, float64Values, err = GetINT56ColumnValues(currentDataType, Int56, GetEncoding(header), valueBytes, key, storeName, padding, decoder)

	case Int64:

		poolIndex, dataType, int64Values, float64Values, err = GetINT64ColumnValues(currentDataType, Int64, GetEncoding(header), valueBytes, key, storeName, padding, decoder)

	case Float8:

		poolIndex, dataType, float64Values, err = GetFLOAT8ColumnValues(currentDataType, Float8, GetEncoding(header), valueBytes, key, storeName, padding, decoder)

	case Float16:

		poolIndex, dataType, float64Values, err = GetFLOAT16ColumnValues(currentDataType, Float16, GetEncoding(header), valueBytes, key, storeName, padding, decoder)

	case Float64:

		poolIndex, dataType, float64Values, err = GetFLOAT64ColumnValues(GetEncoding(header), valueBytes, key, storeName, padding, decoder)

	case String:

		poolIndex, dataType, stringValues, err = GetStringColumnValues(GetEncoding(header), valueBytes, key, storeName, padding, decoder)

	default:
		err = errors.New(fmt.Sprintf(utils.ErrorInvalidDataType, nil, key, storeName))
	}

	return
}

func GetStringColumnValues(encoding Encoding, valueBytes []byte, key, storeName string, length int, decoder Decoder) (poolIndex int, dataType DataType, stringValues []string, err error) {

	poolIndex, stringValues, err = decoder.DecodeStringValues(encoding, valueBytes, key, storeName, length)

	if err != nil {

		return utils.NotAvailable, Invalid, nil, err
	}

	return poolIndex, String, stringValues, err
}

func GetBlobEncoding(sum, len int) Encoding {

	if len != 0 && sum/len > utils.BlobEncodingBytes {

		return Zstd
	}

	return Snappy
}

func IsUnSupportedBackupType(datastoreType utils.DatastoreType) bool {

	return datastoreType == utils.Flow || datastoreType == utils.FlowAggregation ||
		datastoreType == utils.FlowIndex || datastoreType == utils.HealthMetricIndex ||
		datastoreType == utils.HealthMetric || datastoreType == utils.CorrelationMetric ||
		datastoreType == utils.CorrelatedMetricIndex || datastoreType == utils.PolicyFlapHistory ||
		datastoreType == utils.MetricPolicy || datastoreType == utils.EventPolicy ||
		datastoreType == utils.PolicyResult || datastoreType == utils.PolicyAggregation ||
		datastoreType == utils.PolicyIndex || datastoreType == utils.RunbookWorklog
}
