/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/* Change Logs:
* Date                   Author          		Notes
* 2025-05-09  			 Hardik Vala			MOTADATA-4911 Query2.0 First Merge
 */

package query

import (
	"github.com/kelindar/bitmap"
	. "motadatadatastore/codec"
	"motadatadatastore/utils"
)

/*--------------------------------------------- VERTICAL -----------------------------------------------*/

func (worker *Worker) processVerticalINT32Func(updatePosition bool, position, eventId, executorId int, ticks, values []int32, column string, dataType DataType) (bool, int) {

	event := worker.WorkerEvents[eventId]

	if worker.granularity == utils.NotAvailable {

		if worker.lastFunc {

			valid := true

			if worker.timeBoundQuery {

				valid = worker.evaluateLastTick(ticks[len(ticks)-1], eventId, executorId)
			}

			if valid {

				event.currentIndex = worker.setColumnContext(executorId, column+"^last", dataType, false)

				if worker.columnDataTypes[executorId][event.currentIndex] == Int64 {

					worker.evaluateLastValueAggregationFuncINT(int64(values[len(values)-1]), eventId, executorId)

				} else if worker.columnDataTypes[executorId][event.currentIndex] == Float64 {

					worker.evaluateLastValueAggregationFuncFLOAT(float64(values[len(values)-1]), eventId, executorId)

				} else {

					worker.evaluateLastValueAggregationFuncString(INT32ToStringValue(values[len(values)-1]), eventId, executorId)
				}
			}
		}

		minValue, maxValue, sum := int64(0), int64(0), int64(0)

		if worker.aggregationFunc == MinMaxSumCount {

			minValue, maxValue, sum = utils.MinMaxSumINT32(values)

			event.currentIndex = event.minAggregationColumnIndex

			if worker.columnDataTypes[executorId][event.minAggregationColumnIndex] == Int64 {

				worker.evaluateMinAggregationFuncINT(minValue, eventId, executorId)

			} else {

				worker.evaluateMinAggregationFuncFLOAT(float64(minValue), eventId, executorId)
			}

			event.currentIndex = event.maxAggregationColumnIndex

			if worker.columnDataTypes[executorId][event.maxAggregationColumnIndex] == Int64 {

				worker.evaluateMaxAggregationFuncINT(maxValue, eventId, executorId)

			} else {

				worker.evaluateMaxAggregationFuncFLOAT(float64(maxValue), eventId, executorId)
			}

			event.currentIndex = event.sumAggregationColumnIndex

			if worker.columnDataTypes[executorId][event.sumAggregationColumnIndex] == Int64 {

				worker.evaluateSumAggregationFuncINT(sum, eventId, executorId)

			} else {

				worker.evaluateSumAggregationFuncFLOAT(float64(sum), eventId, executorId)
			}

			event.countIndex = event.countAggregationColumnIndex

			worker.evaluateCountAggregationFunc(int64(len(values)), eventId, executorId)

		} else if worker.aggregationFunc == MinMaxSum {

			minValue, maxValue, sum = utils.MinMaxSumINT32(values)

			event.currentIndex = event.minAggregationColumnIndex

			if worker.columnDataTypes[executorId][event.minAggregationColumnIndex] == Int64 {

				worker.evaluateMinAggregationFuncINT(minValue, eventId, executorId)

			} else {

				worker.evaluateMinAggregationFuncFLOAT(float64(minValue), eventId, executorId)
			}

			event.currentIndex = event.maxAggregationColumnIndex

			if worker.columnDataTypes[executorId][event.maxAggregationColumnIndex] == Int64 {

				worker.evaluateMaxAggregationFuncINT(maxValue, eventId, executorId)

			} else {

				worker.evaluateMaxAggregationFuncFLOAT(float64(maxValue), eventId, executorId)
			}

			event.currentIndex = event.sumAggregationColumnIndex

			if worker.columnDataTypes[executorId][event.sumAggregationColumnIndex] == Int64 {

				worker.evaluateSumAggregationFuncINT(sum, eventId, executorId)

			} else {

				worker.evaluateSumAggregationFuncFLOAT(float64(sum), eventId, executorId)
			}

		} else if worker.aggregationFunc == MinMaxCount {

			minValue, maxValue = utils.MinMaxINT32(values)

			event.currentIndex = event.minAggregationColumnIndex

			if worker.columnDataTypes[executorId][event.minAggregationColumnIndex] == Int64 {

				worker.evaluateMinAggregationFuncINT(minValue, eventId, executorId)

			} else {

				worker.evaluateMinAggregationFuncFLOAT(float64(minValue), eventId, executorId)
			}

			event.currentIndex = event.maxAggregationColumnIndex

			if worker.columnDataTypes[executorId][event.maxAggregationColumnIndex] == Int64 {

				worker.evaluateMaxAggregationFuncINT(maxValue, eventId, executorId)

			} else {

				worker.evaluateMaxAggregationFuncFLOAT(float64(maxValue), eventId, executorId)
			}

			event.countIndex = event.countAggregationColumnIndex

			worker.evaluateCountAggregationFunc(int64(len(values)), eventId, executorId)

		} else if worker.aggregationFunc == MinMax {

			minValue, maxValue = utils.MinMaxINT32(values)

			event.currentIndex = event.minAggregationColumnIndex

			if worker.columnDataTypes[executorId][event.minAggregationColumnIndex] == Int64 {

				worker.evaluateMinAggregationFuncINT(minValue, eventId, executorId)

			} else {

				worker.evaluateMinAggregationFuncFLOAT(float64(minValue), eventId, executorId)
			}

			event.currentIndex = event.maxAggregationColumnIndex

			if worker.columnDataTypes[executorId][event.maxAggregationColumnIndex] == Int64 {

				worker.evaluateMaxAggregationFuncINT(maxValue, eventId, executorId)

			} else {

				worker.evaluateMaxAggregationFuncFLOAT(float64(maxValue), eventId, executorId)
			}

		} else if worker.aggregationFunc == MinSumCount {

			minValue, _, sum = utils.MinMaxSumINT32(values)

			event.currentIndex = event.minAggregationColumnIndex

			if worker.columnDataTypes[executorId][event.minAggregationColumnIndex] == Int64 {

				worker.evaluateMinAggregationFuncINT(minValue, eventId, executorId)

			} else {

				worker.evaluateMinAggregationFuncFLOAT(float64(minValue), eventId, executorId)
			}

			event.currentIndex = event.sumAggregationColumnIndex

			if worker.columnDataTypes[executorId][event.sumAggregationColumnIndex] == Int64 {

				worker.evaluateSumAggregationFuncINT(sum, eventId, executorId)

			} else {

				worker.evaluateSumAggregationFuncFLOAT(float64(sum), eventId, executorId)
			}

			event.countIndex = event.countAggregationColumnIndex

			worker.evaluateCountAggregationFunc(int64(len(values)), eventId, executorId)

		} else if worker.aggregationFunc == MinSum {

			minValue, _, sum = utils.MinMaxSumINT32(values)

			event.currentIndex = event.minAggregationColumnIndex

			if worker.columnDataTypes[executorId][event.minAggregationColumnIndex] == Int64 {

				worker.evaluateMinAggregationFuncINT(minValue, eventId, executorId)

			} else {

				worker.evaluateMinAggregationFuncFLOAT(float64(minValue), eventId, executorId)
			}

			event.currentIndex = event.sumAggregationColumnIndex

			if worker.columnDataTypes[executorId][event.sumAggregationColumnIndex] == Int64 {

				worker.evaluateSumAggregationFuncINT(sum, eventId, executorId)

			} else {

				worker.evaluateSumAggregationFuncFLOAT(float64(sum), eventId, executorId)
			}

		} else if worker.aggregationFunc == MinCount {

			minValue = utils.MinINT32(values)

			event.currentIndex = event.minAggregationColumnIndex

			if worker.columnDataTypes[executorId][event.minAggregationColumnIndex] == Int64 {

				worker.evaluateMinAggregationFuncINT(minValue, eventId, executorId)

			} else {

				worker.evaluateMinAggregationFuncFLOAT(float64(minValue), eventId, executorId)
			}

			event.countIndex = event.countAggregationColumnIndex

			worker.evaluateCountAggregationFunc(int64(len(values)), eventId, executorId)

		} else if worker.aggregationFunc == Min {

			minValue = utils.MinINT32(values)

			event.currentIndex = event.minAggregationColumnIndex

			if worker.columnDataTypes[executorId][event.minAggregationColumnIndex] == Int64 {

				worker.evaluateMinAggregationFuncINT(minValue, eventId, executorId)

			} else {

				worker.evaluateMinAggregationFuncFLOAT(float64(minValue), eventId, executorId)
			}

		} else if worker.aggregationFunc == MaxSumCount {

			_, maxValue, sum = utils.MinMaxSumINT32(values)

			event.currentIndex = event.maxAggregationColumnIndex

			if worker.columnDataTypes[executorId][event.maxAggregationColumnIndex] == Int64 {

				worker.evaluateMaxAggregationFuncINT(maxValue, eventId, executorId)

			} else {

				worker.evaluateMaxAggregationFuncFLOAT(float64(maxValue), eventId, executorId)
			}

			event.currentIndex = event.sumAggregationColumnIndex

			if worker.columnDataTypes[executorId][event.sumAggregationColumnIndex] == Int64 {

				worker.evaluateSumAggregationFuncINT(sum, eventId, executorId)

			} else {

				worker.evaluateSumAggregationFuncFLOAT(float64(sum), eventId, executorId)
			}

			event.countIndex = event.countAggregationColumnIndex

			worker.evaluateCountAggregationFunc(int64(len(values)), eventId, executorId)

		} else if worker.aggregationFunc == MaxSum {

			_, maxValue, sum = utils.MinMaxSumINT32(values)

			event.currentIndex = event.maxAggregationColumnIndex

			if worker.columnDataTypes[executorId][event.maxAggregationColumnIndex] == Int64 {

				worker.evaluateMaxAggregationFuncINT(maxValue, eventId, executorId)

			} else {

				worker.evaluateMaxAggregationFuncFLOAT(float64(maxValue), eventId, executorId)
			}

			event.currentIndex = event.sumAggregationColumnIndex

			if worker.columnDataTypes[executorId][event.sumAggregationColumnIndex] == Int64 {

				worker.evaluateSumAggregationFuncINT(sum, eventId, executorId)

			} else {

				worker.evaluateSumAggregationFuncFLOAT(float64(sum), eventId, executorId)
			}

		} else if worker.aggregationFunc == MaxCount {

			maxValue = utils.MaxINT32(values)

			event.currentIndex = event.maxAggregationColumnIndex

			if worker.columnDataTypes[executorId][event.maxAggregationColumnIndex] == Int64 {

				worker.evaluateMaxAggregationFuncINT(maxValue, eventId, executorId)

			} else {

				worker.evaluateMaxAggregationFuncFLOAT(float64(maxValue), eventId, executorId)
			}

			event.countIndex = event.countAggregationColumnIndex

			worker.evaluateCountAggregationFunc(int64(len(values)), eventId, executorId)

		} else if worker.aggregationFunc == Max {

			maxValue = utils.MaxINT32(values)

			event.currentIndex = event.maxAggregationColumnIndex

			if worker.columnDataTypes[executorId][event.maxAggregationColumnIndex] == Int64 {

				worker.evaluateMaxAggregationFuncINT(maxValue, eventId, executorId)

			} else {

				worker.evaluateMaxAggregationFuncFLOAT(float64(maxValue), eventId, executorId)
			}

		} else if worker.aggregationFunc == SumCount {

			sum = utils.SumINT32(values)

			event.currentIndex = event.sumAggregationColumnIndex

			if worker.columnDataTypes[executorId][event.sumAggregationColumnIndex] == Int64 {

				worker.evaluateSumAggregationFuncINT(sum, eventId, executorId)

			} else {

				worker.evaluateSumAggregationFuncFLOAT(float64(sum), eventId, executorId)
			}

			event.countIndex = event.countAggregationColumnIndex

			worker.evaluateCountAggregationFunc(int64(len(values)), eventId, executorId)

		} else if worker.aggregationFunc == Sum {

			sum = utils.SumINT32(values)

			event.currentIndex = event.sumAggregationColumnIndex

			if worker.columnDataTypes[executorId][event.sumAggregationColumnIndex] == Int64 {

				worker.evaluateSumAggregationFuncINT(sum, eventId, executorId)

			} else {

				worker.evaluateSumAggregationFuncFLOAT(float64(sum), eventId, executorId)
			}

		} else if worker.aggregationFunc == Count {

			event.countIndex = event.countAggregationColumnIndex

			worker.evaluateCountAggregationFunc(int64(len(values)), eventId, executorId)

		}

	} else {

		if worker.aggregationFunc == 0 { // means we need raw data...

			updatePosition, position = worker.setCurrentPosition(eventId, executorId, position, ticks, updatePosition)

			worker.setVerticalHistoricalValueColumnINT32(eventId, executorId, column, values)

		} else {

			if worker.aggregationFunc == SumCount || worker.avgFunc {

				if worker.minFunc {

					event.currentIndex = event.minAggregationColumnIndex

					if worker.columnDataTypes[executorId][event.minAggregationColumnIndex] == Int64 {

						worker.evaluateVerticalMinHistogramFuncINT32(ticks, values, eventId, executorId)

					} else {

						worker.evaluateVerticalHistogramFuncFLOATINT32(ticks, values, eventId, executorId, utils.Min)
					}

				}

				if worker.maxFunc {

					event.currentIndex = event.maxAggregationColumnIndex

					if worker.columnDataTypes[executorId][event.maxAggregationColumnIndex] == Int64 {

						worker.evaluateVerticalMaxHistogramFuncINT32(ticks, values, eventId, executorId)

					} else {

						worker.evaluateVerticalHistogramFuncFLOATINT32(ticks, values, eventId, executorId, utils.Max)
					}
				}

				event.currentIndex = event.sumAggregationColumnIndex

				event.countIndex = event.countAggregationColumnIndex

				if worker.columnDataTypes[executorId][event.sumAggregationColumnIndex] == Int64 {

					worker.evaluateVerticalMeanHistogramFuncINT32(ticks, values, eventId, executorId)

				} else {

					poolIndex, float64Values := worker.memoryPools[executorId].AcquireFLOAT64Pool(len(values))

					worker.memoryPools[executorId].ResetFLOAT64Pool(poolIndex, len(values), utils.DummyFLOAT64Value)

					INT32ToFLOAT64Values(values, float64Values)

					worker.evaluateVerticalMeanHistogramFuncFLOAT(ticks, float64Values, eventId, executorId)

					worker.memoryPools[executorId].ReleaseFLOAT64Pool(poolIndex)
				}

			} else {

				if worker.sumFunc {

					event.currentIndex = event.sumAggregationColumnIndex

					if worker.columnDataTypes[executorId][event.sumAggregationColumnIndex] == Int64 {

						worker.evaluateVerticalSumHistogramFuncINT32(ticks, values, eventId, executorId)

					} else {

						worker.evaluateVerticalHistogramFuncFLOATINT32(ticks, values, eventId, executorId, utils.Sum)
					}

				}

				if worker.countFunc {

					event.countIndex = event.countAggregationColumnIndex

					worker.evaluateVerticalCountHistogramFunc(ticks, eventId, executorId)

				}

				if worker.minFunc {

					event.currentIndex = event.minAggregationColumnIndex

					if worker.columnDataTypes[executorId][event.minAggregationColumnIndex] == Int64 {

						worker.evaluateVerticalMinHistogramFuncINT32(ticks, values, eventId, executorId)

					} else {

						worker.evaluateVerticalHistogramFuncFLOATINT32(ticks, values, eventId, executorId, utils.Min)
					}

				}

				if worker.maxFunc {

					event.currentIndex = event.maxAggregationColumnIndex

					if worker.columnDataTypes[executorId][event.maxAggregationColumnIndex] == Int64 {

						worker.evaluateVerticalMaxHistogramFuncINT32(ticks, values, eventId, executorId)

					} else {

						worker.evaluateVerticalHistogramFuncFLOATINT32(ticks, values, eventId, executorId, utils.Max)
					}
				}

			}
		}
	}

	return updatePosition, position
}

// INT32 histogram functions

func (worker *Worker) evaluateVerticalMeanHistogramFuncINT32(ticks, values []int32, eventId, executorId int) {

	event := worker.WorkerEvents[eventId]

	sumFuncValues := worker.memoryPools[executorId].GetINT64Pool(worker.columnPoolIndices[executorId][event.currentIndex])

	countFuncValues := worker.memoryPools[executorId].GetINT64Pool(worker.columnPoolIndices[executorId][event.countIndex])

	if worker.groupElementSize == 0 {

		index := 0

		for i := range ticks {

			index = int(ticks[i]-worker.startTick) / worker.granularity

			if index > len(sumFuncValues) {

				sumFuncValues = worker.memoryPools[executorId].ExpandINT64Pool(worker.columnPoolIndices[executorId][event.currentIndex], index+utils.OverflowLength)

				countFuncValues = worker.memoryPools[executorId].ExpandINT64Pool(worker.columnPoolIndices[executorId][event.countIndex], index+utils.OverflowLength)
			}

			if sumFuncValues[index] == utils.DummyINT64Value {

				sumFuncValues[index] = int64(values[i])

				countFuncValues[index] = 1
			} else {

				sumFuncValues[index] += int64(values[i])

				countFuncValues[index] += 1
			}
		}

	} else {

		executor := worker.executors[executorId]

		if worker.externalGrouping {

			if groups, ok := worker.externalGroupFilterOrdinals[event.group]; ok {

				groups.Iterate(func(group uint32) {

					worker.evaluateVerticalMeanHistogramFuncINT32Helper(event, executor.externalGroupFilters[group], executor.externalGroupHashes[group], sumFuncValues, countFuncValues, values, ticks, executorId)
				})
			}

		} else {

			worker.evaluateVerticalMeanHistogramFuncINT32Helper(event, event.group, event.groupOrdinal, sumFuncValues, countFuncValues, values, ticks, executorId)
		}
	}

}

func (worker *Worker) evaluateVerticalMeanHistogramFuncINT32Helper(event *WorkerEvent, group string, groupOrdinal uint64, sumFuncValues, countFuncValues []int64, values, ticks []int32, executorId int) {

	position := 0

	ok := false

	offset := (int(worker.endTick-worker.startTick) / worker.granularity) + 1

	if position, ok = worker.columnGroups[executorId].Get(groupOrdinal); !ok {

		position = worker.memoryPoolPositions[executorId]

		worker.columnGroups[executorId].Put(groupOrdinal, position)

		worker.resolvedColumnGroups[position/offset] = group

		worker.memoryPoolPositions[executorId] = position + offset

	}

	if worker.columnGroups[executorId].Len()*offset > len(sumFuncValues) {

		sumFuncValues = worker.memoryPools[executorId].ExpandINT64Pool(worker.columnPoolIndices[executorId][event.currentIndex], worker.columnGroups[executorId].Len()*offset)

		countFuncValues = worker.memoryPools[executorId].ExpandINT64Pool(worker.columnPoolIndices[executorId][event.countIndex], worker.columnGroups[executorId].Len()*offset)
	}

	index := 0

	for i := range ticks {

		index = int(ticks[i]-worker.startTick) / worker.granularity

		if sumFuncValues[index+position] == utils.DummyINT64Value {

			sumFuncValues[index+position] = int64(values[i])

			countFuncValues[index+position] = 1
		} else {

			sumFuncValues[index+position] += int64(values[i])

			countFuncValues[index+position] += 1
		}
	}
}

func (worker *Worker) evaluateVerticalMinHistogramFuncINT32(ticks, values []int32, eventId, executorId int) {

	event := worker.WorkerEvents[eventId]

	minFuncValues := worker.memoryPools[executorId].GetINT64Pool(worker.columnPoolIndices[executorId][event.currentIndex])

	if worker.groupElementSize == 0 {

		index := 0

		for i := range ticks {

			index = int(ticks[i]-worker.startTick) / worker.granularity

			if index > len(minFuncValues) {

				minFuncValues = worker.memoryPools[executorId].ExpandINT64Pool(worker.columnPoolIndices[executorId][event.currentIndex], index+utils.OverflowLength)
			}

			if minFuncValues[index] == utils.DummyINT64Value {

				minFuncValues[index] = int64(values[i])

			} else {

				value := int64(values[i])

				if value < minFuncValues[index] {

					minFuncValues[index] = value
				}
			}

		}

	} else {

		executor := worker.executors[executorId]

		if worker.externalGrouping {

			if groups, ok := worker.externalGroupFilterOrdinals[event.group]; ok {

				groups.Iterate(func(group uint32) {

					worker.evaluateVerticalHistogramFuncINT32Helper(event, executor.externalGroupFilters[group], executor.externalGroupHashes[group], minFuncValues, values, ticks, executorId, utils.Min)
				})
			}
		} else {

			worker.evaluateVerticalHistogramFuncINT32Helper(event, event.group, event.groupOrdinal, minFuncValues, values, ticks, executorId, utils.Min)
		}
	}

}

func (worker *Worker) evaluateVerticalMaxHistogramFuncINT32(ticks, values []int32, eventId, executorId int) {

	event := worker.WorkerEvents[eventId]

	maxFuncValues := worker.memoryPools[executorId].GetINT64Pool(worker.columnPoolIndices[executorId][event.currentIndex])

	if worker.groupElementSize == 0 {

		index := 0

		for i := range ticks {

			index = int(ticks[i]-worker.startTick) / worker.granularity

			if index > len(maxFuncValues) {

				maxFuncValues = worker.memoryPools[executorId].ExpandINT64Pool(worker.columnPoolIndices[executorId][event.currentIndex], index+utils.OverflowLength)
			}

			if maxFuncValues[index] == utils.DummyINT64Value {

				maxFuncValues[index] = int64(values[i])

			} else {

				value := int64(values[i])

				if value > maxFuncValues[index] {

					maxFuncValues[index] = value
				}
			}

		}

	} else {

		executor := worker.executors[executorId]

		if worker.externalGrouping {

			if groups, ok := worker.externalGroupFilterOrdinals[event.group]; ok {

				groups.Iterate(func(group uint32) {

					worker.evaluateVerticalHistogramFuncINT32Helper(event, executor.externalGroupFilters[group], executor.externalGroupHashes[group], maxFuncValues, values, ticks, executorId, utils.Max)
				})
			}
		} else {

			worker.evaluateVerticalHistogramFuncINT32Helper(event, event.group, event.groupOrdinal, maxFuncValues, values, ticks, executorId, utils.Max)
		}
	}

}

func (worker *Worker) evaluateVerticalSumHistogramFuncINT32(ticks, values []int32, eventId, executorId int) {

	event := worker.WorkerEvents[eventId]

	sumFuncValues := worker.memoryPools[executorId].GetINT64Pool(worker.columnPoolIndices[executorId][event.currentIndex])

	if worker.groupElementSize == 0 {

		index := 0

		for i := range ticks {

			index = int(ticks[i]-worker.startTick) / worker.granularity

			if index > len(sumFuncValues) {

				sumFuncValues = worker.memoryPools[executorId].ExpandINT64Pool(worker.columnPoolIndices[executorId][event.currentIndex], index+utils.OverflowLength)
			}

			if sumFuncValues[index] == utils.DummyINT64Value {

				sumFuncValues[index] = int64(values[i])

			} else {

				sumFuncValues[index] += int64(values[i])
			}

		}

	} else {

		executor := worker.executors[executorId]

		if worker.externalGrouping {

			if groups, ok := worker.externalGroupFilterOrdinals[event.group]; ok {

				groups.Iterate(func(group uint32) {

					worker.evaluateVerticalHistogramFuncINT32Helper(event, executor.externalGroupFilters[group], executor.externalGroupHashes[group], sumFuncValues, values, ticks, executorId, utils.Sum)
				})
			}
		} else {

			worker.evaluateVerticalHistogramFuncINT32Helper(event, event.group, event.groupOrdinal, sumFuncValues, values, ticks, executorId, utils.Sum)
		}
	}

}

func (worker *Worker) evaluateVerticalHistogramFuncINT32Helper(event *WorkerEvent, group string, groupOrdinal uint64, int64Values []int64, decodedValues, ticks []int32, executorId int, aggregationFunc string) {

	position := 0

	ok := false

	offset := (int(worker.endTick-worker.startTick) / worker.granularity) + 1

	if position, ok = worker.columnGroups[executorId].Get(groupOrdinal); !ok {

		position = worker.memoryPoolPositions[executorId]

		worker.columnGroups[executorId].Put(groupOrdinal, position)

		worker.resolvedColumnGroups[position/offset] = group

		worker.memoryPoolPositions[executorId] = position + offset

	}

	if worker.columnGroups[executorId].Len()*offset > len(int64Values) {

		int64Values = worker.memoryPools[executorId].ExpandINT64Pool(worker.columnPoolIndices[executorId][event.currentIndex], worker.columnGroups[executorId].Len()*offset)

	}

	index := 0

	switch aggregationFunc {

	case utils.Min:

		for i := range ticks {

			index = int(ticks[i]-worker.startTick) / worker.granularity

			value := int64(decodedValues[i])

			if int64Values[index+position] == utils.DummyINT64Value {

				int64Values[index+position] = value

			} else {

				if value < int64Values[index+position] {

					int64Values[index+position] = value
				}
			}

		}

	case utils.Max:

		for i := range ticks {

			index = int(ticks[i]-worker.startTick) / worker.granularity

			value := int64(decodedValues[i])

			if int64Values[index+position] == utils.DummyINT64Value {

				int64Values[index+position] = value

			} else {

				if value > int64Values[index+position] {

					int64Values[index+position] = value
				}
			}

		}

	case utils.Sum:

		for i := range ticks {

			index = int(ticks[i]-worker.startTick) / worker.granularity

			value := int64(decodedValues[i])

			if int64Values[index+position] == utils.DummyINT64Value {

				int64Values[index+position] = value

			} else {

				int64Values[index+position] += value
			}

		}

	case utils.Count:

		for i := range ticks {

			index = int(ticks[i]-worker.startTick) / worker.granularity

			if int64Values[index+position] == utils.DummyINT64Value {

				int64Values[index+position] = 1

			} else {

				int64Values[index+position] += 1
			}

		}
	}
}

func (worker *Worker) evaluateVerticalHistogramFuncFLOATINT32(ticks, values []int32, eventId, executorId int, aggregator string) {

	poolIndex, float64Values := worker.memoryPools[executorId].AcquireFLOAT64Pool(len(values))

	worker.memoryPools[executorId].ResetFLOAT64Pool(poolIndex, len(values), utils.DummyFLOAT64Value)

	defer worker.memoryPools[executorId].ReleaseFLOAT64Pool(poolIndex)

	INT32ToFLOAT64Values(values, float64Values)

	switch aggregator {

	case utils.Min:

		worker.evaluateVerticalMinHistogramFuncFLOAT(ticks, float64Values, eventId, executorId)

	case utils.Max:

		worker.evaluateVerticalMaxHistogramFuncFLOAT(ticks, float64Values, eventId, executorId)

	case utils.Sum:

		worker.evaluateVerticalSumHistogramFuncFLOAT(ticks, float64Values, eventId, executorId)

	}

	return
}

/*-----------------------------------------------------Drill-Down Functions -----------------------------------------------------------*/

func (worker *Worker) setVerticalHistoricalValueColumnINT32(eventId, executorId int, column string, values []int32) {

	event := worker.WorkerEvents[eventId]

	event.currentIndex = worker.setColumnContext(executorId, column+utils.ValueSuffix, Int32, false)

	position := worker.memoryPoolPositions[executorId]

	if worker.columnDataTypes[executorId][event.currentIndex] == Int64 {

		int64Values := worker.memoryPools[executorId].GetINT64Pool(worker.columnPoolIndices[executorId][event.currentIndex])

		if position+len(values) > len(int64Values) {

			int64Values = worker.memoryPools[executorId].ExpandINT64Pool(worker.columnPoolIndices[executorId][event.currentIndex], len(int64Values)+utils.OverflowLength)
		}

		if len(worker.positions) == len(values) {

			for i := range values {

				int64Values[position] = int64(values[i])

				position++
			}
		} else {

			for tick, indexes := range worker.currentPositions {

				if tickPositions, ok := worker.positions[tick]; ok {

					for index, tickPosition := range tickPositions {

						int64Values[tickPosition] = int64(values[indexes[index]])
					}
				}
			}
		}

	} else {

		float64Values := worker.memoryPools[executorId].GetFLOAT64Pool(worker.columnPoolIndices[executorId][event.currentIndex])

		if position+len(values) > len(float64Values) {

			float64Values = worker.memoryPools[executorId].ExpandFLOAT64Pool(worker.columnPoolIndices[executorId][event.currentIndex], len(float64Values)+utils.OverflowLength)
		}

		if len(worker.positions) == len(values) {

			for i := range values {

				float64Values[position] = float64(values[i])

				position++
			}
		} else {

			for tick, indexes := range worker.currentPositions {

				if tickPositions, ok := worker.positions[tick]; ok {

					for index, tickPosition := range tickPositions {

						float64Values[tickPosition] = float64(values[indexes[index]])
					}
				}
			}
		}
	}

}

/*--------------------------------------------- HORIZONTAL -----------------------------------------------*/

func (worker *Worker) processHorizontalINT32Func(executorId, eventId int, values []int32, groups []string, column string, dataType DataType, updatePosition bool, position int) (bool, int) {

	if worker.batchSize == 0 {

		worker.batchSize = len(values)
	}

	event := worker.WorkerEvents[eventId]

	var groupOrdinals []uint64

	if event.grouping {

		groupOrdinals = worker.memoryPools[executorId].GetUINT64Pool(worker.groupOrdinalPoolIndex)
	}

	if worker.granularity == utils.NotAvailable {

		if event.grouping && event.condition {

			size, _ := worker.conditionBitmaps[executorId].Max()

			length := int(size + 1)

			iterateAll := false

			// if condition operand array size is 10 and max bit is 10 length is 10 but aggregated operand array size is 8 than need this condition
			if length > len(values) {

				iterateAll = true

				length = len(values)
			}

			if !iterateAll && length >= len(values)-((len(values)*conditionIterateThreshold)/100) {

				iterateAll = true
			}

			if worker.aggregationFunc == MinMaxSumCount {

				if iterateAll {

					for i := 0; i < length; i++ {

						if worker.conditionBitmaps[executorId].Contains(uint32(i)) {

							event.group = groups[i]
							event.groupOrdinal = groupOrdinals[i]

							event.currentIndex = event.minAggregationColumnIndex

							worker.evaluateMinAggregationFuncINT(int64(values[i]), eventId, executorId)

							event.currentIndex = event.maxAggregationColumnIndex

							worker.evaluateMaxAggregationFuncINT(int64(values[i]), eventId, executorId)

							event.currentIndex = event.sumAggregationColumnIndex

							worker.evaluateSumAggregationFuncINT(int64(values[i]), eventId, executorId)

							event.countIndex = event.countAggregationColumnIndex

							worker.evaluateCountAggregationFunc(1, eventId, executorId)
						}
					}

				} else {

					worker.conditionBitmaps[executorId].Range(func(i uint32) {

						event.group = groups[i]
						event.groupOrdinal = groupOrdinals[i]

						event.currentIndex = event.minAggregationColumnIndex

						worker.evaluateMinAggregationFuncINT(int64(values[i]), eventId, executorId)

						event.currentIndex = event.maxAggregationColumnIndex

						worker.evaluateMaxAggregationFuncINT(int64(values[i]), eventId, executorId)

						event.currentIndex = event.sumAggregationColumnIndex

						worker.evaluateSumAggregationFuncINT(int64(values[i]), eventId, executorId)

						event.countIndex = event.countAggregationColumnIndex

						worker.evaluateCountAggregationFunc(1, eventId, executorId)
					})
				}

			} else if worker.aggregationFunc == MinMaxSum {

				if iterateAll {

					for i := 0; i < length; i++ {

						if worker.conditionBitmaps[executorId].Contains(uint32(i)) {

							event.group = groups[i]
							event.groupOrdinal = groupOrdinals[i]

							event.currentIndex = event.minAggregationColumnIndex

							worker.evaluateMinAggregationFuncINT(int64(values[i]), eventId, executorId)

							event.currentIndex = event.maxAggregationColumnIndex

							worker.evaluateMaxAggregationFuncINT(int64(values[i]), eventId, executorId)

							event.currentIndex = event.sumAggregationColumnIndex

							worker.evaluateSumAggregationFuncINT(int64(values[i]), eventId, executorId)
						}
					}
				} else {

					worker.conditionBitmaps[executorId].Range(func(i uint32) {

						event.group = groups[i]
						event.groupOrdinal = groupOrdinals[i]

						event.currentIndex = event.minAggregationColumnIndex

						worker.evaluateMinAggregationFuncINT(int64(values[i]), eventId, executorId)

						event.currentIndex = event.maxAggregationColumnIndex

						worker.evaluateMaxAggregationFuncINT(int64(values[i]), eventId, executorId)

						event.currentIndex = event.sumAggregationColumnIndex

						worker.evaluateSumAggregationFuncINT(int64(values[i]), eventId, executorId)
					})
				}

			} else if worker.aggregationFunc == MinMaxCount {

				if iterateAll {

					for i := 0; i < length; i++ {

						if worker.conditionBitmaps[executorId].Contains(uint32(i)) {

							event.group = groups[i]
							event.groupOrdinal = groupOrdinals[i]

							event.currentIndex = event.minAggregationColumnIndex

							worker.evaluateMinAggregationFuncINT(int64(values[i]), eventId, executorId)

							event.currentIndex = event.maxAggregationColumnIndex

							worker.evaluateMaxAggregationFuncINT(int64(values[i]), eventId, executorId)

							event.countIndex = event.countAggregationColumnIndex

							worker.evaluateCountAggregationFunc(1, eventId, executorId)
						}
					}
				} else {

					worker.conditionBitmaps[executorId].Range(func(i uint32) {

						event.group = groups[i]
						event.groupOrdinal = groupOrdinals[i]

						event.currentIndex = event.minAggregationColumnIndex

						worker.evaluateMinAggregationFuncINT(int64(values[i]), eventId, executorId)

						event.currentIndex = event.maxAggregationColumnIndex

						worker.evaluateMaxAggregationFuncINT(int64(values[i]), eventId, executorId)

						event.countIndex = event.countAggregationColumnIndex

						worker.evaluateCountAggregationFunc(1, eventId, executorId)
					})
				}

			} else if worker.aggregationFunc == MinMax {

				if iterateAll {

					for i := 0; i < length; i++ {

						if worker.conditionBitmaps[executorId].Contains(uint32(i)) {

							event.group = groups[i]
							event.groupOrdinal = groupOrdinals[i]

							event.currentIndex = event.minAggregationColumnIndex

							worker.evaluateMinAggregationFuncINT(int64(values[i]), eventId, executorId)

							event.currentIndex = event.maxAggregationColumnIndex

							worker.evaluateMaxAggregationFuncINT(int64(values[i]), eventId, executorId)
						}
					}
				} else {

					worker.conditionBitmaps[executorId].Range(func(i uint32) {

						event.group = groups[i]
						event.groupOrdinal = groupOrdinals[i]

						event.currentIndex = event.minAggregationColumnIndex

						worker.evaluateMinAggregationFuncINT(int64(values[i]), eventId, executorId)

						event.currentIndex = event.maxAggregationColumnIndex

						worker.evaluateMaxAggregationFuncINT(int64(values[i]), eventId, executorId)
					})
				}

			} else if worker.aggregationFunc == MinSumCount {

				if iterateAll {

					for i := 0; i < length; i++ {

						if worker.conditionBitmaps[executorId].Contains(uint32(i)) {

							event.group = groups[i]
							event.groupOrdinal = groupOrdinals[i]

							event.currentIndex = event.minAggregationColumnIndex

							worker.evaluateMinAggregationFuncINT(int64(values[i]), eventId, executorId)

							event.currentIndex = event.sumAggregationColumnIndex

							worker.evaluateSumAggregationFuncINT(int64(values[i]), eventId, executorId)

							event.countIndex = event.countAggregationColumnIndex

							worker.evaluateCountAggregationFunc(1, eventId, executorId)
						}
					}
				} else {

					worker.conditionBitmaps[executorId].Range(func(i uint32) {

						event.group = groups[i]
						event.groupOrdinal = groupOrdinals[i]

						event.currentIndex = event.minAggregationColumnIndex

						worker.evaluateMinAggregationFuncINT(int64(values[i]), eventId, executorId)

						event.currentIndex = event.sumAggregationColumnIndex

						worker.evaluateSumAggregationFuncINT(int64(values[i]), eventId, executorId)

						event.countIndex = event.countAggregationColumnIndex

						worker.evaluateCountAggregationFunc(1, eventId, executorId)
					})
				}

			} else if worker.aggregationFunc == MinSum {

				if iterateAll {

					for i := 0; i < length; i++ {

						if worker.conditionBitmaps[executorId].Contains(uint32(i)) {

							event.group = groups[i]
							event.groupOrdinal = groupOrdinals[i]

							event.currentIndex = event.minAggregationColumnIndex

							worker.evaluateMinAggregationFuncINT(int64(values[i]), eventId, executorId)

							event.currentIndex = event.sumAggregationColumnIndex

							worker.evaluateSumAggregationFuncINT(int64(values[i]), eventId, executorId)
						}
					}
				} else {

					worker.conditionBitmaps[executorId].Range(func(i uint32) {

						event.group = groups[i]
						event.groupOrdinal = groupOrdinals[i]

						event.currentIndex = event.minAggregationColumnIndex

						worker.evaluateMinAggregationFuncINT(int64(values[i]), eventId, executorId)

						event.currentIndex = event.sumAggregationColumnIndex

						worker.evaluateSumAggregationFuncINT(int64(values[i]), eventId, executorId)
					})
				}

			} else if worker.aggregationFunc == MinCount {

				if iterateAll {

					for i := 0; i < length; i++ {

						if worker.conditionBitmaps[executorId].Contains(uint32(i)) {

							event.group = groups[i]
							event.groupOrdinal = groupOrdinals[i]

							event.currentIndex = event.minAggregationColumnIndex

							worker.evaluateMinAggregationFuncINT(int64(values[i]), eventId, executorId)

							event.countIndex = event.countAggregationColumnIndex

							worker.evaluateCountAggregationFunc(1, eventId, executorId)
						}
					}
				} else {

					worker.conditionBitmaps[executorId].Range(func(i uint32) {

						event.group = groups[i]
						event.groupOrdinal = groupOrdinals[i]

						event.currentIndex = event.minAggregationColumnIndex

						worker.evaluateMinAggregationFuncINT(int64(values[i]), eventId, executorId)

						event.countIndex = event.countAggregationColumnIndex

						worker.evaluateCountAggregationFunc(1, eventId, executorId)
					})
				}

			} else if worker.aggregationFunc == Min {

				if iterateAll {

					for i := 0; i < length; i++ {

						if worker.conditionBitmaps[executorId].Contains(uint32(i)) {

							event.group = groups[i]
							event.groupOrdinal = groupOrdinals[i]

							event.currentIndex = event.minAggregationColumnIndex

							if worker.columnDataTypes[executorId][event.minAggregationColumnIndex] == Int64 {

								worker.evaluateMinAggregationFuncINT(int64(values[i]), eventId, executorId)

							} else {

								worker.evaluateMinAggregationFuncFLOAT(float64(values[i]), eventId, executorId)
							}
						}
					}
				} else {

					worker.conditionBitmaps[executorId].Range(func(i uint32) {

						event.group = groups[i]
						event.groupOrdinal = groupOrdinals[i]

						event.currentIndex = event.minAggregationColumnIndex

						if worker.columnDataTypes[executorId][event.minAggregationColumnIndex] == Int64 {

							worker.evaluateMinAggregationFuncINT(int64(values[i]), eventId, executorId)

						} else {

							worker.evaluateMinAggregationFuncFLOAT(float64(values[i]), eventId, executorId)
						}
					})
				}

			} else if worker.aggregationFunc == MaxSumCount {

				if iterateAll {

					for i := 0; i < length; i++ {

						if worker.conditionBitmaps[executorId].Contains(uint32(i)) {

							event.group = groups[i]
							event.groupOrdinal = groupOrdinals[i]

							event.currentIndex = event.maxAggregationColumnIndex

							worker.evaluateMaxAggregationFuncINT(int64(values[i]), eventId, executorId)

							event.currentIndex = event.sumAggregationColumnIndex

							worker.evaluateSumAggregationFuncINT(int64(values[i]), eventId, executorId)

							event.countIndex = event.countAggregationColumnIndex

							worker.evaluateCountAggregationFunc(1, eventId, executorId)
						}
					}
				} else {

					worker.conditionBitmaps[executorId].Range(func(i uint32) {

						event.group = groups[i]
						event.groupOrdinal = groupOrdinals[i]

						event.currentIndex = event.maxAggregationColumnIndex

						worker.evaluateMaxAggregationFuncINT(int64(values[i]), eventId, executorId)

						event.currentIndex = event.sumAggregationColumnIndex

						worker.evaluateSumAggregationFuncINT(int64(values[i]), eventId, executorId)

						event.countIndex = event.countAggregationColumnIndex

						worker.evaluateCountAggregationFunc(1, eventId, executorId)
					})
				}

			} else if worker.aggregationFunc == MaxSum {

				if iterateAll {

					for i := 0; i < length; i++ {

						if worker.conditionBitmaps[executorId].Contains(uint32(i)) {

							event.group = groups[i]
							event.groupOrdinal = groupOrdinals[i]

							event.currentIndex = event.maxAggregationColumnIndex

							worker.evaluateMaxAggregationFuncINT(int64(values[i]), eventId, executorId)

							event.currentIndex = event.sumAggregationColumnIndex

							worker.evaluateSumAggregationFuncINT(int64(values[i]), eventId, executorId)
						}
					}

				} else {

					worker.conditionBitmaps[executorId].Range(func(i uint32) {

						event.group = groups[i]
						event.groupOrdinal = groupOrdinals[i]

						event.currentIndex = event.maxAggregationColumnIndex

						worker.evaluateMaxAggregationFuncINT(int64(values[i]), eventId, executorId)

						event.currentIndex = event.sumAggregationColumnIndex

						worker.evaluateSumAggregationFuncINT(int64(values[i]), eventId, executorId)
					})
				}

			} else if worker.aggregationFunc == MaxCount {

				if iterateAll {

					for i := 0; i < length; i++ {

						if worker.conditionBitmaps[executorId].Contains(uint32(i)) {

							event.group = groups[i]
							event.groupOrdinal = groupOrdinals[i]

							event.currentIndex = event.maxAggregationColumnIndex

							worker.evaluateMaxAggregationFuncINT(int64(values[i]), eventId, executorId)

							event.countIndex = event.countAggregationColumnIndex

							worker.evaluateCountAggregationFunc(1, eventId, executorId)
						}
					}
				} else {

					worker.conditionBitmaps[executorId].Range(func(i uint32) {

						event.group = groups[i]
						event.groupOrdinal = groupOrdinals[i]

						event.currentIndex = event.maxAggregationColumnIndex

						worker.evaluateMaxAggregationFuncINT(int64(values[i]), eventId, executorId)

						event.countIndex = event.countAggregationColumnIndex

						worker.evaluateCountAggregationFunc(1, eventId, executorId)
					})
				}

			} else if worker.aggregationFunc == Max {

				if iterateAll {

					for i := 0; i < length; i++ {

						if worker.conditionBitmaps[executorId].Contains(uint32(i)) {

							event.group = groups[i]
							event.groupOrdinal = groupOrdinals[i]

							event.currentIndex = event.maxAggregationColumnIndex

							if worker.columnDataTypes[executorId][event.maxAggregationColumnIndex] == Int64 {

								worker.evaluateMaxAggregationFuncINT(int64(values[i]), eventId, executorId)

							} else {

								worker.evaluateMaxAggregationFuncFLOAT(float64(values[i]), eventId, executorId)
							}

						}
					}
				} else {

					worker.conditionBitmaps[executorId].Range(func(i uint32) {

						event.group = groups[i]
						event.groupOrdinal = groupOrdinals[i]

						event.currentIndex = event.maxAggregationColumnIndex

						if worker.columnDataTypes[executorId][event.maxAggregationColumnIndex] == Int64 {

							worker.evaluateMaxAggregationFuncINT(int64(values[i]), eventId, executorId)

						} else {

							worker.evaluateMaxAggregationFuncFLOAT(float64(values[i]), eventId, executorId)
						}
					})
				}

			} else if worker.aggregationFunc == SumCount {

				if iterateAll {

					for i := 0; i < length; i++ {

						if worker.conditionBitmaps[executorId].Contains(uint32(i)) {

							event.group = groups[i]
							event.groupOrdinal = groupOrdinals[i]

							event.currentIndex = event.sumAggregationColumnIndex

							if worker.columnDataTypes[executorId][event.sumAggregationColumnIndex] == Int64 {

								worker.evaluateSumAggregationFuncINT(int64(values[i]), eventId, executorId)

							} else {

								worker.evaluateSumAggregationFuncFLOAT(float64(values[i]), eventId, executorId)
							}

							event.countIndex = event.countAggregationColumnIndex

							if worker.preAggregatedQuery {

								worker.evaluateCountAggregationFunc(int64(values[i]), eventId, executorId)

							} else {

								worker.evaluateCountAggregationFunc(1, eventId, executorId)
							}

						}
					}
				} else {

					worker.conditionBitmaps[executorId].Range(func(i uint32) {

						event.group = groups[i]
						event.groupOrdinal = groupOrdinals[i]

						event.currentIndex = event.sumAggregationColumnIndex

						if worker.columnDataTypes[executorId][event.sumAggregationColumnIndex] == Int64 {

							worker.evaluateSumAggregationFuncINT(int64(values[i]), eventId, executorId)

						} else {

							worker.evaluateSumAggregationFuncFLOAT(float64(values[i]), eventId, executorId)
						}

						event.countIndex = event.countAggregationColumnIndex

						if worker.preAggregatedQuery {

							worker.evaluateCountAggregationFunc(int64(values[i]), eventId, executorId)

						} else {

							worker.evaluateCountAggregationFunc(1, eventId, executorId)
						}
					})
				}

			} else if worker.aggregationFunc == Sum {

				if iterateAll {

					for i := 0; i < length; i++ {

						if worker.conditionBitmaps[executorId].Contains(uint32(i)) {

							event.group = groups[i]
							event.groupOrdinal = groupOrdinals[i]

							event.currentIndex = event.sumAggregationColumnIndex

							if worker.columnDataTypes[executorId][event.sumAggregationColumnIndex] == Int64 {

								worker.evaluateSumAggregationFuncINT(int64(values[i]), eventId, executorId)

							} else {

								worker.evaluateSumAggregationFuncFLOAT(float64(values[i]), eventId, executorId)
							}

						}
					}
				} else {

					worker.conditionBitmaps[executorId].Range(func(i uint32) {

						event.group = groups[i]
						event.groupOrdinal = groupOrdinals[i]

						event.currentIndex = event.sumAggregationColumnIndex

						if worker.columnDataTypes[executorId][event.sumAggregationColumnIndex] == Int64 {

							worker.evaluateSumAggregationFuncINT(int64(values[i]), eventId, executorId)

						} else {

							worker.evaluateSumAggregationFuncFLOAT(float64(values[i]), eventId, executorId)
						}
					})
				}

			} else if worker.aggregationFunc == Count {

				if iterateAll {

					for i := 0; i < length; i++ {

						if worker.conditionBitmaps[executorId].Contains(uint32(i)) {

							event.group = groups[i]
							event.groupOrdinal = groupOrdinals[i]

							event.countIndex = event.countAggregationColumnIndex

							if worker.preAggregatedQuery {

								worker.evaluateCountAggregationFunc(int64(values[i]), eventId, executorId)

							} else {

								worker.evaluateCountAggregationFunc(1, eventId, executorId)
							}

						}
					}
				} else {

					worker.conditionBitmaps[executorId].Range(func(i uint32) {

						event.group = groups[i]
						event.groupOrdinal = groupOrdinals[i]

						event.countIndex = event.countAggregationColumnIndex

						if worker.preAggregatedQuery {

							worker.evaluateCountAggregationFunc(int64(values[i]), eventId, executorId)

						} else {

							worker.evaluateCountAggregationFunc(1, eventId, executorId)
						}
					})
				}
			}

			if worker.lastFunc {

				column += utils.KeySeparator + LastFunc

				event.currentIndex = worker.setColumnContext(executorId, column, dataType, false)

				tickPoolIndex := worker.setColumnContext(executorId, column+"^tick", Int64, true)

				if iterateAll {

					for i := 0; i < length; i++ {

						if worker.conditionBitmaps[executorId].Contains(uint32(i)) {

							event.group = groups[i]
							event.groupOrdinal = groupOrdinals[i]

							if worker.evaluateHorizontalLastValueTick(tickPoolIndex, eventId, executorId) {

								if worker.columnDataTypes[executorId][event.currentIndex] == Int64 {

									worker.evaluateLastValueAggregationFuncINT(int64(values[i]), eventId, executorId)

								} else if worker.columnDataTypes[executorId][event.currentIndex] == Float64 {

									worker.evaluateLastValueAggregationFuncFLOAT(float64(values[i]), eventId, executorId)

								} else {

									worker.evaluateLastValueAggregationFuncString(INT32ToStringValue(values[i]), eventId, executorId)
								}
							}

						}
					}
				} else {

					worker.conditionBitmaps[executorId].Range(func(i uint32) {

						event.group = groups[i]
						event.groupOrdinal = groupOrdinals[i]

						if worker.evaluateHorizontalLastValueTick(tickPoolIndex, eventId, executorId) {

							if worker.columnDataTypes[executorId][event.currentIndex] == Int64 {

								worker.evaluateLastValueAggregationFuncINT(int64(values[i]), eventId, executorId)

							} else if worker.columnDataTypes[executorId][event.currentIndex] == Float64 {

								worker.evaluateLastValueAggregationFuncFLOAT(float64(values[i]), eventId, executorId)

							} else {

								worker.evaluateLastValueAggregationFuncString(INT32ToStringValue(values[i]), eventId, executorId)
							}
						}
					})
				}
			}

		} else if event.grouping && !event.condition {

			length := len(values)

			if len(groups) < length {

				length = len(groups)
			}

			if worker.aggregationFunc == MinMaxSumCount {

				for i := 0; i < length; i++ {

					event.group = groups[i]
					event.groupOrdinal = groupOrdinals[i]

					event.currentIndex = event.minAggregationColumnIndex

					worker.evaluateMinAggregationFuncINT(int64(values[i]), eventId, executorId)

					event.currentIndex = event.maxAggregationColumnIndex

					worker.evaluateMaxAggregationFuncINT(int64(values[i]), eventId, executorId)

					event.currentIndex = event.sumAggregationColumnIndex

					worker.evaluateSumAggregationFuncINT(int64(values[i]), eventId, executorId)

					event.countIndex = event.countAggregationColumnIndex

					worker.evaluateCountAggregationFunc(1, eventId, executorId)
				}

			} else if worker.aggregationFunc == MinMaxSum {

				for i := 0; i < length; i++ {

					event.group = groups[i]
					event.groupOrdinal = groupOrdinals[i]

					event.currentIndex = event.minAggregationColumnIndex

					worker.evaluateMinAggregationFuncINT(int64(values[i]), eventId, executorId)

					event.currentIndex = event.maxAggregationColumnIndex

					worker.evaluateMaxAggregationFuncINT(int64(values[i]), eventId, executorId)

					event.currentIndex = event.sumAggregationColumnIndex

					worker.evaluateSumAggregationFuncINT(int64(values[i]), eventId, executorId)
				}

			} else if worker.aggregationFunc == MinMaxCount {

				for i := 0; i < length; i++ {

					event.group = groups[i]
					event.groupOrdinal = groupOrdinals[i]

					event.currentIndex = event.minAggregationColumnIndex

					worker.evaluateMinAggregationFuncINT(int64(values[i]), eventId, executorId)

					event.currentIndex = event.maxAggregationColumnIndex

					worker.evaluateMaxAggregationFuncINT(int64(values[i]), eventId, executorId)

					event.countIndex = event.countAggregationColumnIndex

					worker.evaluateCountAggregationFunc(1, eventId, executorId)
				}

			} else if worker.aggregationFunc == MinMax {

				for i := 0; i < length; i++ {

					event.group = groups[i]
					event.groupOrdinal = groupOrdinals[i]

					event.currentIndex = event.minAggregationColumnIndex

					worker.evaluateMinAggregationFuncINT(int64(values[i]), eventId, executorId)

					event.currentIndex = event.maxAggregationColumnIndex

					worker.evaluateMaxAggregationFuncINT(int64(values[i]), eventId, executorId)
				}

			} else if worker.aggregationFunc == MinSumCount {

				for i := 0; i < length; i++ {

					event.group = groups[i]
					event.groupOrdinal = groupOrdinals[i]

					event.currentIndex = event.minAggregationColumnIndex

					worker.evaluateMinAggregationFuncINT(int64(values[i]), eventId, executorId)

					event.currentIndex = event.sumAggregationColumnIndex

					worker.evaluateSumAggregationFuncINT(int64(values[i]), eventId, executorId)

					event.countIndex = event.countAggregationColumnIndex

					worker.evaluateCountAggregationFunc(1, eventId, executorId)
				}

			} else if worker.aggregationFunc == MinSum {

				for i := 0; i < length; i++ {

					event.group = groups[i]
					event.groupOrdinal = groupOrdinals[i]

					event.currentIndex = event.minAggregationColumnIndex

					worker.evaluateMinAggregationFuncINT(int64(values[i]), eventId, executorId)

					event.currentIndex = event.sumAggregationColumnIndex

					worker.evaluateSumAggregationFuncINT(int64(values[i]), eventId, executorId)
				}

			} else if worker.aggregationFunc == MinCount {

				for i := 0; i < length; i++ {

					event.group = groups[i]
					event.groupOrdinal = groupOrdinals[i]

					event.currentIndex = event.minAggregationColumnIndex

					worker.evaluateMinAggregationFuncINT(int64(values[i]), eventId, executorId)

					event.countIndex = event.countAggregationColumnIndex

					worker.evaluateCountAggregationFunc(1, eventId, executorId)
				}

			} else if worker.aggregationFunc == Min {

				for i := 0; i < length; i++ {

					event.group = groups[i]
					event.groupOrdinal = groupOrdinals[i]

					event.currentIndex = event.minAggregationColumnIndex

					if worker.columnDataTypes[executorId][event.minAggregationColumnIndex] == Int64 {

						worker.evaluateMinAggregationFuncINT(int64(values[i]), eventId, executorId)

					} else {

						worker.evaluateMinAggregationFuncFLOAT(float64(values[i]), eventId, executorId)
					}

				}

			} else if worker.aggregationFunc == MaxSumCount {

				for i := 0; i < length; i++ {

					event.group = groups[i]
					event.groupOrdinal = groupOrdinals[i]

					event.currentIndex = event.maxAggregationColumnIndex

					worker.evaluateMaxAggregationFuncINT(int64(values[i]), eventId, executorId)

					event.currentIndex = event.sumAggregationColumnIndex

					worker.evaluateSumAggregationFuncINT(int64(values[i]), eventId, executorId)

					event.countIndex = event.countAggregationColumnIndex

					worker.evaluateCountAggregationFunc(1, eventId, executorId)
				}

			} else if worker.aggregationFunc == MaxSum {

				for i := 0; i < length; i++ {

					event.group = groups[i]
					event.groupOrdinal = groupOrdinals[i]

					event.currentIndex = event.maxAggregationColumnIndex

					worker.evaluateMaxAggregationFuncINT(int64(values[i]), eventId, executorId)

					event.currentIndex = event.sumAggregationColumnIndex

					worker.evaluateSumAggregationFuncINT(int64(values[i]), eventId, executorId)
				}

			} else if worker.aggregationFunc == MaxCount {

				for i := 0; i < length; i++ {

					event.group = groups[i]
					event.groupOrdinal = groupOrdinals[i]

					event.currentIndex = event.maxAggregationColumnIndex

					worker.evaluateMaxAggregationFuncINT(int64(values[i]), eventId, executorId)

					event.countIndex = event.countAggregationColumnIndex

					worker.evaluateCountAggregationFunc(1, eventId, executorId)
				}

			} else if worker.aggregationFunc == Max {

				for i := 0; i < length; i++ {

					event.group = groups[i]
					event.groupOrdinal = groupOrdinals[i]

					event.currentIndex = event.maxAggregationColumnIndex

					if worker.columnDataTypes[executorId][event.maxAggregationColumnIndex] == Int64 {

						worker.evaluateMaxAggregationFuncINT(int64(values[i]), eventId, executorId)

					} else {

						worker.evaluateMaxAggregationFuncFLOAT(float64(values[i]), eventId, executorId)
					}

				}

			} else if worker.aggregationFunc == SumCount {

				for i := 0; i < length; i++ {

					event.group = groups[i]
					event.groupOrdinal = groupOrdinals[i]

					event.currentIndex = event.sumAggregationColumnIndex

					if worker.columnDataTypes[executorId][event.sumAggregationColumnIndex] == Int64 {

						worker.evaluateSumAggregationFuncINT(int64(values[i]), eventId, executorId)

					} else {

						worker.evaluateSumAggregationFuncFLOAT(float64(values[i]), eventId, executorId)
					}

					event.countIndex = event.countAggregationColumnIndex

					if worker.preAggregatedQuery {

						worker.evaluateCountAggregationFunc(int64(values[i]), eventId, executorId)

					} else {

						worker.evaluateCountAggregationFunc(1, eventId, executorId)
					}
				}

			} else if worker.aggregationFunc == Sum {

				for i := 0; i < length; i++ {

					event.group = groups[i]
					event.groupOrdinal = groupOrdinals[i]

					event.currentIndex = event.sumAggregationColumnIndex

					if worker.columnDataTypes[executorId][event.sumAggregationColumnIndex] == Int64 {

						worker.evaluateSumAggregationFuncINT(int64(values[i]), eventId, executorId)

					} else {

						worker.evaluateSumAggregationFuncFLOAT(float64(values[i]), eventId, executorId)
					}

				}

			} else if worker.aggregationFunc == Count {

				for i := 0; i < length; i++ {

					event.group = groups[i]
					event.groupOrdinal = groupOrdinals[i]

					event.countIndex = event.countAggregationColumnIndex

					if worker.preAggregatedQuery {

						worker.evaluateCountAggregationFunc(int64(values[i]), eventId, executorId)

					} else {

						worker.evaluateCountAggregationFunc(1, eventId, executorId)
					}
				}

			}

			if worker.lastFunc {

				column += utils.KeySeparator + LastFunc

				event.currentIndex = worker.setColumnContext(executorId, column, dataType, false)

				tickPoolIndex := worker.setColumnContext(executorId, column+"^tick", Int64, true)

				for i := 0; i < length; i++ {

					event.group = groups[i]
					event.groupOrdinal = groupOrdinals[i]

					if worker.evaluateHorizontalLastValueTick(tickPoolIndex, eventId, executorId) {

						if worker.columnDataTypes[executorId][event.currentIndex] == Int64 {

							worker.evaluateLastValueAggregationFuncINT(int64(values[i]), eventId, executorId)

						} else if worker.columnDataTypes[executorId][event.currentIndex] == Float64 {

							worker.evaluateLastValueAggregationFuncFLOAT(float64(values[i]), eventId, executorId)

						} else {

							worker.evaluateLastValueAggregationFuncString(INT32ToStringValue(values[i]), eventId, executorId)
						}
					}
				}

			}

		} else if !event.grouping && event.condition {

			minValue, maxValue, sum, count := int64(0), int64(0), int64(0), int64(0)

			if worker.aggregationFunc == Min {

				minValue = utils.MinINT32Conditional(values, worker.conditionBitmaps[executorId])

				event.currentIndex = event.minAggregationColumnIndex

				if worker.columnDataTypes[executorId][event.minAggregationColumnIndex] == Int64 {

					worker.evaluateMinAggregationFuncINT(minValue, eventId, executorId)

				} else {

					worker.evaluateMinAggregationFuncFLOAT(float64(minValue), eventId, executorId)
				}

			} else if worker.aggregationFunc == Max {

				maxValue = utils.MaxINT32Conditional(values, worker.conditionBitmaps[executorId])

				event.currentIndex = event.maxAggregationColumnIndex

				if worker.columnDataTypes[executorId][event.maxAggregationColumnIndex] == Int64 {

					worker.evaluateMaxAggregationFuncINT(maxValue, eventId, executorId)

				} else {

					worker.evaluateMaxAggregationFuncFLOAT(float64(maxValue), eventId, executorId)
				}

			} else if worker.aggregationFunc == SumCount {

				sum, count = utils.SumCountINT32Conditional(values, worker.conditionBitmaps[executorId])

				event.currentIndex = event.sumAggregationColumnIndex

				if worker.columnDataTypes[executorId][event.sumAggregationColumnIndex] == Int64 {

					worker.evaluateSumAggregationFuncINT(sum, eventId, executorId)

				} else {

					worker.evaluateSumAggregationFuncFLOAT(float64(sum), eventId, executorId)
				}

				event.countIndex = event.countAggregationColumnIndex

				worker.evaluateCountAggregationFunc(count, eventId, executorId)

			} else if worker.aggregationFunc == Sum {

				sum = utils.SumINT32Conditional(values, worker.conditionBitmaps[executorId])

				event.currentIndex = event.sumAggregationColumnIndex

				if worker.columnDataTypes[executorId][event.sumAggregationColumnIndex] == Int64 {

					worker.evaluateSumAggregationFuncINT(sum, eventId, executorId)

				} else {

					worker.evaluateSumAggregationFuncFLOAT(float64(sum), eventId, executorId)
				}

			} else if worker.aggregationFunc == Count {

				event.countIndex = event.countAggregationColumnIndex

				if worker.preAggregatedQuery {
					worker.evaluateCountAggregationFunc(utils.CountINT32Conditional(worker.conditionBitmaps[executorId], values), eventId, executorId)
				} else {

					worker.evaluateCountAggregationFunc(utils.CountConditional(worker.conditionBitmaps[executorId]), eventId, executorId)
				}

			}

			if worker.lastFunc {

				column += utils.KeySeparator + LastFunc

				event.currentIndex = worker.setColumnContext(executorId, column, dataType, false)

				tickPoolIndex := worker.setColumnContext(executorId, column+"^tick", Int64, true)

				if worker.evaluateHorizontalLastValueTick(tickPoolIndex, eventId, executorId) {

					if worker.columnDataTypes[executorId][event.currentIndex] == Int64 {

						worker.evaluateLastValueAggregationFuncINT(int64(utils.LastINT32Conditional(values, worker.conditionBitmaps[executorId])), eventId, executorId)

					} else if worker.columnDataTypes[executorId][event.currentIndex] == Float64 {

						worker.evaluateLastValueAggregationFuncFLOAT(float64(utils.LastINT32Conditional(values, worker.conditionBitmaps[executorId])), eventId, executorId)

					} else {

						worker.evaluateLastValueAggregationFuncString(INT32ToStringValue(utils.LastINT32Conditional(values, worker.conditionBitmaps[executorId])), eventId, executorId)
					}
				}

			}

		} else if !event.condition && !event.grouping {

			minValue, maxValue, sum := int64(0), int64(0), int64(0)

			if worker.aggregationFunc == Min {

				minValue = utils.MinINT32(values)

				event.currentIndex = event.minAggregationColumnIndex

				if worker.columnDataTypes[executorId][event.minAggregationColumnIndex] == Int64 {

					worker.evaluateMinAggregationFuncINT(minValue, eventId, executorId)

				} else {

					worker.evaluateMinAggregationFuncFLOAT(float64(minValue), eventId, executorId)
				}

			} else if worker.aggregationFunc == Max {

				maxValue = utils.MaxINT32(values)

				event.currentIndex = event.maxAggregationColumnIndex

				if worker.columnDataTypes[executorId][event.maxAggregationColumnIndex] == Int64 {

					worker.evaluateMaxAggregationFuncINT(maxValue, eventId, executorId)

				} else {

					worker.evaluateMaxAggregationFuncFLOAT(float64(maxValue), eventId, executorId)
				}

			} else if worker.aggregationFunc == SumCount {

				sum = utils.SumINT32(values)

				event.currentIndex = event.sumAggregationColumnIndex

				if worker.columnDataTypes[executorId][event.sumAggregationColumnIndex] == Int64 {

					worker.evaluateSumAggregationFuncINT(sum, eventId, executorId)

				} else {

					worker.evaluateSumAggregationFuncFLOAT(float64(sum), eventId, executorId)
				}

				event.countIndex = event.countAggregationColumnIndex

				if worker.preAggregatedQuery {

					worker.evaluateCountAggregationFunc(utils.SumINT32(values), eventId, executorId)

				} else {

					worker.evaluateCountAggregationFunc(int64(len(values)), eventId, executorId)
				}

			} else if worker.aggregationFunc == Sum {

				sum = utils.SumINT32(values)

				event.currentIndex = event.sumAggregationColumnIndex

				if worker.columnDataTypes[executorId][event.sumAggregationColumnIndex] == Int64 {

					worker.evaluateSumAggregationFuncINT(sum, eventId, executorId)

				} else {

					worker.evaluateSumAggregationFuncFLOAT(float64(sum), eventId, executorId)
				}

			} else if worker.aggregationFunc == Count {

				event.countIndex = event.countAggregationColumnIndex

				if worker.preAggregatedQuery {

					worker.evaluateCountAggregationFunc(utils.SumINT32(values), eventId, executorId)

				} else {

					worker.evaluateCountAggregationFunc(int64(len(values)), eventId, executorId)
				}

			}

			if worker.lastFunc {

				column += utils.KeySeparator + LastFunc

				event.currentIndex = worker.setColumnContext(executorId, column, dataType, false)

				tickPoolIndex := worker.setColumnContext(executorId, column+"^tick", Int64, true)

				if worker.evaluateHorizontalLastValueTick(tickPoolIndex, eventId, executorId) {

					if worker.columnDataTypes[executorId][event.currentIndex] == Int64 {

						worker.evaluateLastValueAggregationFuncINT(int64(values[len(values)-1]), eventId, executorId)

					} else if worker.columnDataTypes[executorId][event.currentIndex] == Float64 {

						worker.evaluateLastValueAggregationFuncFLOAT(float64(values[len(values)-1]), eventId, executorId)

					} else {

						worker.evaluateLastValueAggregationFuncString(INT32ToStringValue(values[len(values)-1]), eventId, executorId)
					}
				}

			}
		}

	} else {

		if worker.aggregationFunc == 0 { // means we need raw data...

			delete(worker.missingColumns, column)

			if !worker.lastFunc && updatePosition {

				updatePosition = false

				worker.memoryPoolPositions[executorId] = position

				if event.condition {

					position = worker.setHorizontalHistoricalTickColumn(executorId, worker.tick, len(values), worker.conditionBitmaps[executorId])
				} else {

					position = worker.setHorizontalHistoricalTickColumn(executorId, worker.tick, len(values), nil)
				}
			}

			if event.condition {

				worker.setHorizontalHistoricalValueColumnINT32(eventId, executorId, worker.getBatchSize(len(values), executorId, event.condition), column, values, worker.conditionBitmaps[executorId])

			} else {

				worker.setHorizontalHistoricalValueColumnINT32(eventId, executorId, worker.getBatchSize(len(values), executorId, event.condition), column, values, nil)
			}

		} else {

			if event.grouping && event.condition {

				size, _ := worker.conditionBitmaps[executorId].Max()

				length := int(size + 1)

				iterateAll := false

				// if condition operand array size is 10 and max bit is 10 length is 10 but aggregated operand array size is 8 than need this condition
				if length > len(values) {

					iterateAll = true

					length = len(values)
				}

				if !iterateAll && length >= len(values)-((len(values)*conditionIterateThreshold)/100) {

					iterateAll = true
				}

				if worker.aggregationFunc == Min {

					if iterateAll {

						for i := 0; i < length; i++ {

							if worker.conditionBitmaps[executorId].Contains(uint32(i)) {

								event.group = groups[i]
								event.groupOrdinal = groupOrdinals[i]

								event.currentIndex = event.minAggregationColumnIndex

								if worker.columnDataTypes[executorId][event.minAggregationColumnIndex] == Int64 {

									worker.evaluateHorizontalMinHistogramFuncINT(int64(values[i]), eventId, executorId)

								} else {

									worker.evaluateHorizontalMinHistogramFuncFLOAT(float64(values[i]), eventId, executorId)
								}

							}
						}
					} else {

						worker.conditionBitmaps[executorId].Range(func(i uint32) {

							event.group = groups[i]
							event.groupOrdinal = groupOrdinals[i]

							event.currentIndex = event.minAggregationColumnIndex

							if worker.columnDataTypes[executorId][event.minAggregationColumnIndex] == Int64 {

								worker.evaluateHorizontalMinHistogramFuncINT(int64(values[i]), eventId, executorId)

							} else {

								worker.evaluateHorizontalMinHistogramFuncFLOAT(float64(values[i]), eventId, executorId)
							}
						})
					}

				} else if worker.aggregationFunc == Max {

					if iterateAll {

						for i := 0; i < length; i++ {

							if worker.conditionBitmaps[executorId].Contains(uint32(i)) {

								event.group = groups[i]
								event.groupOrdinal = groupOrdinals[i]

								event.currentIndex = event.maxAggregationColumnIndex

								if worker.columnDataTypes[executorId][event.maxAggregationColumnIndex] == Int64 {

									worker.evaluateHorizontalMaxHistogramFuncINT(int64(values[i]), eventId, executorId)

								} else {

									worker.evaluateHorizontalMaxHistogramFuncFLOAT(float64(values[i]), eventId, executorId)
								}

							}
						}
					} else {

						worker.conditionBitmaps[executorId].Range(func(i uint32) {

							event.group = groups[i]
							event.groupOrdinal = groupOrdinals[i]

							event.currentIndex = event.maxAggregationColumnIndex

							if worker.columnDataTypes[executorId][event.maxAggregationColumnIndex] == Int64 {

								worker.evaluateHorizontalMaxHistogramFuncINT(int64(values[i]), eventId, executorId)

							} else {

								worker.evaluateHorizontalMaxHistogramFuncFLOAT(float64(values[i]), eventId, executorId)
							}

						})
					}

				} else if worker.aggregationFunc == SumCount {

					event.currentIndex = event.sumAggregationColumnIndex

					event.countIndex = event.countAggregationColumnIndex

					if iterateAll {

						for i := 0; i < length; i++ {

							if worker.conditionBitmaps[executorId].Contains(uint32(i)) {

								event.group = groups[i]
								event.groupOrdinal = groupOrdinals[i]

								if worker.columnDataTypes[executorId][event.sumAggregationColumnIndex] == Int64 {

									worker.evaluateHorizontalMeanHistogramFuncINT(int64(values[i]), 1, eventId, executorId)

								} else {

									worker.evaluateHorizontalMeanHistogramFuncFLOAT(float64(values[i]), 1, eventId, executorId)
								}
							}
						}
					} else {

						worker.conditionBitmaps[executorId].Range(func(i uint32) {

							event.group = groups[i]
							event.groupOrdinal = groupOrdinals[i]

							if worker.columnDataTypes[executorId][event.sumAggregationColumnIndex] == Int64 {

								worker.evaluateHorizontalMeanHistogramFuncINT(int64(values[i]), 1, eventId, executorId)

							} else {

								worker.evaluateHorizontalMeanHistogramFuncFLOAT(float64(values[i]), 1, eventId, executorId)
							}
						})
					}
				} else if worker.aggregationFunc == Sum {

					event.currentIndex = event.sumAggregationColumnIndex

					if iterateAll {

						for i := 0; i < length; i++ {

							if worker.conditionBitmaps[executorId].Contains(uint32(i)) {

								event.group = groups[i]
								event.groupOrdinal = groupOrdinals[i]

								if worker.columnDataTypes[executorId][event.sumAggregationColumnIndex] == Int64 {

									worker.evaluateHorizontalSumHistogramFuncINT(int64(values[i]), eventId, executorId)

								} else {

									worker.evaluateHorizontalSumHistogramFuncFLOAT(float64(values[i]), eventId, executorId)
								}
							}
						}

					} else {

						worker.conditionBitmaps[executorId].Range(func(i uint32) {

							event.group = groups[i]
							event.groupOrdinal = groupOrdinals[i]

							if worker.columnDataTypes[executorId][event.sumAggregationColumnIndex] == Int64 {

								worker.evaluateHorizontalSumHistogramFuncINT(int64(values[i]), eventId, executorId)

							} else {

								worker.evaluateHorizontalSumHistogramFuncFLOAT(float64(values[i]), eventId, executorId)
							}
						})
					}
				} else if worker.aggregationFunc == Count {

					event.countIndex = event.countAggregationColumnIndex

					if iterateAll {

						for i := 0; i < length; i++ {

							if worker.conditionBitmaps[executorId].Contains(uint32(i)) {

								event.group = groups[i]
								event.groupOrdinal = groupOrdinals[i]

								if worker.preAggregatedQuery {

									worker.evaluateHorizontalCountHistogramFunc(int64(values[i]), eventId, executorId)
								} else {

									worker.evaluateHorizontalCountHistogramFunc(1, eventId, executorId)
								}

							}
						}

					} else {

						worker.conditionBitmaps[executorId].Range(func(i uint32) {

							event.group = groups[i]
							event.groupOrdinal = groupOrdinals[i]

							if worker.preAggregatedQuery {

								worker.evaluateHorizontalCountHistogramFunc(int64(values[i]), eventId, executorId)
							} else {

								worker.evaluateHorizontalCountHistogramFunc(1, eventId, executorId)
							}

						})
					}
				}

			} else if !event.grouping && event.condition {

				if worker.aggregationFunc == MinMaxSumCount {

					event.currentIndex = event.minAggregationColumnIndex

					worker.evaluateHorizontalMinHistogramFuncINT(utils.MinINT32Conditional(values, worker.conditionBitmaps[executorId]), eventId, executorId)

					event.currentIndex = event.maxAggregationColumnIndex

					worker.evaluateHorizontalMaxHistogramFuncINT(utils.MaxINT32Conditional(values, worker.conditionBitmaps[executorId]), eventId, executorId)

					event.currentIndex = event.sumAggregationColumnIndex

					worker.evaluateHorizontalSumHistogramFuncINT(utils.SumINT32Conditional(values, worker.conditionBitmaps[executorId]), eventId, executorId)

					event.countIndex = event.countAggregationColumnIndex

					worker.evaluateHorizontalCountHistogramFunc(utils.CountConditional(worker.conditionBitmaps[executorId]), eventId, executorId)

				} else if worker.aggregationFunc == MinMaxSum {

					event.currentIndex = event.minAggregationColumnIndex

					worker.evaluateHorizontalMinHistogramFuncINT(utils.MinINT32Conditional(values, worker.conditionBitmaps[executorId]), eventId, executorId)

					event.currentIndex = event.maxAggregationColumnIndex

					worker.evaluateHorizontalMaxHistogramFuncINT(utils.MaxINT32Conditional(values, worker.conditionBitmaps[executorId]), eventId, executorId)

					event.currentIndex = event.sumAggregationColumnIndex

					worker.evaluateHorizontalSumHistogramFuncINT(utils.SumINT32Conditional(values, worker.conditionBitmaps[executorId]), eventId, executorId)

				} else if worker.aggregationFunc == MinMaxCount {

					event.currentIndex = event.minAggregationColumnIndex

					worker.evaluateHorizontalMinHistogramFuncINT(utils.MinINT32Conditional(values, worker.conditionBitmaps[executorId]), eventId, executorId)

					event.currentIndex = event.maxAggregationColumnIndex

					worker.evaluateHorizontalMaxHistogramFuncINT(utils.MaxINT32Conditional(values, worker.conditionBitmaps[executorId]), eventId, executorId)

					event.countIndex = event.countAggregationColumnIndex

					worker.evaluateHorizontalCountHistogramFunc(utils.CountConditional(worker.conditionBitmaps[executorId]), eventId, executorId)

				} else if worker.aggregationFunc == MinMax {

					event.currentIndex = event.minAggregationColumnIndex

					worker.evaluateHorizontalMinHistogramFuncINT(utils.MinINT32Conditional(values, worker.conditionBitmaps[executorId]), eventId, executorId)

					event.currentIndex = event.maxAggregationColumnIndex

					worker.evaluateHorizontalMaxHistogramFuncINT(utils.MaxINT32Conditional(values, worker.conditionBitmaps[executorId]), eventId, executorId)

				} else if worker.aggregationFunc == MinSumCount {

					event.currentIndex = event.minAggregationColumnIndex

					worker.evaluateHorizontalMinHistogramFuncINT(utils.MinINT32Conditional(values, worker.conditionBitmaps[executorId]), eventId, executorId)

					event.currentIndex = event.sumAggregationColumnIndex

					worker.evaluateHorizontalSumHistogramFuncINT(utils.SumINT32Conditional(values, worker.conditionBitmaps[executorId]), eventId, executorId)

					event.countIndex = event.countAggregationColumnIndex

					worker.evaluateHorizontalCountHistogramFunc(utils.CountConditional(worker.conditionBitmaps[executorId]), eventId, executorId)

				} else if worker.aggregationFunc == MinSum {

					event.currentIndex = event.minAggregationColumnIndex

					worker.evaluateHorizontalMinHistogramFuncINT(utils.MinINT32Conditional(values, worker.conditionBitmaps[executorId]), eventId, executorId)

					event.currentIndex = event.sumAggregationColumnIndex

					worker.evaluateHorizontalSumHistogramFuncINT(utils.SumINT32Conditional(values, worker.conditionBitmaps[executorId]), eventId, executorId)

				} else if worker.aggregationFunc == MinCount {

					event.currentIndex = event.minAggregationColumnIndex

					worker.evaluateHorizontalMinHistogramFuncINT(utils.MinINT32Conditional(values, worker.conditionBitmaps[executorId]), eventId, executorId)

					event.countIndex = event.countAggregationColumnIndex

					worker.evaluateHorizontalCountHistogramFunc(utils.CountConditional(worker.conditionBitmaps[executorId]), eventId, executorId)

				} else if worker.aggregationFunc == Min {

					event.currentIndex = event.minAggregationColumnIndex

					if worker.columnDataTypes[executorId][event.minAggregationColumnIndex] == Int64 {

						worker.evaluateHorizontalMinHistogramFuncINT(utils.MinINT32Conditional(values, worker.conditionBitmaps[executorId]), eventId, executorId)

					} else {

						worker.evaluateHorizontalMinHistogramFuncFLOAT(float64(utils.MinINT32Conditional(values, worker.conditionBitmaps[executorId])), eventId, executorId)
					}

				} else if worker.aggregationFunc == MaxSumCount {

					event.currentIndex = event.maxAggregationColumnIndex

					worker.evaluateHorizontalMaxHistogramFuncINT(utils.MaxINT32Conditional(values, worker.conditionBitmaps[executorId]), eventId, executorId)

					event.currentIndex = event.sumAggregationColumnIndex

					worker.evaluateHorizontalSumHistogramFuncINT(utils.SumINT32Conditional(values, worker.conditionBitmaps[executorId]), eventId, executorId)

					event.countIndex = event.countAggregationColumnIndex

					worker.evaluateHorizontalCountHistogramFunc(utils.CountConditional(worker.conditionBitmaps[executorId]), eventId, executorId)

				} else if worker.aggregationFunc == MaxSum {

					event.currentIndex = event.maxAggregationColumnIndex

					worker.evaluateHorizontalMaxHistogramFuncINT(utils.MaxINT32Conditional(values, worker.conditionBitmaps[executorId]), eventId, executorId)

					event.currentIndex = event.sumAggregationColumnIndex

					worker.evaluateHorizontalSumHistogramFuncINT(utils.SumINT32Conditional(values, worker.conditionBitmaps[executorId]), eventId, executorId)

				} else if worker.aggregationFunc == MaxCount {

					event.currentIndex = event.maxAggregationColumnIndex

					worker.evaluateHorizontalMaxHistogramFuncINT(utils.MaxINT32Conditional(values, worker.conditionBitmaps[executorId]), eventId, executorId)

					event.countIndex = event.countAggregationColumnIndex

					worker.evaluateHorizontalCountHistogramFunc(utils.CountConditional(worker.conditionBitmaps[executorId]), eventId, executorId)

				} else if worker.aggregationFunc == Max {

					event.currentIndex = event.maxAggregationColumnIndex

					if worker.columnDataTypes[executorId][event.maxAggregationColumnIndex] == Int64 {

						worker.evaluateHorizontalMaxHistogramFuncINT(utils.MaxINT32Conditional(values, worker.conditionBitmaps[executorId]), eventId, executorId)

					} else {

						worker.evaluateHorizontalMaxHistogramFuncFLOAT(float64(utils.MaxINT32Conditional(values, worker.conditionBitmaps[executorId])), eventId, executorId)
					}

				} else if worker.aggregationFunc == SumCount {

					event.currentIndex = event.sumAggregationColumnIndex

					event.countIndex = event.countAggregationColumnIndex

					sum, count := utils.SumCountINT32Conditional(values, worker.conditionBitmaps[executorId])

					if worker.columnDataTypes[executorId][event.sumAggregationColumnIndex] == Int64 {

						worker.evaluateHorizontalMeanHistogramFuncINT(sum, count, eventId, executorId)

					} else {

						worker.evaluateHorizontalMeanHistogramFuncFLOAT(float64(sum), count, eventId, executorId)

					}

				} else if worker.aggregationFunc == Sum {

					event.currentIndex = event.sumAggregationColumnIndex

					if worker.columnDataTypes[executorId][event.sumAggregationColumnIndex] == Int64 {

						worker.evaluateHorizontalSumHistogramFuncINT(utils.SumINT32Conditional(values, worker.conditionBitmaps[executorId]), eventId, executorId)

					} else {

						worker.evaluateHorizontalSumHistogramFuncFLOAT(float64(utils.SumINT32Conditional(values, worker.conditionBitmaps[executorId])), eventId, executorId)

					}

				} else if worker.aggregationFunc == Count {

					event.countIndex = event.countAggregationColumnIndex

					if worker.preAggregatedQuery {

						worker.evaluateHorizontalCountHistogramFunc(utils.CountINT32Conditional(worker.conditionBitmaps[executorId], values), eventId, executorId)
					} else {
						worker.evaluateHorizontalCountHistogramFunc(utils.CountConditional(worker.conditionBitmaps[executorId]), eventId, executorId)
					}

				}

			} else if !event.condition && !event.grouping {

				if worker.aggregationFunc == MinMaxSumCount {

					event.currentIndex = event.minAggregationColumnIndex

					worker.evaluateHorizontalMinHistogramFuncINT(utils.MinINT32(values), eventId, executorId)

					event.currentIndex = event.maxAggregationColumnIndex

					worker.evaluateHorizontalMaxHistogramFuncINT(utils.MaxINT32(values), eventId, executorId)

					event.currentIndex = event.sumAggregationColumnIndex

					worker.evaluateHorizontalSumHistogramFuncINT(utils.SumINT32(values), eventId, executorId)

					event.countIndex = event.countAggregationColumnIndex

					worker.evaluateHorizontalCountHistogramFunc(int64(len(values)), eventId, executorId)

				} else if worker.aggregationFunc == MinMaxSum {

					event.currentIndex = event.minAggregationColumnIndex

					worker.evaluateHorizontalMinHistogramFuncINT(utils.MinINT32(values), eventId, executorId)

					event.currentIndex = event.maxAggregationColumnIndex

					worker.evaluateHorizontalMaxHistogramFuncINT(utils.MaxINT32(values), eventId, executorId)

					event.currentIndex = event.sumAggregationColumnIndex

					worker.evaluateHorizontalSumHistogramFuncINT(utils.SumINT32(values), eventId, executorId)

				} else if worker.aggregationFunc == MinMaxCount {

					event.currentIndex = event.minAggregationColumnIndex

					worker.evaluateHorizontalMinHistogramFuncINT(utils.MinINT32(values), eventId, executorId)

					event.currentIndex = event.maxAggregationColumnIndex

					worker.evaluateHorizontalMaxHistogramFuncINT(utils.MaxINT32(values), eventId, executorId)

					event.countIndex = event.countAggregationColumnIndex

					worker.evaluateHorizontalCountHistogramFunc(int64(len(values)), eventId, executorId)

				} else if worker.aggregationFunc == MinMax {

					event.currentIndex = event.minAggregationColumnIndex

					worker.evaluateHorizontalMinHistogramFuncINT(utils.MinINT32(values), eventId, executorId)

					event.currentIndex = event.maxAggregationColumnIndex

					worker.evaluateHorizontalMaxHistogramFuncINT(utils.MaxINT32(values), eventId, executorId)

				} else if worker.aggregationFunc == MinSumCount {

					event.currentIndex = event.minAggregationColumnIndex

					worker.evaluateHorizontalMinHistogramFuncINT(utils.MinINT32(values), eventId, executorId)

					event.currentIndex = event.sumAggregationColumnIndex

					worker.evaluateHorizontalSumHistogramFuncINT(utils.SumINT32(values), eventId, executorId)

					event.countIndex = event.countAggregationColumnIndex

					worker.evaluateHorizontalCountHistogramFunc(int64(len(values)), eventId, executorId)

				} else if worker.aggregationFunc == MinSum {

					event.currentIndex = event.minAggregationColumnIndex

					worker.evaluateHorizontalMinHistogramFuncINT(utils.MinINT32(values), eventId, executorId)

					event.currentIndex = event.sumAggregationColumnIndex

					worker.evaluateHorizontalSumHistogramFuncINT(utils.SumINT32(values), eventId, executorId)

				} else if worker.aggregationFunc == MinCount {

					event.currentIndex = event.minAggregationColumnIndex

					worker.evaluateHorizontalMinHistogramFuncINT(utils.MinINT32(values), eventId, executorId)

					event.countIndex = event.countAggregationColumnIndex

					worker.evaluateHorizontalCountHistogramFunc(int64(len(values)), eventId, executorId)

				} else if worker.aggregationFunc == Min {

					event.currentIndex = event.minAggregationColumnIndex

					if worker.columnDataTypes[executorId][event.minAggregationColumnIndex] == Int64 {

						worker.evaluateHorizontalMinHistogramFuncINT(utils.MinINT32(values), eventId, executorId)

					} else {

						worker.evaluateHorizontalMinHistogramFuncFLOAT(float64(utils.MinINT32(values)), eventId, executorId)
					}

				} else if worker.aggregationFunc == MaxSumCount {

					event.currentIndex = event.maxAggregationColumnIndex

					worker.evaluateHorizontalMaxHistogramFuncINT(utils.MaxINT32(values), eventId, executorId)

					event.currentIndex = event.sumAggregationColumnIndex

					worker.evaluateHorizontalSumHistogramFuncINT(utils.SumINT32(values), eventId, executorId)

					event.countIndex = event.countAggregationColumnIndex

					worker.evaluateHorizontalCountHistogramFunc(int64(len(values)), eventId, executorId)

				} else if worker.aggregationFunc == MaxSum {

					event.currentIndex = event.maxAggregationColumnIndex

					worker.evaluateHorizontalMaxHistogramFuncINT(utils.MaxINT32(values), eventId, executorId)

					event.currentIndex = event.sumAggregationColumnIndex

					worker.evaluateHorizontalSumHistogramFuncINT(utils.SumINT32(values), eventId, executorId)

				} else if worker.aggregationFunc == MaxCount {

					event.currentIndex = event.maxAggregationColumnIndex

					worker.evaluateHorizontalMaxHistogramFuncINT(utils.MaxINT32(values), eventId, executorId)

					event.countIndex = event.countAggregationColumnIndex

					worker.evaluateHorizontalCountHistogramFunc(int64(len(values)), eventId, executorId)

				} else if worker.aggregationFunc == Max {

					event.currentIndex = event.maxAggregationColumnIndex

					if worker.columnDataTypes[executorId][event.maxAggregationColumnIndex] == Int64 {

						worker.evaluateHorizontalMaxHistogramFuncINT(utils.MaxINT32(values), eventId, executorId)

					} else {

						worker.evaluateHorizontalMaxHistogramFuncFLOAT(float64(utils.MaxINT32(values)), eventId, executorId)
					}

				} else if worker.aggregationFunc == SumCount {

					event.currentIndex = event.sumAggregationColumnIndex

					event.countIndex = event.countAggregationColumnIndex

					if worker.columnDataTypes[executorId][event.sumAggregationColumnIndex] == Int64 {

						worker.evaluateHorizontalMeanHistogramFuncINT(utils.SumINT32(values), int64(len(values)), eventId, executorId)
					} else {

						worker.evaluateHorizontalMeanHistogramFuncFLOAT(float64(utils.SumINT32(values)), int64(len(values)), eventId, executorId)
					}

				} else if worker.aggregationFunc == Sum {

					event.currentIndex = event.sumAggregationColumnIndex

					if worker.columnDataTypes[executorId][event.sumAggregationColumnIndex] == Int64 {

						worker.evaluateHorizontalSumHistogramFuncINT(utils.SumINT32(values), eventId, executorId)
					} else {

						worker.evaluateHorizontalSumHistogramFuncFLOAT(float64(utils.SumINT32(values)), eventId, executorId)
					}

				} else if worker.aggregationFunc == Count {

					event.countIndex = event.countAggregationColumnIndex

					if worker.preAggregatedQuery {

						worker.evaluateHorizontalCountHistogramFunc(utils.SumINT32(values), eventId, executorId)
					} else {
						worker.evaluateHorizontalCountHistogramFunc(int64(len(values)), eventId, executorId)
					}
				}
			}
		}
	}

	return updatePosition, position
}

func (worker *Worker) setHorizontalHistoricalValueColumnINT32(eventId, executorId, batchSize int, column string, values []int32, bitmap bitmap.Bitmap) {

	event := worker.WorkerEvents[eventId]

	event.currentIndex = worker.setColumnContext(executorId, column+utils.ValueSuffix, Int64, true)

	// in case of when missing column is in first position than we have appended the string as a datatype as we don't know the exact datatype of the column
	if worker.columnDataTypes[executorId][event.currentIndex] == String {

		poolIndex, stringValues := worker.memoryPools[executorId].AcquireStringPool(len(values))

		defer worker.memoryPools[executorId].ReleaseStringPool(poolIndex)

		INT32ToStringValues(values, stringValues)

		if bitmap != nil {

			worker.setHorizontalHistoricalValueColumnStringConditional(eventId, executorId, column, stringValues, bitmap)

		} else {

			worker.setHorizontalHistoricalValueColumnString(eventId, executorId, batchSize, column, stringValues)
		}

		return
	}

	position := worker.memoryPoolPositions[executorId]

	if position > worker.maxHistoricalRecords {

		worker.queryAbort = true

		return
	}

	int64Values := worker.memoryPools[executorId].GetINT64Pool(worker.columnPoolIndices[executorId][event.currentIndex])

	if bitmap != nil {

		size, _ := bitmap.Max()

		batchSize = int(size + 1)

		if position+batchSize > len(int64Values) {

			int64Values = worker.memoryPools[executorId].ExpandINT64Pool(worker.columnPoolIndices[executorId][event.currentIndex], len(int64Values)+utils.OverflowLength)
		}

		for i := 0; i < batchSize; i++ {

			if bitmap.Contains(uint32(i)) {

				int64Values[position] = int64(values[i])

				position++

			}

		}

	} else {

		if position+batchSize > len(int64Values) {

			int64Values = worker.memoryPools[executorId].ExpandINT64Pool(worker.columnPoolIndices[executorId][event.currentIndex], len(int64Values)+utils.OverflowLength)
		}

		for i := 0; i < batchSize; i++ {

			int64Values[position] = int64(values[i])

			position++
		}
	}

}
