/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/* Change Logs:
* Date                   Author          		Notes
* 2025-05-09  			 Hardik Vala			MOTADATA-4911 Query2.0 First Merge
 */

package query

import (
	bytes2 "bytes"
	"errors"
	"fmt"
	"github.com/kelindar/bitmap"
	"motadatadatastore/codec"
	"motadatadatastore/datastore"
	"motadatadatastore/storage"
	"motadatadatastore/utils"
	"reflect"
	"strings"
	"time"
)

func (executor *Executor) parseHybridQuery(dataSource utils.MotadataMap, dataPoints []utils.MotadataMap, plugins []interface{}, histogramQuery bool) error {

	timestamp := time.Now().UnixMilli()

	var err error

	entities := dataSource.GetMapValue(Entities)

	executor.preAggregationQuery = !(executor.preAggregationJobId == utils.NotAvailable && len(entities) == 1)

	if executor.preAggregationJobId != utils.NotAvailable || (executor.toDateTime.Unix()-executor.fromDateTime.Unix()) <= 300 {

		executor.preAggregationQuery = false
	}

	if executor.drillDownQuery {

		executor.preAggregationQuery = false
	}

	var dates []time.Time

	if executor.datastoreType != utils.ObjectStatusFlapMetric {

		// qualified dates
		dates = utils.QualifyDates(executor.fromDateTime, executor.toDateTime)

	} else {

		dates = utils.QualifyWeeklyDates(executor.fromDateTime, executor.toDateTime)
	}

	externalGroupSize := 0

	if dataSource.Contains(VisualizationResultBy) {

		for _, grouping := range dataSource.GetSliceValue(VisualizationResultBy) {

			executor.GroupColumns[executor.GroupColumnElementSize] = codec.ToString(grouping)

			if executor.GroupColumns[executor.GroupColumnElementSize] == Group || executor.GroupColumns[executor.GroupColumnElementSize] == Tag {

				executor.externalGroupIndex = executor.GroupColumnElementSize
			}

			executor.GroupColumnElementSize++
		}

		if utils.QueryPlanLogging {

			executor.logQueryPlan(fmt.Sprintf("group by columns are %v", executor.GroupColumns[:executor.GroupColumnElementSize]))
		}

		// external grouping filters

		// external grouping
		if executor.externalGroupIndex >= 0 { //external grouping

			objectId := utils.Empty

			externalGroups := dataSource.GetMapValue(executor.GroupColumns[executor.externalGroupIndex])

			length := uint32(len(externalGroups))

			ordinals := map[string]int{}

			for externalGroup, externalGroupings := range externalGroups {

				externalGroupSize++

				ordinal, found := ordinals[externalGroup]

				if !found {

					ordinal = len(ordinals)

					ordinals[externalGroup] = ordinal

					executor.externalGroupFilters[executor.externalGroupFilterElementSize] = externalGroup

					executor.externalGroupHashes[executor.externalGroupFilterElementSize] = utils.GetHash64([]byte(externalGroup))

					executor.externalGroupFilterElementSize++
				}

				for _, entity := range externalGroupings.([]interface{}) {

					objectId = codec.ToString(entity)

					if _, ok := executor.externalGroupFilterOrdinals[objectId]; !ok {

						executor.externalGroupFilterOrdinals[objectId] = NewCompactedBitmap(length)
					}

					executor.externalGroupFilterOrdinals[objectId].Set(uint32(ordinal))
				}
			}

			if strings.Contains(objectId, utils.GroupSeparator) {

				executor.externalInstanceGrouping = true
			}

			if externalGroupSize == 0 {

				return errors.New(utils.ErrorGroupingEntitiesRequired)
			}
		}

	} else if executor.category == Grid || executor.category == Chart || executor.category == TopN || executor.category == Map || executor.category == Sankey {

		return errors.New(fmt.Sprintf(utils.ErrorGroupingRequired, executor.category))
	}

	filters := dataSource.GetMapValue(Filters)

	dataFilter := filters.GetMapValue(DataFilter)

	resultFilter := filters.GetMapValue(ResultFilter)

	if len(resultFilter) > 0 {

		executor.resultFilter = true
	}

	if executor.preAggregationJobId == utils.NotAvailable {

		for index, dataPoint := range dataPoints {

			column := dataPoint.GetStringValue(DataPoint)

			if strings.Contains(column, utils.InstanceSeparator) {

				executor.innerGroupingField = strings.Split(column, utils.InstanceSeparator)[0]

				executor.instanceQuery = true
			}

			//Replace the column with shadow counter original value
			if datastore.IsShadowCounter(column) {

				executor.shadowCounterIndices.Add(index)

				column = datastore.FlipShadowCounter(column)
			}

			if index == 0 && executor.preAggregationQuery {

				probes := len(entities) * 2

				if executor.instanceQuery {

					entityKeys := dataPoint.GetMapValue(EntityKeys)

					if len(entityKeys) == 0 {

						probes = len(entities) * 10 * 2
					} else {

						probes = len(entityKeys) * 2
					}
				}

				if utils.EnvironmentType != utils.DatastoreTestEnvironment {

					if int(executor.toDateTime.Unix()-executor.fromDateTime.Unix()) < 86400 {

						executor.preAggregationQuery = probes*len(dataPoints) > 200

					} else {

						executor.preAggregationQuery = probes*len(dataPoints) > 50
					}

					if utils.QueryPlanLogging {

						executor.logQueryPlan(fmt.Sprintf("after probing aggregation flag is %v", executor.preAggregationQuery))
					}
				}
			}

			// for * query or if column not qualified in aggregation mark aggregation flag as no
			if column == utils.All || !datastore.IsAggregationMetric(column) {

				executor.preAggregationQuery = false

				if utils.QueryPlanLogging && !datastore.IsAggregationMetric(column) {

					executor.logQueryPlan(fmt.Sprintf(WarningColumnAggregationMissing, column))
				}

				break
			}
		}

		if executor.preAggregationQuery {

			if executor.queryEngineType == Metric || executor.queryEngineType == AIOps {

				if len(dataFilter) > 0 {

					executor.preFilterQuery = true

					err = executor.evaluateVerticalFilter(entities, dataFilter, plugins)

					if err != nil {

						return err
					}

					if len(executor.fields) == 0 {

						return errors.New("failed to qualify filter keys") //need to provide reason for failing to qualify...
					}

					executor.preAggregationQuery = utils.EnvironmentType == utils.DatastoreTestEnvironment || len(executor.fields)*len(dataPoints) > 200
				}

				if executor.preAggregationQuery {

					return executor.parseHorizontalQuery(dataSource, dataPoints, plugins, histogramQuery)
				}
			}
		} else if len(dataFilter) > 0 {

			err = executor.evaluateVerticalFilter(entities, dataFilter, plugins)

			if err != nil {

				return err
			}

			if len(executor.fields) > 0 {

				executor.preFilterQuery = true

			} else if executor.datastoreType == utils.MetricPolicy || executor.datastoreType == utils.EventPolicy || executor.datastoreType == utils.TrapFlapHistory {

				executor.preFilterQuery = false

			} else { //if filter was failed to apply then throw error

				return errors.New("failed to qualify filter keys") //need to provide reason for failing to qualify...
			}
		}
	} else {

		executor.preAggregationQuery = false
	}

	executor.preAggregationTimeInterval = executor.chooseAggregationTimeInterval(utils.UnixToSeconds(executor.fromDateTime.Unix()), utils.UnixToSeconds(executor.toDateTime.Unix()), false)

	//apply the interval specified in the query if the key is present.
	if executor.preAggregationJobId != utils.NotAvailable && executor.request.Contains(utils.Interval) {

		executor.preAggregationTimeInterval = executor.request.GetIntValue(utils.Interval)
	}

	if utils.QueryPlanLogging {

		executor.logQueryPlan(fmt.Sprintf("qualified interval is %v", executor.preAggregationTimeInterval))

		executor.logQueryPlan(fmt.Sprintf("aggregation query flag is %v", executor.preAggregationQuery))
	}

	executor.keyPoolIndex, executor.keys = executor.memoryPool.AcquireStringPool(utils.NotAvailable)

	executor.probes = 2

	if executor.drillDownQuery {

		columnPoolIndex, columns := executor.memoryPool.AcquireStringPool(len(dataPoints))

		defer executor.memoryPool.ReleaseStringPool(columnPoolIndex)

		column := utils.Empty

		var entityKeys utils.MotadataMap

		if executor.datastoreType == utils.TrapFlapHistory || executor.datastoreType == utils.NetRouteMetric {

			entityKeys = make(utils.MotadataMap)
		}

		resolveEntities := false

		for index, dataPoint := range dataPoints {

			column = dataPoint.GetStringValue(DataPoint)

			if index == 0 {

				executor.outerGroupingField = utils.EventSource

				if strings.Contains(column, utils.InstanceSeparator) {

					executor.innerGroupingField = strings.Split(column, utils.InstanceSeparator)[0]

					executor.instanceQuery = true
				}

				if executor.plugin == datastore.NetRouteStatusPlugin || executor.plugin == datastore.NetRouteMetricPlugin || executor.plugin == datastore.NetRouteEventPlugin {

					executor.outerGroupingField = utils.NetRouteId

					resolveEntities = true

				} else if executor.datastoreType == utils.PerformanceMetric || executor.datastoreType == utils.ObjectStatusMetric || executor.datastoreType == utils.ObjectStatusFlapMetric {

					executor.outerGroupingField = utils.ObjectId

				} else if executor.datastoreType == utils.TrapFlapHistory {

					executor.innerGroupingField = utils.TrapOID

					resolveEntities = true
				}

				executor.Columns[executor.ColumnElementSize] = utils.TimestampKey

				executor.ColumnElementSize++

				executor.Columns[executor.ColumnElementSize] = executor.outerGroupingField

				executor.ColumnElementSize++

				if len(executor.innerGroupingField) > 0 {

					executor.Columns[executor.ColumnElementSize] = executor.innerGroupingField

					executor.ColumnElementSize++
				}
			}

			//Replace the column with shadow counter original value
			if datastore.IsShadowCounter(column) {

				executor.shadowCounterIndices.Add(index)

				column = datastore.FlipShadowCounter(column)
			}

			executor.Columns[executor.ColumnElementSize] = column + utils.ValueSuffix

			executor.ColumnElementSize++

			columns[index] = column

			if resolveEntities {

				for key, value := range dataPoint.GetMapValue(EntityKeys) {

					utils.Split(key, utils.KeySeparator, executor.tokenizers[0])

					entityKeys[executor.tokenizers[0].Tokens[0]+utils.KeySeparator+executor.tokenizers[0].Tokens[1]] = value
				}
			}
		}

		executor.probes = 2 * len(dataPoints)

		if executor.datastoreType == utils.ObjectStatusFlapMetric {

			for index := 0; index < len(dates); index++ {

				executor.qualifyDrillDownVerticalKeys(entities, plugins, dates[index], utils.Empty, columns)
			}
		} else {

			if len(entityKeys) > 0 {

				clear(entities)

				for key, value := range entityKeys {

					entities[key] = value
				}
			}

			for index := len(dates) - 1; index >= 0; index-- {

				executor.qualifyDrillDownVerticalKeys(entities, plugins, dates[index], utils.Empty, columns)
			}
		}

	} else {

		uniqueColumns := map[string]struct{}{}

		for index, dataPoint := range dataPoints {

			column := dataPoint.GetStringValue(DataPoint)

			aggregator := dataPoint.GetStringValue(Aggregator)

			if statisticalFunc := dataPoint.GetStringValue(StatisticalFunc); statisticalFunc != utils.Empty {

				executor.statisticalFuncs[index] = statisticalFunc

			}

			entityKeys := dataPoint.GetMapValue(EntityKeys)

			if aggregator == Sparkline {

				continue
			}

			//Replace the column with shadow counter original value
			if datastore.IsShadowCounter(column) {

				executor.shadowCounterIndices.Add(index)

				column = datastore.FlipShadowCounter(column)

				entityKeys = nil
			}

			executor.aggregations[aggregator] += 1

			executor.columnIndices[column+utils.DotSeparator+aggregator] = index

			// As of now * will work on vertical records with limitation of not having any filter conditions + only one aggregation func support

			if column == utils.All {

				plugins = dataPoint.GetSliceValue(utils.Plugins)

				for i := len(dates) - 1; i >= 0; i-- {

					for pluginIndex := range plugins {

						plugin := codec.ToString(plugins[pluginIndex])

						if store := executor.isVerticalStoreAvailable(dates[i], plugin, aggregator); store != nil && store.GetDatastoreType() != utils.StaticMetric {

							start := executor.keyElementSize

							executor.qualifyAllVerticalKeys(store, entities, uniqueColumns)

							executor.qualifyWorkerEventKeyIndices(start, store, dates[i], plugin, aggregator)

						}

					}
				}

				executor.timeBoundQuery = true

				executor.Columns[executor.ColumnElementSize] = datastore.Timestamp + utils.KeySeparator + LastFunc

				executor.ColumnElementSize++

				for uniqueColumn := range uniqueColumns {

					executor.Columns[executor.ColumnElementSize] = uniqueColumn + utils.KeySeparator + aggregator

					executor.ColumnElementSize++
				}

				break

			} else {

				if _, ok := uniqueColumns[column]; ok {

					executor.Columns[executor.ColumnElementSize] = column + utils.KeySeparator + aggregator

					executor.ColumnElementSize++

					continue
				}

				uniqueColumns[column] = struct{}{}

				executor.Columns[executor.ColumnElementSize] = column + utils.KeySeparator + aggregator

				executor.ColumnElementSize++

				executor.qualifyVerticalKeys(dataPoint.GetMapValue(Entities), entityKeys, dataPoint.GetSliceValue(utils.Plugins), dates, column, aggregator)
			}
		}

	}

	executor.applyGranularity(histogramQuery, timestamp)

	return nil
}

// ex. key qualification is like 1^cpu, 1^memory, 2^cpu, 2^memory
func (executor *Executor) qualifyDrillDownVerticalKeys(entities utils.MotadataMap, plugins []interface{}, date time.Time, aggregator string, columns []string) {

	// filter query
	if executor.preFilterQuery {

		for pluginIndex := range plugins {

			plugin := codec.ToString(plugins[pluginIndex])

			if store := executor.isVerticalStoreAvailable(date, plugin, aggregator); store != nil {

				start := executor.keyElementSize

				for key := range executor.fields {

					utils.Split(key, utils.KeySeparator, executor.tokenizers[0])

					if entities.Contains(executor.tokenizers[0].Tokens[0]) && strings.EqualFold(codec.ToString(entities[executor.tokenizers[0].Tokens[0]]), plugin) {

						executor.buildDrillDownVerticalKey(store, executor.tokenizers[0].Tokens[0], executor.tokenizers[0].Tokens[1], columns)
					}
				}

				executor.qualifyWorkerEventKeyIndices(start, store, date, plugin, aggregator)
			}
		}
	} else {

		if executor.instanceQuery {

			for pluginIndex := range plugins {

				plugin := codec.ToString(plugins[pluginIndex])

				if store := executor.isVerticalStoreAvailable(date, plugin, aggregator); store != nil {

					/*
						for ex. if counter is interface~in.packets first we will get all the instance name with checking column~instance.name in string column store
					*/

					garbageStore := datastore.GetStore(plugin, utils.None, false, true, executor.encoder, executor.tokenizers[0])

					if garbageStore == nil {

						continue
					}

					buffers, err := garbageStore.GetContainKeys([]byte(executor.innerGroupingField+"~instance.name"), false)

					if err != nil {

						executor.logError(err.Error())

						continue
					}

					start := executor.keyElementSize

					for _, bytes := range buffers {

						utils.Split(string(bytes), utils.KeySeparator, executor.tokenizers[0])

						if entities.Contains(executor.tokenizers[0].Tokens[0]) {

							executor.buildDrillDownVerticalKey(store, executor.tokenizers[0].Tokens[0], executor.tokenizers[0].Tokens[1], columns)
						}
					}

					executor.qualifyWorkerEventKeyIndices(start, store, date, plugin, aggregator)
				}
			}
		} else {

			for pluginIndex := range plugins {

				plugin := codec.ToString(plugins[pluginIndex])

				if store := executor.isVerticalStoreAvailable(date, plugin, aggregator); store != nil {

					start := executor.keyElementSize

					if executor.datastoreType == utils.EventPolicy {

						executor.buildDrillDownVerticalKey(store, executor.value, utils.Empty, columns)

					} else {

						for entity, pluginId := range entities {

							if strings.EqualFold(codec.ToString(pluginId), plugin) {

								executor.buildDrillDownVerticalKey(store, entity, utils.Empty, columns)
							}
						}
					}

					executor.qualifyWorkerEventKeyIndices(start, store, date, plugin, aggregator)
				}
			}
		}
	}
}

// ex. key qualification is like 1^cpu, 2^cpu, 1^memory, 2^memory
func (executor *Executor) qualifyVerticalKeys(entities utils.MotadataMap, entityKeys utils.MotadataMap, plugins []interface{}, dates []time.Time, column, aggregator string) {

	if executor.preAggregationJobId != utils.NotAvailable { // aggregation query have always entity keys so no need for any probe for it hence build key directs and return

		plugin := codec.ToString(plugins[0])

		if entityKeys != nil && len(entityKeys) > 0 {

			for index := len(dates) - 1; index >= 0; index-- {

				if store := executor.isVerticalStoreAvailable(dates[index], plugin, aggregator); store != nil {

					start := executor.keyElementSize

					for key := range entityKeys {

						executor.buildVerticalKey(store, key)
					}

					executor.qualifyWorkerEventKeyIndices(start, store, dates[index], plugin, aggregator)
				}
			}
		}

		return
	}

	instances := map[string][][]byte{}

	if aggregator == LastFunc {

		for index := len(dates) - 1; index >= 0; index-- {

			date := dates[index]

			// filter query
			if executor.preFilterQuery {

				for pluginIndex := range plugins {

					plugin := codec.ToString(plugins[pluginIndex])

					if store := executor.isVerticalStoreAvailable(date, plugin, aggregator); store != nil {

						start := executor.keyElementSize

						for key := range executor.fields {

							utils.Split(key, utils.KeySeparator, executor.tokenizers[0])

							if entities.Contains(executor.tokenizers[0].Tokens[0]) && strings.EqualFold(codec.ToString(entities[executor.tokenizers[0].Tokens[0]]), plugin) {

								/*
									for scalar key is monitor + ^ + column and for instance key is monitor + ^ + instance + ^ + column
								*/

								executor.evaluateLastFuncKeys(store, datastore.GetMetricKey(executor.tokenizers[0].Tokens[0], column, executor.tokenizers[0].Tokens[1]), plugin)
							}
						}

						executor.qualifyWorkerEventKeyIndices(start, store, dates[index], plugin, aggregator)
					}
				}
			} else {

				// received key from motadata side, so no need to build key from datastore end.
				if entityKeys != nil && len(entityKeys) > 0 {

					for pluginIndex := range plugins {

						plugin := codec.ToString(plugins[pluginIndex])

						if store := executor.isVerticalStoreAvailable(date, plugin, aggregator); store != nil {

							start := executor.keyElementSize

							for key, pluginId := range entityKeys {

								if strings.EqualFold(codec.ToString(pluginId), plugin) {

									utils.Split(key, utils.KeySeparator, executor.tokenizers[0])

									executor.evaluateLastFuncKeys(store, key, plugin)
								}
							}

							executor.qualifyWorkerEventKeyIndices(start, store, dates[index], plugin, aggregator)
						}
					}
				} else {

					// instance query
					if executor.instanceQuery {

						if executor.externalInstanceGrouping {

							for pluginIndex := range plugins {

								plugin := codec.ToString(plugins[pluginIndex])

								if datastore.IsStoreAvailable(plugin) {

									if garbageStore := datastore.GetStore(plugin, utils.StaticMetric, false, true, executor.encoder, executor.tokenizers[0]); garbageStore != nil {

										start := executor.keyElementSize

										qualifiedKeys := utils.MotadataMap{}

										for key := range executor.externalGroupFilterOrdinals {

											utils.Split(key, utils.GroupSeparator, executor.tokenizers[0])

											key = datastore.GetMetricKey(executor.tokenizers[0].Tokens[0], column, executor.tokenizers[0].Tokens[1])

											if pluginId, ok := entities[executor.tokenizers[0].Tokens[0]]; ok && strings.EqualFold(plugin, codec.ToString(pluginId)) && !qualifiedKeys.Contains(key) {

												executor.evaluateLastFuncKeys(garbageStore, key, plugin)
											}
										}

										executor.qualifyWorkerEventKeyIndices(start, garbageStore, dates[index], plugin, aggregator)
									}
								}
							}

						} else {

							for pluginIndex := range plugins {

								plugin := codec.ToString(plugins[pluginIndex])

								if store := executor.isVerticalStoreAvailable(date, plugin, aggregator); store != nil {

									garbageStore := datastore.GetStore(plugin, utils.None, false, true, executor.encoder, executor.tokenizers[0])

									if garbageStore == nil {

										continue
									}

									/*
										for ex. if counter is interface~in.packets first we will get all the instance name with checking column~instance.name in string column store
									*/

									utils.Split(column, utils.InstanceSeparator, executor.tokenizers[0])

									var buffers [][]byte

									found := false

									if buffers, found = instances[plugin]; !found {

										var err error

										buffers, err = garbageStore.GetContainKeys([]byte(executor.tokenizers[0].Tokens[0]+"~instance.name"), false)

										if err != nil {

											executor.logError(err.Error())

											continue
										}

										instances[plugin] = buffers
									}

									start := executor.keyElementSize

									qualifiedKeys := utils.MotadataMap{}

									for _, bytes := range buffers {

										utils.Split(string(bytes), utils.KeySeparator, executor.tokenizers[0])

										key := datastore.GetMetricKey(executor.tokenizers[0].Tokens[0], column, executor.tokenizers[0].Tokens[1])

										if !qualifiedKeys.Contains(key) && entities.Contains(executor.tokenizers[0].Tokens[0]) {

											qualifiedKeys[key] = struct{}{}

											executor.evaluateLastFuncKeys(store, key, plugin)
										}
									}

									executor.qualifyWorkerEventKeyIndices(start, store, dates[index], plugin, aggregator)
								}

							}
						}
					} else {

						for pluginIndex := range plugins {

							plugin := codec.ToString(plugins[pluginIndex])

							if store := executor.isVerticalStoreAvailable(date, plugin, aggregator); store != nil {

								start := executor.keyElementSize

								if executor.datastoreType == utils.MetricPolicy || executor.datastoreType == utils.EventPolicy {

									executor.evaluateLastFuncKeys(store, executor.value+utils.KeySeparator+column, plugin)

								} else {

									for entity, pluginId := range entities {

										if strings.EqualFold(codec.ToString(pluginId), plugin) {

											/*
												key is monitor + ^ + column
											*/
											executor.evaluateLastFuncKeys(store, datastore.GetMetricKey(entity, column, utils.Empty), plugin)
										}
									}
								}

								executor.qualifyWorkerEventKeyIndices(start, store, dates[index], plugin, aggregator)
							}
						}
					}
				}
			}
		}
	} else {

		for index := len(dates) - 1; index >= 0; index-- {

			date := dates[index]

			// filter query
			if executor.preFilterQuery {

				for pluginIndex := range plugins {

					plugin := codec.ToString(plugins[pluginIndex])

					if store := executor.isVerticalStoreAvailable(date, plugin, aggregator); store != nil {

						start := executor.keyElementSize

						for key := range executor.fields {

							utils.Split(key, utils.KeySeparator, executor.tokenizers[0])

							if entities.Contains(executor.tokenizers[0].Tokens[0]) && strings.EqualFold(codec.ToString(entities[executor.tokenizers[0].Tokens[0]]), plugin) {

								/*
									for scalar key is monitor + ^ + column and for instance key is monitor + ^ + instance + ^ + column
								*/

								executor.buildVerticalKey(store, datastore.GetMetricKey(executor.tokenizers[0].Tokens[0], column, executor.tokenizers[0].Tokens[1]))
							}
						}

						executor.qualifyWorkerEventKeyIndices(start, store, dates[index], plugin, aggregator)
					}
				}
			} else {

				// received key from motadata side, so no need to build key from datastore end.
				if entityKeys != nil && len(entityKeys) > 0 {

					for pluginIndex := range plugins {

						plugin := codec.ToString(plugins[pluginIndex])

						if store := executor.isVerticalStoreAvailable(date, plugin, aggregator); store != nil {

							start := executor.keyElementSize

							for key, pluginId := range entityKeys {

								if strings.EqualFold(codec.ToString(pluginId), plugin) {

									executor.buildVerticalKey(store, key)
								}
							}

							executor.qualifyWorkerEventKeyIndices(start, store, dates[index], plugin, aggregator)
						}
					}
				} else {

					// instance query
					if len(executor.innerGroupingField) > 0 {

						if executor.externalInstanceGrouping {

							for pluginIndex := range plugins {

								plugin := codec.ToString(plugins[pluginIndex])

								if store := executor.isVerticalStoreAvailable(date, plugin, aggregator); store != nil {

									qualifiedKeys := utils.MotadataMap{}

									start := executor.keyElementSize

									for key := range executor.externalGroupFilterOrdinals {

										utils.Split(key, utils.GroupSeparator, executor.tokenizers[0])

										key = datastore.GetMetricKey(executor.tokenizers[0].Tokens[0], column, executor.tokenizers[0].Tokens[1])

										if pluginId, ok := entities[executor.tokenizers[0].Tokens[0]]; ok && strings.EqualFold(plugin, codec.ToString(pluginId)) && !qualifiedKeys.Contains(key) {

											executor.buildVerticalKey(store, key)
										}
									}

									executor.qualifyWorkerEventKeyIndices(start, store, dates[index], plugin, aggregator)
								}
							}

						} else {

							for pluginIndex := range plugins {

								plugin := codec.ToString(plugins[pluginIndex])

								if store := executor.isVerticalStoreAvailable(date, plugin, aggregator); store != nil {

									garbageStore := datastore.GetStore(plugin, utils.None, false, true, executor.encoder, executor.tokenizers[0])

									if garbageStore == nil {

										continue
									}

									/*
										for ex. if counter is interface~in.packets first we will get all the instance name with checking column~instance.name in string column store
									*/

									utils.Split(column, utils.InstanceSeparator, executor.tokenizers[0])

									var buffers [][]byte

									found := false

									if buffers, found = instances[plugin]; !found {

										var err error

										buffers, err = garbageStore.GetContainKeys([]byte(executor.tokenizers[0].Tokens[0]+"~instance.name"), false)

										if err != nil {

											executor.logError(err.Error())

											continue
										}

										instances[plugin] = buffers
									}

									qualifiedKeys := utils.MotadataMap{}

									start := executor.keyElementSize

									for _, bytes := range buffers {

										utils.Split(string(bytes), utils.KeySeparator, executor.tokenizers[0])

										key := datastore.GetMetricKey(executor.tokenizers[0].Tokens[0], column, executor.tokenizers[0].Tokens[1])

										if !qualifiedKeys.Contains(key) && entities.Contains(executor.tokenizers[0].Tokens[0]) {

											qualifiedKeys[key] = struct{}{}

											executor.buildVerticalKey(store, key)
										}
									}

									executor.qualifyWorkerEventKeyIndices(start, store, dates[index], plugin, aggregator)
								}
							}
						}
					} else {

						for pluginIndex := range plugins {

							plugin := codec.ToString(plugins[pluginIndex])

							if store := executor.isVerticalStoreAvailable(date, plugin, aggregator); store != nil {

								start := executor.keyElementSize

								if executor.datastoreType == utils.MetricPolicy || executor.datastoreType == utils.EventPolicy {

									executor.buildVerticalKey(store, executor.value+utils.KeySeparator+column)

								} else {
									for entity, pluginId := range entities {

										if strings.EqualFold(codec.ToString(pluginId), plugin) {

											/*
												key is monitor + ^ + column
											*/

											executor.buildVerticalKey(store, datastore.GetMetricKey(entity, column, utils.Empty))
										}
									}
								}

								executor.qualifyWorkerEventKeyIndices(start, store, dates[index], plugin, aggregator)
							}
						}
					}
				}
			}
		}
	}
}

func (executor *Executor) evaluateLastFuncKeys(store *storage.Store, key, plugin string) {

	/*
		why last func condition - for last func we need to check in Raw records only, no need to check in Aggregation

		1.  check that filtered key map not contains key (qualified Key + group separator + external group - in case of external groups only else "")

		2.  if numeric store not contains key then we'll check in string store also to qualify keys - this condition is to cover the scenario of particular monitor's data conversion like
			system.cpu.percent in cisco systems gives float value but in HP it's string value, so to qualify that monitor we need to check it here...
	*/

	if _, ok := executor.fields[key]; !ok {

		executor.KeyBuffers[0] = []byte(key + datastore.DefaultKeyPart)

		found := false

		if found, _ = store.Has(executor.KeyBuffers[0], executor.tokenizers[0]); found {

			executor.fields[key] = len(executor.fields)

			executor.buildVerticalKey(store, key)

		} else if garbageStore := datastore.GetStore(plugin, utils.StaticMetric, false, true, executor.encoder, executor.tokenizers[0]); garbageStore != nil { // numeric store exist but key not contains in numeric store, so need to probe in string store also
			//  for only string counters exists in plugin

			if found, _ = garbageStore.Has(executor.KeyBuffers[0], executor.tokenizers[0]); found {

				executor.fields[key] = len(executor.fields)

				executor.buildVerticalKey(garbageStore, key)
			}
		}
	}
}

func (executor *Executor) qualifyAllVerticalKeys(store *storage.Store, entities utils.MotadataMap, uniqueColumns map[string]struct{}) {

	keyBuffers, err := store.GetKeys(nil, nil, false, codec.Invalid)

	if err != nil {

		executor.logError(err.Error())

		return
	}

	for _, bytes := range keyBuffers {

		key := string(bytes)

		if !strings.Contains(key, "^time^") {

			utils.Split(key, utils.KeySeparator, executor.tokenizers[0])

			key = strings.Join(executor.tokenizers[0].Tokens[:executor.tokenizers[0].Counts-1], utils.KeySeparator) //remove the ^part suffix

			if _, found := executor.fields[key]; !found && entities.Contains(executor.tokenizers[0].Tokens[0]) {

				uniqueColumns[executor.tokenizers[0].Tokens[executor.tokenizers[0].Counts-2]] = struct{}{}

				executor.fields[key] = len(executor.fields)

				executor.buildVerticalKey(store, key)
			}
		}
	}
}

func (executor *Executor) buildVerticalKey(store *storage.Store, key string) {

	if executor.preAggregationJobId != utils.NotAvailable {

		keyBytes := []byte(key)

		partIndex := bytes2.LastIndexByte(keyBytes, utils.KeySeparatorByte)

		executor.qualifyDistributionKeys(key, string(keyBytes[:partIndex])+utils.KeySeparator+datastore.Time+utils.KeySeparator+string(keyBytes[partIndex+1:]))

		return
	}

	parts := 1

	if store.GetDatastoreType() != utils.StaticMetric {

		parts = getParts(utils.GetHash64([]byte(key)), store)

	}

	for index := 0; index < parts; index++ {

		part := codec.INTToStringValue(index)

		executor.qualifyDistributionKeys(key+utils.KeySeparator+part, datastore.GetTimeTickKey(key)+utils.KeySeparator+part)
	}
}

func (executor *Executor) buildDrillDownVerticalKey(store *storage.Store, entity, instance string, columns []string) {

	parts := 1

	if store.GetDatastoreType() != utils.StaticMetric {

		parts = getParts(utils.GetHash64([]byte(datastore.GetMetricKey(entity, columns[0], instance))), store)
	}

	for index := 0; index < parts; index++ {

		part := codec.INTToStringValue(index)

		for _, column := range columns {

			key := datastore.GetMetricKey(entity, column, instance)

			executor.qualifyDistributionKeys(key+utils.KeySeparator+part, datastore.GetTimeTickKey(key)+utils.KeySeparator+part)
		}
	}
}

func (executor *Executor) isVerticalStoreAvailable(date time.Time, plugin string, aggregator string) *storage.Store {

	storeName := utils.UnixMillisToDate(date.UnixMilli()) + utils.HyphenSeparator + datastore.VerticalStore +
		utils.HyphenSeparator + plugin

	if datastore.IsStoreAvailable(storeName) {

		return datastore.GetStore(storeName, utils.None, false, true, executor.encoder, executor.tokenizers[0])

	}

	if aggregator == LastFunc {

		/*
			use case := only string counters plugin, so what happen if timeline is more than one day?

			ex. fan sensor in network device contains only string counters, so let's assume if user fire query for last one month (30 days) in that case if we want to avoid rest 29 days store probe then need this condition
		*/

		if datastore.IsStoreAvailable(plugin) {

			return datastore.GetStore(plugin, utils.StaticMetric, false, true, executor.encoder, executor.tokenizers[0])
		}
	}

	return nil
}

func (executor *Executor) evaluateVerticalFilter(entities, dataFilters utils.MotadataMap, plugins []interface{}) error {

	if executor.datastoreType == utils.StatusFlapHistory || executor.datastoreType == utils.MetricPolicy || executor.datastoreType == utils.EventPolicy ||
		executor.datastoreType == utils.TrapFlapHistory {

		return nil
	}

	t := time.Now().UnixMilli()

	groups := dataFilters.GetMapListValue(Groups)

	err := executor.evaluateStaticVerticalFilter(groups, entities)

	if err != nil {

		return err
	}

	operator := dataFilters.GetStringValue(Operator)

	included := dataFilters.GetStringValue(Filter) == include

	index, poolIndices := executor.memoryPool.AcquireINTPool(utils.NotAvailable)

	elementSize := 0

	defer executor.memoryPool.ReleaseINTPool(index)

	bitmaps := make([]bitmap.Bitmap, len(groups))

	for groupIndex, group := range groups {

		excluded := group.GetStringValue(Filter) == exclude

		if !included {

			excluded = !excluded
		}

		conditions := group.GetMapListValue(Conditions)

		operandValues := make([][]string, len(conditions))

		for conditionIndex, condition := range conditions {

			utils.Split(condition.GetStringValue(Operand), utils.KeySeparator, executor.tokenizers[0])

			operand := executor.tokenizers[0].Tokens[0]

			poolIndex, nestedOperandValues := executor.memoryPool.AcquireStringPool(utils.NotAvailable)

			valueElementSize := 0

			searchableColumn := false

			if datastore.IsSearchableColumn(operand) && condition.GetStringValue(Operator) != in {

				if store := datastore.GetStore(operand+utils.HyphenSeparator+datastore.VerticalStore+utils.HyphenSeparator+codec.INTToStringValue(int(codec.String)),
					utils.None, false, true, executor.encoder, executor.tokenizers[1]); store != nil {

					mappingStore := datastore.GetStore(datastore.Object+utils.HyphenSeparator+datastore.Mappings, utils.Mapping,
						false, true, executor.encoder, executor.tokenizers[1])

					if mappingStore != nil {

						executor.KeyBuffers[0] = []byte(strings.ToLower(condition.GetStringValue(utils.Value)))

						var buffers [][]byte

						buffers, err = store.GetContainKeys(executor.KeyBuffers[0], excluded)

						if err != nil {

							executor.logError(err.Error())

							continue
						}

						if len(buffers) > 0 {

							qualifiedIndex := 0

							for k := range buffers {

								executor.KeyBuffers[qualifiedIndex] = buffers[k]

								qualifiedIndex++

								if qualifiedIndex == utils.MaxWorkerEventKeyGroupLength {

									searchableColumn = executor.filterSearchableVerticalKeys(store, mappingStore, entities, nestedOperandValues, qualifiedIndex, &valueElementSize)

									qualifiedIndex = 0
								}
							}

							if qualifiedIndex > 0 {

								searchableColumn = executor.filterSearchableVerticalKeys(store, mappingStore, entities, nestedOperandValues, qualifiedIndex, &valueElementSize)
							}
						}
					}
				}
			}

			if !searchableColumn {

				for j := range plugins {

					plugin := codec.ToString(plugins[j])

					if store := datastore.GetStore(plugin, utils.None, false, true, executor.encoder, executor.tokenizers[1]); store != nil {

						qualifiedIndex := 0

						var buffers [][]byte

						buffers, err = store.GetContainKeys([]byte(operand), false)

						if err != nil {

							executor.logError(err.Error())

							continue
						}

						if len(buffers) > 0 {

							for _, bytes := range buffers {

								utils.Split(string(bytes), utils.KeySeparator, executor.tokenizers[0])

								if executor.tokenizers[0].Counts > 0 && entities.Contains(executor.tokenizers[0].Tokens[0]) {

									executor.KeyBuffers[qualifiedIndex] = bytes

									qualifiedIndex++

									if qualifiedIndex == utils.MaxWorkerEventKeyGroupLength {

										executor.filterVerticalKeys(store, condition, !excluded, qualifiedIndex, nestedOperandValues, &valueElementSize)

										qualifiedIndex = 0
									}
								}
							}

							if qualifiedIndex > 0 {

								executor.filterVerticalKeys(store, condition, !excluded, qualifiedIndex, nestedOperandValues, &valueElementSize)
							}
						}
					}
				}
			}

			operandValues[conditionIndex] = nestedOperandValues[:valueElementSize]

			poolIndices[elementSize] = poolIndex

			elementSize++
		}

		// max length of Values
		maxLength := len(operandValues[0])

		for i := range operandValues {

			if maxLength < len(operandValues[i]) {

				maxLength = len(operandValues[i])
			}
		}

		for i := 0; i < maxLength; i++ {

			for conditionIndex := range operandValues {

				if len(operandValues[conditionIndex]) > i {

					if _, found := executor.fields[operandValues[conditionIndex][i]]; !found {

						executor.fields[operandValues[conditionIndex][i]] = len(executor.fields)
					}
				}

			}
		}

		// nested bit maps is used for each group conditions union and intersection
		nestedConditionBitmaps := make([]bitmap.Bitmap, len(conditions))

		for i := 0; i < len(conditions); i++ {

			nestedConditionBitmaps[i] = bitmap.Bitmap{0}
		}

		//set position in bitmap if filters contains slice value
		for i := 0; i < maxLength; i++ {

			for conditionIndex := range operandValues {

				if len(operandValues[conditionIndex]) > i {

					if value, found := executor.fields[operandValues[conditionIndex][i]]; found {

						nestedConditionBitmaps[conditionIndex].Set(uint32(value))
					}
				}
			}
		}

		if len(conditions) > 1 {

			// union
			if group.GetStringValue(Operator) == or {

				nestedConditionBitmaps[0].Or(nestedConditionBitmaps[1])

				if len(conditions) == 3 {

					nestedConditionBitmaps[0].Or(nestedConditionBitmaps[2])
				}

			} else { // intersection
				nestedConditionBitmaps[0].And(nestedConditionBitmaps[1])

				if len(conditions) == 3 {

					nestedConditionBitmaps[0].And(nestedConditionBitmaps[2])
				}
			}
		}

		bitmaps[groupIndex] = nestedConditionBitmaps[0]
	}

	if len(groups) > 1 {

		// union
		if operator == or {

			for i := 1; i < len(bitmaps); i++ {

				bitmaps[0].Or(bitmaps[i])
			}
		} else { // intersection
			for i := 1; i < len(bitmaps); i++ {

				bitmaps[0].And(bitmaps[i])
			}
		}
	}

	// remove from filters if final innerBitmaps not contains value
	for key, value := range executor.fields {

		if !bitmaps[0].Contains(uint32(value)) {

			delete(executor.fields, key)
		}
	}

	// release the pool indexes
	for _, poolIndex := range poolIndices[:elementSize] {

		executor.memoryPool.ReleaseStringPool(poolIndex)
	}

	if utils.QueryPlanLogging {

		executor.logQueryPlan(fmt.Sprintf("query planner took %v ms to filter %v keys", time.Now().UnixMilli()-t, len(executor.fields)))
	}

	return nil
}

func (executor *Executor) evaluateStaticVerticalFilter(groups []utils.MotadataMap, entities utils.MotadataMap) error {

	size := 0

	// single instance in or equal filter so no need to probe, direct set as entity.keys
	entityFilter := false

	instances := map[string]struct{}{}

	for _, group := range groups {

		conditions := group.GetMapListValue(Conditions)

		size += len(conditions)

		if len(conditions) > 3 {

			return errors.New(fmt.Sprintf(utils.ErrorFilterKeyQualification, utils.ErrorInvalidCondition))
		}

		for _, condition := range conditions {

			utils.Split(condition.GetStringValue(Operand), utils.KeySeparator, executor.tokenizers[0])

			operator := condition.GetStringValue(Operator)

			operand := executor.tokenizers[0].Tokens[0]

			if len(entities) == 1 && len(groups) == 1 && len(conditions) == 1 && (!strings.Contains(operand, utils.InstanceSeparator) || strings.HasSuffix(operand, "~instance.name")) &&
				(operator == in || operator == equal) {

				entityFilter = true

				if operator == equal {

					instances[condition.GetStringValue(utils.Value)] = struct{}{}

				} else {

					for _, instance := range condition.GetSliceValue(utils.Value) {

						instances[instance.(string)] = struct{}{}
					}
				}
			}

			executor.conditionOperands[operand] = struct{}{}
		}
	}

	if entityFilter {

		for entity := range entities {

			utils.Split(entity, utils.KeySeparator, executor.tokenizers[0])

			for instance := range instances {

				executor.fields[executor.tokenizers[0].Tokens[0]+utils.KeySeparator+instance] = len(executor.fields)
			}
		}

		return nil

	}

	if size > len(executor.decoders) {

		return errors.New(fmt.Sprintf(utils.ErrorFilterKeyQualification, utils.ErrorInvalidFilter))
	}

	return nil
}

func (executor *Executor) filterSearchableVerticalKeys(store, mappingStore *storage.Store, entities utils.MotadataMap, filters []string, qualifiedIndex int, valueElementSize *int) bool {

	buffers, errs, _ := store.GetMultiples(executor.KeyBuffers[:qualifiedIndex], executor.ValueBuffers[:qualifiedIndex], executor.encoder, executor.eventBatches[0], &executor.waitGroups[0], executor.tokenizers[0], false)

	if errs != nil && len(errs) > 0 {

		for _, err := range errs {

			if err != nil {

				executor.logError(err.Error())
			}
		}
	}

	searchableColumn := false

	for i := range buffers {

		if len(buffers[i]) > 0 {

			bitmapPoolIndex, bitmapBytes, err := executor.decoder.DecodeSnappy(buffers[i])

			if err != nil {

				executor.logError(err.Error())

				if bitmapPoolIndex != utils.NotAvailable {

					executor.decoder.MemoryPool.ReleaseBytePool(bitmapPoolIndex)
				}

				continue
			}

			if len(bitmapBytes) > 0 {

				searchTokenBitmap := bitmap.FromBytes(bitmapBytes)

				if searchTokenBitmap != nil {

					ordinalPoolIndex, ordinals := executor.memoryPool.AcquireINT32Pool(searchTokenBitmap.Count())

					ordinalIndex := 0

					searchTokenBitmap.Range(func(ordinal uint32) {

						ordinals[ordinalIndex] = int32(ordinal)

						ordinalIndex++
					})

					executor.cleanupMapping()

					mapperPoolIndex := utils.NotAvailable

					// no need to probe datatype wise as searchable always has string datatype
					err, _, mapperPoolIndex = mappingStore.ResolveMapping(ordinalPoolIndex, executor.encoder, executor.stringOrdinalMappings, executor.numericOrdinalMappings, searchTokenBitmap.Count())

					if err != nil && !strings.Contains(err.Error(), utils.ErrorIteratorDone) {

						if bitmapPoolIndex != utils.NotAvailable {

							executor.decoder.MemoryPool.ReleaseBytePool(bitmapPoolIndex)
						}

						if mapperPoolIndex != utils.NotAvailable {

							executor.decoder.MemoryPool.ReleaseStringPool(mapperPoolIndex)
						}

						if ordinalPoolIndex != utils.NotAvailable {

							executor.decoder.MemoryPool.ReleaseINT32Pool(ordinalPoolIndex)
						}

						continue
					}

					values := executor.encoder.MemoryPool.GetStringPool(mapperPoolIndex)

					for j := range ordinals {

						utils.Split(values[j], utils.GroupSeparator, executor.tokenizers[0])

						if executor.tokenizers[0].Counts > 1 && entities.Contains(executor.tokenizers[0].Tokens[0]) {

							filters[*valueElementSize] = executor.tokenizers[0].Tokens[0] + utils.KeySeparator + executor.tokenizers[0].Tokens[1]

							*valueElementSize++
						}
					}

					executor.decoder.MemoryPool.ReleaseINT32Pool(ordinalPoolIndex)

					executor.decoder.MemoryPool.ReleaseStringPool(mapperPoolIndex)
				}
			}

			searchableColumn = true

			if bitmapPoolIndex != utils.NotAvailable {

				executor.decoder.MemoryPool.ReleaseBytePool(bitmapPoolIndex)
			}
		}
	}

	return searchableColumn
}

func (executor *Executor) filterVerticalKeys(store *storage.Store, condition utils.MotadataMap, included bool, qualifiedIndex int, filters []string, valueElementSize *int) {

	buffers, errs, _ := store.GetMultiples(executor.KeyBuffers[:qualifiedIndex], executor.ValueBuffers[:qualifiedIndex], executor.encoder, executor.eventBatches[0], &executor.waitGroups[0], executor.tokenizers[0], false)

	if errs != nil && len(errs) > 0 {

		for _, err := range errs {

			if err != nil {

				executor.logError(err.Error())
			}
		}
	}

	for i, bytes := range buffers {

		if len(bytes) > 0 {

			if executor.validVerticalFilter(condition, string(bytes), included) {

				utils.Split(string(executor.KeyBuffers[i]), utils.KeySeparator, executor.tokenizers[0])

				if executor.tokenizers[0].Counts > 0 { // qualified

					filters[*valueElementSize] = executor.tokenizers[0].Tokens[0] + utils.KeySeparator + executor.tokenizers[0].Tokens[1]

					*valueElementSize++
				}
			}
		}
	}
}

func (executor *Executor) validVerticalFilter(condition utils.MotadataMap, fromValue string, included bool) bool {

	conditionValue := strings.ToLower(condition.GetStringValue(utils.Value))

	switch condition.GetStringValue(Operator) {

	case equal:

		if included {

			return strings.EqualFold(strings.ToLower(fromValue), strings.ToLower(conditionValue))

		} else {

			return !strings.EqualFold(strings.ToLower(fromValue), strings.ToLower(conditionValue))
		}

	case startWith:

		if included {

			return strings.HasPrefix(strings.ToLower(fromValue), strings.ToLower(conditionValue))

		} else {

			return !strings.HasPrefix(strings.ToLower(fromValue), strings.ToLower(conditionValue))
		}

	case endWith:

		if included {

			return strings.HasSuffix(strings.ToLower(fromValue), strings.ToLower(conditionValue))

		} else {

			return !strings.HasSuffix(strings.ToLower(fromValue), strings.ToLower(conditionValue))
		}

	case contain:

		return (included && strings.Contains(strings.ToLower(fromValue), strings.ToLower(conditionValue))) || (!included && !strings.Contains(strings.ToLower(fromValue), strings.ToLower(conditionValue)))

	case in:

		values := condition.GetSliceValue(utils.Value)

		if reflect.ValueOf(values[0]).Kind() == reflect.String {

			filters := map[string]struct{}{}

			for _, value := range values {

				filters[strings.ToLower(value.(string))] = struct{}{}
			}

			_, contains := filters[strings.ToLower(fromValue)]

			if included {

				return contains
			} else {

				return !contains
			}
		} else {
			filters := map[int]struct{}{}

			for _, value := range values {

				filters[codec.ToINT(value)] = struct{}{}
			}

			_, contains := filters[codec.ToINT(fromValue)]

			if included {

				return contains
			} else {

				return !contains
			}
		}

	default:
		return false
	}
}
