/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/*
	This class is used to write data in vertical format, specifically metric data.
	In a vertical writer, we write performance metrics, status metrics, event policies, and trap flap histories.
	Event policies and trap flap histories are written both horizontally and vertically.
*/

/* Change Logs:
* Date                   Author          		Notes
* 2025-02-10			 <PERSON><PERSON>val <PERSON>ra			Motad<PERSON>-4913  Altered modulo operator with new modulo function
* 2025-03-05			 <PERSON><PERSON><PERSON>-5190  Migrated constants from datastore to utils to match SonarQube Standard
* 2025-03-05             Vedant Dokania         Motadata-5451  Status Flap vertical insertion
* 2025-03-21			 <PERSON>haval <PERSON>ra			Motadata-5452  Changed Prefix For NetRoute Status Metric Columns And Wrote NetRoute Event Vertically
* 2025-04-02			 Dhaval Bera			Motadata-4859  Added NetRoute Status Metric Datastore Type
* 2025-04-05			 Dhaval Bera			Motadata-6076  Added Length Checks Before Batch Insertion And Read String Column
* 2025-05-05			 Swapnil A. Dave		MOTADATA-6078 Provision for put multiples in trap, metric and policy writing
												increased the blob pools number for vertical writer from 2 to 3 due to pool exhaustion
												from already acquired pool for reading, encoding the value and holding the pool for batch writing.
* 2025-05-10             Dhaval Bera            MOTADATA-6174  Added Unmap function to release memory-mapped resources
* 2025-05-15			 Vedant D. Dokania		Motadata-6251 Sonar Error Fixing
*/

package writer

import (
	bytes2 "bytes"
	"encoding/binary"
	"errors"
	"fmt"
	"github.com/kelindar/bitmap"
	"golang.org/x/sys/unix"
	"math"
	. "motadatadatastore/codec"
	"motadatadatastore/datastore"
	. "motadatadatastore/storage"
	"motadatadatastore/utils"
	"os"
	"reflect"
	"runtime"
	"strings"
	"sync"
	"sync/atomic"
	"time"
)

var (
	verticalWriterLogger = utils.NewLogger("Vertical Writer", "writer")

	verticalEvents = utils.EventDir + utils.PathSeparator + "0"

	statusColumns = []string{datastore.UpTime, datastore.DownTime, datastore.UnknownTime, datastore.SuspendTime, datastore.DisableTime, datastore.UnreachableTime, datastore.MaintenanceTime}
)

const (
	InvalidStringColumn = "invalid string column"

	ReceivedKey = "writer %v received key %v for tick %v with value %v "

	ReceivedBatch = "writer %v received a batch"

	StackTraceVerticalWriter = "!!!STACK TRACE for vertical writer %v!!! \n %v"
)

const (
	metricValueKeyIndex = 0

	metricTimeKeyIndex = 1
)

type VerticalWriter struct {
	encoder Encoder

	decoder Decoder

	storeType utils.DatastoreType

	event DiskIOEvent

	blobEvent datastore.BlobEvent

	staticMetricWriters []*StaticMetricWriter

	waitGroup sync.WaitGroup

	tokenizers []*utils.Tokenizer

	events []DiskIOEventBatch

	Events chan utils.MotadataMap

	ShutdownNotifications chan bool

	// key - object-id^store-name {"key": [][]}
	verticalCacheEntries map[string]map[string][][]interface{} //key of first map storeName^object.id

	verticalAggregationStringCacheEntries map[string][][]interface{}

	stringFields, flushedObjects map[string][]string

	stringFieldSize map[string]int

	// keyBuffers -> key buffers used at the time for flushing cache entries
	// readBuffers -> value buffers used at the time for reading from store values are copied here
	//writeBuffers -> are the pointers to actual byte buffers from put, used for writing in batch manner
	keyBuffers, writeBuffers, readBuffers [][]byte

	bufferBytes, valueBufferBytes, int32Bytes []byte

	statusColumns, stringValues []string

	statusFlapColumns []string

	int8Values []int8 //buffers for temp variables

	err error

	plugin string

	tick int64

	writerId, position, overflowPoolLength, length, batchSize int

	Pending int32

	shutdown bool
}

func NewVerticalWriter(writerId int, staticMetricWriters []*StaticMetricWriter) *VerticalWriter {
	//writer requires defaultblobpools, i.e, 2 + 1 because put multiples holds the byte pool for timekey and valuekey and
	//policy and trap plugin have blob columns making the blob pools insufficient for the writer
	//encoders like snappy and zstd require blob pool when they are encoding.
	pool := utils.NewMemoryPool(12, utils.MaxPoolLength, false, utils.DefaultBlobPools+1)

	encoder := NewEncoder(pool)

	decoder := NewDecoder(pool)

	keyBuffers := make([][]byte, 2)

	writeBuffers := make([][]byte, len(keyBuffers))

	readBuffers := make([][]byte, len(keyBuffers))

	for index := range readBuffers {

		bytes, err := unix.Mmap(-1, 0, utils.MaxValueBufferBytes, unix.PROT_READ|unix.PROT_WRITE, unix.MAP_PRIVATE|unix.MAP_ANON)

		if err != nil {

			verticalWriterLogger.Error(fmt.Sprintf("error %v occurred while mapping annonymous buffer for vertical writer %v and index %v", err, writerId, index))

			bytes = make([]byte, utils.MaxValueBufferBytes)
		}

		readBuffers[index] = bytes
	}

	return &VerticalWriter{

		writerId: writerId,

		staticMetricWriters: staticMetricWriters,

		stringFields: map[string][]string{},

		stringFieldSize: map[string]int{},

		Events: make(chan utils.MotadataMap, 1_00_00),

		encoder: encoder,

		decoder: decoder,

		events: make([]DiskIOEventBatch, utils.MaxStoreParts),

		event: DiskIOEvent{},

		blobEvent: datastore.BlobEvent{},

		keyBuffers: keyBuffers,

		readBuffers: readBuffers,

		writeBuffers: writeBuffers,

		verticalCacheEntries: map[string]map[string][][]interface{}{},

		verticalAggregationStringCacheEntries: map[string][][]interface{}{},

		int8Values: make([]int8, 2),

		stringValues: make([]string, 1),

		ShutdownNotifications: make(chan bool, 5),

		waitGroup: sync.WaitGroup{},

		flushedObjects: map[string][]string{},

		tokenizers: make([]*utils.Tokenizer, 2),

		int32Bytes: make([]byte, 4),
	}

}

func (writer *VerticalWriter) Start() {

	for i := range writer.events {

		writer.events[i] = DiskIOEventBatch{}
	}

	for i := range writer.tokenizers {

		writer.tokenizers[i] = &utils.Tokenizer{

			Tokens: make([]string, utils.TokenizerLength),
		}
	}
	bytes, err := unix.Mmap(-1, 0, utils.DataWriterValueBufferBytes, unix.PROT_READ|unix.PROT_WRITE, unix.MAP_PRIVATE|unix.MAP_ANON)

	if err != nil {

		verticalWriterLogger.Error(fmt.Sprintf("error %v occurred while mapping annonymous buffer for vertical writer %v value buffer", err, writer.writerId))

		bytes = make([]byte, utils.DataWriterValueBufferBytes)
	}

	writer.valueBufferBytes = bytes

	go func() {

		utils.WriterEngineShutdownMutex.Add(1)

		for {

			if writer.shutdown || utils.GlobalShutdown {

				break
			}

			writer.processRequest()
		}

		utils.WriterEngineShutdownMutex.Done()
	}()

}

func (writer *VerticalWriter) processRequest() {

	timer := time.NewTicker(time.Second * time.Duration(utils.DatastoreFlushTimerSeconds))

	defer func() {

		if err := recover(); err != nil {

			stackTraceBytes := make([]byte, 1<<20)

			verticalWriterLogger.Error(fmt.Sprintf("error %v occurred in vertical writer %v for plugin %v", err, writer.writerId, writer.plugin))

			verticalWriterLogger.Error(fmt.Sprintf(StackTraceVerticalWriter, writer.writerId, string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))

			timer.Stop()

			verticalWriterLogger.Error(fmt.Sprintf("vertical writer %v restarted", writer.writerId))

		}
	}()

	for {

		select {

		case event := <-writer.Events:

			writer.writeVerticalBatch(event)

		case <-timer.C:

			writer.flushCaches(true)

		case <-writer.ShutdownNotifications:

			verticalWriterLogger.Info(fmt.Sprintf("shutting down vertical writer %v", writer.writerId))

			if len(writer.verticalCacheEntries) > 0 {

				verticalWriterLogger.Info(fmt.Sprintf("vertical writer %v flushing %v entries, reason: shutdown message received", writer.writerId, len(writer.verticalCacheEntries)))

				writer.flushCaches(true)

				verticalWriterLogger.Info(fmt.Sprintf("vertical writer %v caches has been flushed", writer.writerId))
			}

			for index := range writer.readBuffers {

				if err := unix.Munmap(writer.readBuffers[index]); err != nil {

					verticalWriterLogger.Warn(fmt.Sprintf("failed to unmap anonymous mapped buffer,reason: %v", err.Error()))
				}
			}

			err := unix.Munmap(writer.valueBufferBytes)

			if err != nil {

				verticalWriterLogger.Warn(fmt.Sprintf("failed to unmap anonymous mapped value buffer,reason: %v", err.Error()))
			}

			writer.encoder.MemoryPool.Unmap()

			writer.shutdown = true

			return

		}
	}

}

func (writer *VerticalWriter) writeVerticalBatch(event utils.MotadataMap) {

	fileName := event.GetStringValue(utils.File)

	defer func(fileName string) {

		utils.VerticalFormatSyncNotifications <- utils.WriterSyncEvent{

			File: fileName, WriterId: writer.writerId,
		}

		atomic.AddInt32(&writer.Pending, -1)

		writer.decoder.MemoryPool.TestPoolLeak()
	}(fileName)

	if !event.IsNotEmpty() {

		return
	}

	writer.storeType = event[datastore.DatastoreType].(utils.DatastoreType)

	// The file is coming from the datareader, but for test cases we have put this condition where it sends buffer.
	if len(fileName) > 0 {

		writer.err = writer.read(verticalEvents + utils.PathSeparator + fileName)

		if writer.err != nil {

			verticalWriterLogger.Error(fmt.Sprintf("error %v occurred while read batch file %v", writer.err, event.GetStringValue(utils.File)))

			return
		}
	} else if event.Contains(utils.Buffer) {

		writer.bufferBytes = event[utils.Buffer].([]uint8)
	} else {

		return
	}

	switch {

	case writer.storeType == utils.TrapFlapHistory: // trap vertically

		writer.overflowPoolLength = utils.LogOverflowLength

		writer.writeTrap(event)

	case writer.storeType == utils.EventPolicy: // policy vertically for log and flow

		writer.overflowPoolLength = utils.LogOverflowLength

		writer.writePolicy(event)

	case writer.storeType == utils.PerformanceMetric || writer.storeType == utils.NetRouteMetric:

		writer.overflowPoolLength = utils.OverflowLength

		writer.writePerformanceMetric(event)

	case writer.storeType == utils.ObjectStatusFlapMetric:

		writer.overflowPoolLength = utils.OverflowLength

		writer.writeStatusFlapMetric(event)

	case writer.storeType == utils.ObjectStatusMetric || writer.storeType == utils.NetRouteStatusMetric: // performance metric vertically

		writer.overflowPoolLength = utils.OverflowLength

		writer.writeStatusMetric(event)

	default:

		verticalWriterLogger.Error(fmt.Sprintf("invalid batch received at vertical writer %v", writer.writerId))

	}
}

func (writer *VerticalWriter) writePerformanceMetric(batch utils.MotadataMap) {

	if utils.TraceEnabled() {

		verticalWriterLogger.Trace(fmt.Sprintf(ReceivedBatch, writer.writerId))
	}

	/*
		The whole batch is categorized within one tick and one pluginId

		(meaning :-the metric data was polled at this particular tick and for so-and-so plugin for the whole batch)
	*/

	defer writer.cleanUp()

	writer.tick = batch.GetInt64Value(utils.Tick)

	writer.plugin = batch.GetStringValue(utils.Plugin)

	/*
					Batch Packing Logic inside verticalWriter


					[RowsLength
			        Row1
					Row2
			        Row3
			        EOT bytes]
			        [RowsLength
			        Row4
			        Row5
			        Row6
			        EOT bytes]

			What's inside one row ?

			object id --> 4 bytes

		       instanceLength ---> 4 bytes

				position +=4

				instance column ----> instanceLength

				position += instanceLength

			for position < rowsLength {

			dataType --> 1 byte

			position +=1

			metricColumnLength ---> 4 bytes

			position +=4

			metricColumn ---> metricColumnLength

			position += metricColumnLength

			valueColumnLength ---> 4 bytes

			position +=4

			valueColumn ----> valueColumnLength

			position += valueColumnLength



	*/

	for writer.position < len(writer.bufferBytes) && len(writer.bufferBytes[writer.position:]) > 4 {

		length := int(binary.LittleEndian.Uint32(writer.bufferBytes[writer.position : writer.position+4]))

		writer.position += 4

		//Batch is corrupted so don't read the batch
		if length+3 > len(writer.bufferBytes[writer.position:]) || !bytes2.Equal(writer.bufferBytes[writer.position+length:writer.position+length+3], utils.EOTBytes) {

			verticalWriterLogger.Warn(fmt.Sprintf("invalid batch %v for plugin hence skipping records ", writer.plugin))

			return

		}

		writer.processPerformanceBatch(length, writer.position)

		writer.position += 3 // skipping EOT bytes

	}
}

func (writer *VerticalWriter) writeStatusFlapMetric(batch utils.MotadataMap) {

	if utils.TraceEnabled() {

		verticalWriterLogger.Trace(fmt.Sprintf(ReceivedBatch, writer.writerId))
	}

	/*
		The whole batch is categorized within one tick and one pluginId

		(meaning :-the metric data was polled at this particular tick and for so-and-so plugin for the whole batch)
	*/

	defer writer.cleanUp()

	writer.tick = batch.GetInt64Value(utils.Tick)

	writer.plugin = batch.GetStringValue(utils.Plugin) + utils.DotSeparator + utils.Duration

	/*
					Batch Packing Logic inside verticalWriter


					[RowsLength
			        Row1
					Row2
			        Row3
			        EOT bytes]
			        [RowsLength
			        Row4
			        Row5
			        Row6
			        EOT bytes]

			What's inside one row ?

			object id --> 4 bytes

		       instanceLength ---> 4 bytes

				position +=4

				instance column ----> instanceLength

				position += instanceLength

			for position < rowsLength {

			dataType --> 1 byte

			position +=1

			metricColumnLength ---> 4 bytes

			position +=4

			metricColumn ---> metricColumnLength

			position += metricColumnLength

			valueColumnLength ---> 4 bytes

			position +=4

			valueColumn ----> valueColumnLength

			position += valueColumnLength



	*/

	for writer.position < len(writer.bufferBytes) && len(writer.bufferBytes[writer.position:]) > 4 {

		length := int(binary.LittleEndian.Uint32(writer.bufferBytes[writer.position : writer.position+4]))

		writer.position += 4

		//Batch is corrupted so don't read the batch
		if length+3 > len(writer.bufferBytes[writer.position:]) || !bytes2.Equal(writer.bufferBytes[writer.position+length:writer.position+length+3], utils.EOTBytes) {

			verticalWriterLogger.Warn(fmt.Sprintf("invalid batch %v for plugin hence skipping records ", writer.plugin))

			return

		}

		writer.processStatusFlapBatch(length, writer.position)

		writer.position += 3

	}
}

/*
We unpack the record and then store it in cache. We do not write data directly to the store; instead, we sync data to the store later.
*/
func (writer *VerticalWriter) processPerformanceBatch(length, currentPosition int) {

	defer func(length, currentPosition int) {

		if err := recover(); err != nil {

			writer.position = currentPosition + length

			verticalWriterLogger.Error(fmt.Sprintf("error %v occurred while decode store type %v, plugin %v, tick %v", err, writer.storeType, writer.plugin, writer.tick))

			stackTraceBytes := make([]byte, 1<<20)

			verticalWriterLogger.Error(fmt.Sprintf(StackTraceVerticalWriter, writer.writerId, string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))

		}
	}(length, currentPosition)

	metric, objectId, instance, value, key := utils.Empty, utils.Empty, utils.Empty, utils.Empty, utils.Empty

	position := writer.position

	if writer.storeType == utils.NetRouteMetric {

		objectId = UINT64ToStringValue(binary.LittleEndian.Uint64(writer.bufferBytes[writer.position : writer.position+8]))

		writer.position += 8

	} else {

		objectId = UINT32ToStringValue(binary.LittleEndian.Uint32(writer.bufferBytes[writer.position : writer.position+4]))

		writer.position += 4

	}

	var err error

	instance, err = writer.readStringColumn()

	if err != nil {

		verticalWriterLogger.Error(fmt.Sprintf("error %v occurred while reading instance column for objectId %v , plugin %v", err, objectId, writer.plugin))

		return
	}

	for writer.position < position+length {

		if utils.GlobalShutdown {

			break
		}

		dataType := writer.bufferBytes[writer.position : writer.position+1][0]

		writer.position += 1

		metric, err = writer.readStringColumn()

		if err != nil {

			verticalWriterLogger.Error(fmt.Sprintf("error %v occurred while reading metric column for objectId %v, plugin %v", err, objectId, writer.plugin))

			return
		}

		//To read value we need to now the length of the whole metric-row
		//after that we can subtract 13 bytes from that to get the value from rest bytes

		if len(metric) == 0 { // from java side we are getting empty column so need this check

			continue
		}

		if writer.plugin == datastore.NetRouteEventPlugin {

			writer.processNetRouteEvent(objectId, metric) //as of now only 1 column is inserted

			continue

		}

		writer.flushedObjects[writer.plugin] = append(writer.flushedObjects[writer.plugin], objectId)

		key = datastore.GetMetricKey(objectId, metric, instance)

		var numericValue interface{}

		switch dataType {

		case datastore.StringColumn:

			/*
				in the case of string store, we need not have date in the store name
				as the string stores are for the lifetime and are considered as garbage metrics
			*/

			value, err = writer.readStringColumn()

			if err != nil {

				verticalWriterLogger.Error(fmt.Sprintf("error %v occurred while reading value column for objectId %v, plugin %v", err, objectId, writer.plugin))

				return
			}

			/*
				string metrics are directly written into the store instead of first being written into cache
				Also, the string values are not encoded they are just written as slice of bytes
				and if the key has the same value as the previous one, we don't write it again
			*/

			if utils.TraceEnabled() {

				verticalWriterLogger.Trace(fmt.Sprintf(ReceivedKey, writer.writerId, key, writer.tick, value))
			}

			writer.err = writer.flushStringMetric(key, value, metric, objectId)

			if writer.err != nil {

				verticalWriterLogger.Error(fmt.Sprintf(utils.ErrorWritingStringMetric, writer.err, datastore.GetMetricKey(objectId, metric, instance), writer.writerId))
			}

		case datastore.IntegerColumn:
			//if it is a numeric value, it is cast into int64
			numericValue = int64(binary.LittleEndian.Uint64(writer.bufferBytes[writer.position : writer.position+8]))

			writer.position += 8

			fallthrough

		case datastore.FloatingColumn:

			if dataType == datastore.FloatingColumn {

				numericValue = math.Float64frombits(binary.LittleEndian.Uint64(writer.bufferBytes[writer.position : writer.position+8]))

				writer.position += 8
			}

			// Store Name: date-format-plugin

			store := datastore.GetStore(datastore.GetStoreById(writer.tick, writer.plugin, datastore.VerticalStore), utils.PerformanceMetric, true, true, writer.encoder, writer.tokenizers[1])

			if store == nil {

				verticalWriterLogger.Error(fmt.Sprintf(utils.ErrorAcquireStore, datastore.GetStoreById(writer.tick, writer.plugin, datastore.VerticalStore)) + fmt.Sprintf(MessageWriterId, writer.writerId))

				continue
			}

			/*
										as the code is same for writing both int metric and floating metric
				used the fallthrough for writing either datatype
				and cast the value corresponding the datatype
			*/

			/*
				Metrics are first assessed if they are garbage, then they are written into string store rather than numeric store
			*/
			if !datastore.IsGarbageColumn(metric) {

				if utils.TraceEnabled() {

					verticalWriterLogger.Trace(fmt.Sprintf(ReceivedKey, writer.writerId, key, writer.tick, numericValue))
				}

				key += utils.GroupSeparator + writer.plugin + utils.GroupSeparator + INTToStringValue(int(writer.storeType))

				writer.err = writer.writeMetricCacheEntries(key, metric, store.GetName(), numericValue, objectId, Invalid, false)

				if writer.err != nil {

					verticalWriterLogger.Error(fmt.Sprintf("error %v occurred while writing numeric metric %v in writer %v", writer.err, datastore.GetMetricKey(objectId, metric, instance), writer.writerId))

				}

			} else {

				value = ToString(numericValue)

				if utils.TraceEnabled() {

					verticalWriterLogger.Trace(fmt.Sprintf(ReceivedKey, writer.writerId, key, writer.tick, value))
				}

				writer.err = writer.flushStringMetric(key, value, metric, objectId)

				if writer.err != nil {

					verticalWriterLogger.Error(fmt.Sprintf(utils.ErrorWritingStringMetric, writer.err, datastore.GetMetricKey(objectId, metric, instance), writer.writerId))

				}
			}

		}

	}

}

func (writer *VerticalWriter) processStatusFlapBatch(length, currentPosition int) {

	defer func(length, currentPosition int) {

		if err := recover(); err != nil {

			writer.position = currentPosition + length

			verticalWriterLogger.Error(fmt.Sprintf("error %v occurred while decode store type %v, plugin %v, tick %v", err, writer.storeType, writer.plugin, writer.tick))

			stackTraceBytes := make([]byte, 1<<20)

			verticalWriterLogger.Error(fmt.Sprintf(StackTraceVerticalWriter, writer.writerId, string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))
		}

	}(length, currentPosition)

	metric, objectId, instance, key := utils.Empty, utils.Empty, utils.Empty, utils.Empty

	position := writer.position

	objectId = UINT32ToStringValue(binary.LittleEndian.Uint32(writer.bufferBytes[writer.position : writer.position+4]))

	writer.position += 4

	var err error

	instance, err = writer.readStringColumn()

	if err != nil {

		verticalWriterLogger.Error(fmt.Sprintf("error %v occurred while reading instance column for objectId %v , for plugin %v", err, objectId, writer.plugin))

		return
	}

	for writer.position < position+length {

		if utils.GlobalShutdown {

			break
		}

		dataType := writer.bufferBytes[writer.position : writer.position+1][0]

		writer.position += 1

		metric, err = writer.readStringColumn()

		if err != nil {

			verticalWriterLogger.Error(fmt.Sprintf("error %v occurred while reading metric column for objectId %v , for plugin %v", err, objectId, writer.plugin))

			return
		}

		//To read value we need to now the length of the whole metric-row
		//after that we can subtract 13 bytes from that to get the value from rest bytes

		if len(metric) == 0 { // from java side we are getting empty column so need this check

			continue
		}

		writer.flushedObjects[writer.plugin] = append(writer.flushedObjects[writer.plugin], objectId)

		key = datastore.GetMetricKey(objectId, metric, instance)

		var numericValue int64

		switch dataType {

		case datastore.StringColumn:

			value, err := writer.readStringColumn()

			if err != nil {

				verticalWriterLogger.Error(fmt.Sprintf("error %v occurred while reading value column for objectId %v , for plugin %v", err, objectId, writer.plugin))

				return
			}

			if strings.Contains(metric, datastore.ObjectStatusFlapHistory) {

				numericValue = int64(datastore.StatusFlapOrdinals[strings.ToUpper(value)])

			} else {

				/*
					string metrics are directly written into the store instead of first being written into cache
					Also, the string values are not encoded they are just written as slice of bytes
					and if the key has the same value as the previous one, we don't write it again
				*/

				if utils.TraceEnabled() {

					verticalWriterLogger.Trace(fmt.Sprintf(ReceivedKey, writer.writerId, key, writer.tick, value))
				}

				writer.err = writer.flushStringMetric(key, value, metric, objectId)

				if writer.err != nil {

					verticalWriterLogger.Error(fmt.Sprintf(utils.ErrorWritingStringMetric, writer.err, datastore.GetMetricKey(objectId, metric, instance), writer.writerId))
				}

				continue
			}

		case datastore.IntegerColumn:
			//if it is a numeric value, it is cast into int64
			numericValue = int64(binary.LittleEndian.Uint64(writer.bufferBytes[writer.position : writer.position+8]))

			writer.position += 8

		}

		store := datastore.GetStore(datastore.GetStoreById(utils.GetStartingWeek(time.Unix(writer.tick, 0)).Unix(), writer.plugin, datastore.VerticalStore), writer.storeType, true, true, writer.encoder, writer.tokenizers[1])

		if store == nil {

			continue
		}

		key += utils.GroupSeparator + writer.plugin + utils.GroupSeparator + INTToStringValue(int(writer.storeType))

		writer.err = writer.writeMetricCacheEntries(key, metric, store.GetName(), numericValue, objectId, Invalid, false)

		if writer.err != nil {

			verticalWriterLogger.Error(fmt.Sprintf("error %v occurred while writing numeric metric %v in writer %v", writer.err, datastore.GetMetricKey(objectId, metric, instance), writer.writerId))

		}

	}

}

func (writer *VerticalWriter) writeStatusMetric(batch utils.MotadataMap) {

	if utils.TraceEnabled() {

		verticalWriterLogger.Trace(fmt.Sprintf(ReceivedBatch, writer.writerId))
	}

	/*
		The whole batch is categorized within one tick and one pluginId

		(meaning :-the metric data was polled at this particular tick and for so-and-so plugin for the whole batch)
	*/

	defer writer.cleanUp()

	writer.tick = batch.GetInt64Value(utils.Tick)

	writer.plugin = batch.GetStringValue(utils.Plugin)

	/*

		buffer contains two things ordinal-map(ordinal->data) and data-block(with ordinals in place of actual data)
	*/

	for writer.position < len(writer.bufferBytes) && len(writer.bufferBytes[writer.position:]) > 4 {

		length := int(binary.LittleEndian.Uint32(writer.bufferBytes[writer.position : writer.position+4]))

		writer.position += 4

		//Batch is corrupted so don't read the batch
		if length+3 > len(writer.bufferBytes[writer.position:]) || !bytes2.Equal(writer.bufferBytes[writer.position+length:writer.position+length+3], utils.EOTBytes) {

			return

		}

		writer.processStatusMetricBatch()

		writer.position += 3 // skipping EOT bytes

	}

}

/*
						in the case of monitor-level status, the plugin id will be 0
	and the status column will be monitored.uptime, monitor.downtime, etc

						in-case of instance-level status, the plugin id will be the specific instance plugin
	like windows-process, windows-file plugin, etc.
	and the status column will be instanceType~uptime, instanceType~downTime, etc
*/

func (writer *VerticalWriter) processStatusMetricBatch() {

	prefix := datastore.Monitor

	if writer.storeType == utils.NetRouteStatusMetric {

		prefix = datastore.NetRoute

	}

	instanceType, objectId, instance := utils.Empty, utils.Empty, utils.Empty

	var err error

	objectId, err = writer.readStringColumn()

	if err != nil {

		verticalWriterLogger.Error(fmt.Sprintf("error %v occurred while reading objectId column, for plugin %v", err, writer.plugin))

		return

	}

	instance, err = writer.readStringColumn()

	if err != nil {

		verticalWriterLogger.Error(fmt.Sprintf("error %v occurred while reading instance column, for plugin %v", err, writer.plugin))

		return

	}

	_, err = writer.readStringColumn() //skipping status bytes

	if err != nil {

		verticalWriterLogger.Error(fmt.Sprintf("error %v occurred while reading status column, for plugin %v", err, writer.plugin))

		return
	}

	_, err = writer.readStringColumn() //skipping reason column

	if err != nil {

		verticalWriterLogger.Error(fmt.Sprintf("error %v occurred while reading reason column, for plugin %v", err, writer.plugin))

		return
	}

	statusColumn, err := writer.readStringColumn()

	if err != nil {

		verticalWriterLogger.Error(fmt.Sprintf("error %v occurred while reading status column, for plugin %v", err, writer.plugin))

		return
	}

	if strings.Contains(statusColumn, utils.InstanceSeparator) {

		utils.Split(statusColumn, utils.InstanceSeparator, writer.tokenizers[0])

		instanceType = writer.tokenizers[0].Tokens[0]

	}

	duration := int32(binary.LittleEndian.Uint32(writer.bufferBytes[writer.position : writer.position+4]))

	writer.position += 4

	writer.flushedObjects[writer.plugin] = append(writer.flushedObjects[writer.plugin], objectId)

	store := datastore.GetStore(datastore.GetStoreById(writer.tick, writer.plugin, datastore.VerticalStore), writer.storeType, true, true, writer.encoder, writer.tokenizers[1])

	if store == nil {

		verticalWriterLogger.Error(fmt.Sprintf(utils.ErrorAcquireStore, datastore.GetStoreById(writer.tick, writer.plugin, datastore.VerticalStore)) + fmt.Sprintf(MessageWriterId, writer.writerId))

		return
	}

	/*
							consider the below-given scenario to understand the code below it
		scenario :-

							writer-1 is assigned interface~uptime for instance Gi0 and monitor 1

							writer-2 is assigned interface~downtime for instance Gi2 and monitor 1

							use-case:-there will be status columns that will be dumped for uptime
							(.*)uptime.sec & (.*)uptime.percent, similarly for other statuses

							example :-

							1. writer-1 will have the following key-value for instance
		1^Gi0^interface~uptime.sec -> 10
								1^Gi0^interface~uptime.percent -> 100

								& the other following keys will have value 0
		1^Gi0^interface~downtime/suspend/maintenance/disable/unknown/unreachable.percent

							2. writer-2 will have the following keys
		1^Gi2^interface~downtime.sec -> 10
								1^Gi2^interface~downtime.percent -> 100

								& the other following keys will have value 0
		1^Gi2^interface~uptime/suspend/maintenance/disable/unknown/unreachable.percent

	*/

	for _, column := range statusColumns { //iterating over the 7 status columns

		if strings.HasSuffix(statusColumn, column) {

			//if the assigned status is encounter the key will be
			// .sec & .percent with value 10 and 100

			key := datastore.GetMetricKey(objectId, statusColumn+".seconds", instance)

			if utils.TraceEnabled() {

				verticalWriterLogger.Trace(fmt.Sprintf(ReceivedKey, writer.writerId, key, writer.tick, duration))
			}

			writer.err = writer.writeMetricCacheEntries(key, statusColumn+".seconds", store.GetName(), int64(duration), objectId, GetDataTypeINT(int(duration)), true)

			key = datastore.GetMetricKey(objectId, statusColumn+datastore.SuffixPercent, instance)

			if utils.TraceEnabled() {

				verticalWriterLogger.Trace(fmt.Sprintf(ReceivedKey, writer.writerId, key, writer.tick, 100))
			}

			writer.err = writer.writeMetricCacheEntries(key, statusColumn+datastore.SuffixPercent, store.GetName(), float64(100), objectId, Float8, true)

			if writer.err != nil {

				verticalWriterLogger.Error(fmt.Sprintf("error %v occurred while writing metric %v in writer %v", writer.err, datastore.GetMetricKey(objectId, datastore.Status, instance), writer.writerId))
			}

		} else {

			//else the key will be .percent for the status columns other than the assigned

			metric := ""

			if instanceType != utils.Empty {

				metric = instanceType + utils.InstanceSeparator + column

			} else {

				metric = prefix + utils.DotSeparator + column
			}

			key := datastore.GetMetricKey(objectId, metric+datastore.SuffixPercent, instance)

			if utils.TraceEnabled() {

				verticalWriterLogger.Trace(fmt.Sprintf(ReceivedKey, writer.writerId, key, writer.tick, 0))
			}

			//the value is hardcoded to 0 percent for the rest status columns to align the array

			writer.err = writer.writeMetricCacheEntries(key, metric+datastore.SuffixPercent, store.GetName(), float64(0), objectId, Float8, true)

			if writer.err != nil {

				verticalWriterLogger.Error(fmt.Sprintf("error %v occurred while writing metric %v in writer %v", writer.err, datastore.GetMetricKey(objectId, datastore.Status, instance), writer.writerId))
			}

		}

	}

}

func (writer *VerticalWriter) flushStringMetric(key, value, metric, objectId string) error {

	if writer.staticMetricWriters != nil {

		writer.staticMetricWriters[utils.GetFastModN(utils.GetHash64([]byte(writer.plugin)), utils.StaticMetricWriters)].Events <- utils.MotadataMap{

			utils.Plugin: writer.plugin,

			"key": key,

			Value: value,
		}

	}

	utils.Split(key, utils.KeySeparator, writer.tokenizers[0])

	//means an instance key, and we need to do mapping for keys like 1^x^system.process
	if writer.tokenizers[0].Counts == 3 && !strings.Contains(writer.tokenizers[0].Tokens[2], utils.InstanceSeparator) {

		mappingStore := datastore.GetStore(datastore.Object+utils.HyphenSeparator+datastore.Mappings, utils.Mapping, true, true, writer.encoder, writer.tokenizers[1])

		if mappingStore == nil {

			return errors.New(fmt.Sprintf(utils.ErrorAcquireStore, datastore.Object+utils.HyphenSeparator+datastore.Mappings))
		}

		if !mappingStore.ExistStringMapping(objectId + utils.GroupSeparator + value) {

			//put mappings only puts when key is not there in the store, so no need to verify the same here
			err := mappingStore.PutStringMapping(objectId+utils.GroupSeparator+value, writer.encoder)

			if err != nil {

				return errors.New(fmt.Sprintf(utils.ErrorWriteKey, []byte(objectId+utils.GroupSeparator+value), mappingStore.GetName(), err.Error()+fmt.Sprintf(MessageWriterId, writer.writerId)))
			}
		}
	}

	if len(value)+utils.MaxValueBytes > utils.MaxValueBufferBytes {

		value = value[:utils.MaxValueBufferBytes-utils.MaxValueBytes]

	}

	if datastore.IsAggregationMetric(metric) {

		key := key + utils.GroupSeparator + writer.plugin + utils.GroupSeparator + INTToStringValue(int(writer.storeType))

		entries, _ := writer.verticalAggregationStringCacheEntries[key]

		position := utils.NotAvailable

		if entries == nil {

			entries = make([][]interface{}, 2)

			entries[0] = make([]interface{}, 0)

			entries[1] = make([]interface{}, 0)

			entries[1] = append(entries[1], String)
		}

		entries[0], position = datastore.AppendTimeTick(utils.UnixToSeconds(writer.tick), entries[0])

		entries[1] = datastore.AppendValue(position, value, String, String, entries[1])

		writer.verticalAggregationStringCacheEntries[key] = entries

	}

	if datastore.IsSearchableColumn(metric) {

		utils.Split(key, utils.KeySeparator, writer.tokenizers[0])

		if mappingStore := datastore.GetStore(datastore.Object+utils.HyphenSeparator+datastore.Mappings, utils.Mapping, true, true, writer.encoder, writer.tokenizers[1]); mappingStore != nil {

			found, count, err := mappingStore.GetStringMapping(writer.tokenizers[0].Tokens[0] + utils.GroupSeparator + writer.tokenizers[0].Tokens[1])

			if err == nil && found {

				/*
					hardcoded condition in store file while opening store if the store name is changed, need to change there too.
				*/
				indexStore := datastore.GetStore(writer.tokenizers[0].Tokens[writer.tokenizers[0].Counts-1]+utils.HyphenSeparator+datastore.VerticalStore+utils.HyphenSeparator+INTToStringValue(int(String)), utils.Index, true, true, writer.encoder, writer.tokenizers[1])

				if indexStore == nil {

					return errors.New(fmt.Sprintf(utils.ErrorAcquireStore, writer.tokenizers[0].Tokens[writer.tokenizers[0].Counts-1]+utils.HyphenSeparator+datastore.VerticalStore+utils.HyphenSeparator+INTToStringValue(int(String))))
				}

				utils.Split(value, utils.SpaceSeparator, writer.tokenizers[1])

				for _, indexToken := range writer.tokenizers[1].Tokens[:writer.tokenizers[1].Counts] {

					if err := writer.flushStringMetricIndexCacheEntries(indexStore, indexToken, count); err != nil {

						verticalWriterLogger.Error(fmt.Sprintf(utils.ErrorUpdatePostingListIndex, indexToken, writer.tokenizers[0].Tokens[writer.tokenizers[0].Counts-1], err) + fmt.Sprintf(MessageWriterId, writer.writerId))
					}
				}
			}
		}
	}

	return nil
}

func (writer *VerticalWriter) flushStringMetricIndexCacheEntries(store *Store, searchToken string, count int32) error {

	/*
		string metric posting list store name
		metric-176

		note :- 176 is the numeric ordinal for string datatype
	*/

	keyBytes := []byte(strings.ToLower(searchToken))

	if store != nil && len(keyBytes) > 0 {

		_, valueBytes, err := store.Get(keyBytes, writer.readBuffers[0], writer.encoder, writer.event, &writer.waitGroup, writer.tokenizers[1], false)

		if err != nil {

			store.Delete(keyBytes, writer.encoder, writer.tokenizers[1])

			verticalWriterLogger.Error(fmt.Sprintf(utils.ErrorGetKey, string(keyBytes), store.GetName(), err.Error()) + fmt.Sprintf(MessageWriterId, writer.writerId))

			valueBytes = nil
		}

		indexBitmap := bitmap.Bitmap{}

		bytePoolIndex := utils.NotAvailable

		var bufferBytes []byte

		poolIndex := -1

		if valueBytes != nil && len(valueBytes) > 0 { //if the store contains the value for the current token

			poolIndex, bufferBytes, err = writer.decoder.DecodeSnappy(valueBytes)

			if err != nil {

				verticalWriterLogger.Error(fmt.Sprintf(utils.ErrorDecodeValues, string(keyBytes), store.GetName(), err.Error()) + fmt.Sprintf(MessageWriterId, writer.writerId))

				return err
			}

			defer writer.decoder.MemoryPool.ReleaseBytePool(poolIndex)

			if len(bufferBytes) > 0 {

				indexBitmap = bitmap.FromBytes(bufferBytes)

				indexBitmap.Set(uint32(count))

				bytePoolIndex, bufferBytes = writer.encoder.EncodeSnappy(indexBitmap.ToBytes(), utils.MaxValueBytes)

			}

		} else { //if the store does not have this key

			indexBitmap.Set(uint32(count))

			bytePoolIndex, bufferBytes = writer.encoder.EncodeSnappy(indexBitmap.ToBytes(), utils.MaxValueBytes)

		}

		if bytePoolIndex == utils.NotAvailable {

			return errors.New(fmt.Sprintf(utils.ErrorWriteKey, string(keyBytes), store.GetName(), "failed to encode buffer bytes"))
		}

		err = store.Put(keyBytes, bufferBytes, writer.encoder, writer.tokenizers[1])

		writer.encoder.MemoryPool.ReleaseBytePool(bytePoolIndex)

		if err != nil {

			return errors.New(fmt.Sprintf(utils.ErrorWriteKey, string(keyBytes), store.GetName(), err.Error()))
		}

	}

	return nil
}

// trap writer

func (writer *VerticalWriter) writeTrap(event utils.MotadataMap) {

	defer writer.cleanUp()

	writer.tick = event.GetInt64Value(utils.Tick)

	writer.plugin = event.GetStringValue(utils.Plugin) + ".flap"

	store := datastore.GetStore(datastore.GetStoreById(writer.tick, writer.plugin, datastore.VerticalStore), utils.TrapFlapHistory, true, true, writer.encoder, writer.tokenizers[1])

	if store == nil {

		verticalWriterLogger.Error(fmt.Sprintf(utils.ErrorAcquireStore, datastore.GetStoreById(writer.tick, writer.plugin, datastore.VerticalStore)) + fmt.Sprintf(MessageWriterId, writer.writerId))

		return
	}

	clear(writer.stringFields)

	clear(writer.stringFieldSize)

	for writer.position < len(writer.bufferBytes) && len(writer.bufferBytes[writer.position:]) > 4 {

		rowLength := int(binary.LittleEndian.Uint32(writer.bufferBytes[writer.position : writer.position+4]))

		writer.position += 4

		//Batch is corrupted so don't read the batch
		if rowLength+3 > len(writer.bufferBytes[writer.position:]) || !bytes2.Equal(writer.bufferBytes[writer.position+rowLength:writer.position+rowLength+3], utils.EOTBytes) {

			verticalWriterLogger.Warn(fmt.Sprintf("invalid batch for plugin %v hence skipping records ", writer.plugin))

			return

		}

		position := writer.position

		writer.length = int(binary.LittleEndian.Uint16(writer.bufferBytes[writer.position : writer.position+2]))

		writer.position += 2

		eventSource := string(writer.bufferBytes[writer.position : writer.position+writer.length])

		writer.position += writer.length

		for writer.position < position+rowLength {

			dataType := writer.bufferBytes[writer.position : writer.position+1][0]

			writer.position += 1

			field, err := writer.readStringColumn()

			if err != nil {

				verticalWriterLogger.Error(fmt.Sprintf("error %v occurred while reading field column, for plugin %v", err, writer.plugin))

				return

			}

			value := utils.Empty

			if dataType == datastore.StringColumn {

				value, err = writer.readStringColumn()

				if err != nil {

					verticalWriterLogger.Error(fmt.Sprintf("error %v occurred while reading value column, for plugin %v", err, writer.plugin))

					return

				}

			} else {

				value = INT64ToStringValue(int64(binary.LittleEndian.Uint64(writer.bufferBytes[writer.position : writer.position+8])))

				writer.position += 8
			}

			writer.stringFields[field] = append(writer.stringFields[field], value)

			writer.stringFieldSize[field] += len(value)

		}

		writer.stringFields[utils.EventSource] = append(writer.stringFields[utils.EventSource], eventSource)

		writer.position += 3 //skipping EOT bytes

	}

	writer.batchSize = len(writer.stringFields[utils.EventSource])

	tickPoolIndex, ticks := writer.encoder.MemoryPool.AcquireINT32Pool(len(writer.stringFields[utils.EventSource]))

	writer.encoder.MemoryPool.ResetINT32Pool(tickPoolIndex, utils.NotAvailable, utils.UnixToSeconds(writer.tick))

	defer writer.encoder.MemoryPool.ReleaseINT32Pool(tickPoolIndex)

	keys := make(map[string][]string)

	for field, values := range writer.stringFields {

		if field == utils.EventSource || field == utils.TrapOID {

			continue
		}

		for index, value := range values {

			key := writer.stringFields[utils.EventSource][index] + utils.KeySeparator + writer.stringFields[utils.TrapOID][index] + utils.KeySeparator + field

			keys[key] = append(keys[key], value)
		}
	}

	part := uint16(0)

	for key, values := range keys {

		part = 0

		if store.ContainsMultipartKey() {

			part = store.GetMaxPart(utils.GetHash64([]byte(key)))
		}

		if err := writer.flushValues(ticks[:len(values)], values, key, part, store, false); err != nil {

			verticalWriterLogger.Error(fmt.Sprintf("err %v occurred while writing trap vertically for key %v", err.Error(), key))
		}
	}
}

// Policy vertical for log/flow
func (writer *VerticalWriter) writePolicy(batch utils.MotadataMap) {

	defer writer.cleanUp()

	writer.tick = batch.GetInt64Value(utils.Tick)

	writer.plugin = batch.GetStringValue(utils.Plugin)

	store := datastore.GetStore(datastore.GetStoreById(writer.tick, writer.plugin, datastore.VerticalStore), utils.EventPolicy, true, true, writer.encoder, writer.tokenizers[1])

	if store == nil {

		verticalWriterLogger.Error(fmt.Sprintf(utils.ErrorAcquireStore, datastore.GetStoreById(writer.tick, writer.plugin, datastore.VerticalStore)))

		return
	}

	clear(writer.stringFields)

	clear(writer.stringFieldSize)

	for writer.position < len(writer.bufferBytes) && len(writer.bufferBytes[writer.position:]) > 4 {

		rowLength := int(binary.LittleEndian.Uint32(writer.bufferBytes[writer.position : writer.position+4]))

		writer.position += 4

		//Batch is corrupted so don't read the batch
		if rowLength+3 > len(writer.bufferBytes[writer.position:]) || !bytes2.Equal(writer.bufferBytes[writer.position+rowLength:writer.position+rowLength+3], utils.EOTBytes) {

			verticalWriterLogger.Warn(fmt.Sprintf("invalid batch for plugin %v hence skipping records ", writer.plugin))

			return

		}

		position := writer.position

		writer.length = int(binary.LittleEndian.Uint16(writer.bufferBytes[writer.position : writer.position+2]))

		writer.position += 2

		eventSource := string(writer.bufferBytes[writer.position : writer.position+writer.length])

		writer.position += writer.length

		for writer.position < position+rowLength {

			dataType := writer.bufferBytes[writer.position : writer.position+1][0]

			writer.position += 1

			field, err := writer.readStringColumn()

			if err != nil {

				verticalWriterLogger.Error(fmt.Sprintf("error %v occurred while reading field column, for plugin %v", err, writer.plugin))

				return

			}

			value := utils.Empty

			if dataType == datastore.StringColumn {

				value, err = writer.readStringColumn()

				if err != nil {

					verticalWriterLogger.Error(fmt.Sprintf("error %v occurred while reading value column, for plugin %v", err, writer.plugin))

					return

				}

			} else {

				value = INT64ToStringValue(int64(binary.LittleEndian.Uint64(writer.bufferBytes[writer.position : writer.position+8])))

				writer.position += 8
			}

			writer.stringFields[field] = append(writer.stringFields[field], value)

			writer.stringFieldSize[field] += len(value)

		}

		writer.stringFields[utils.EventSource] = append(writer.stringFields[utils.EventSource], eventSource)

		writer.position += 3 //skipping EOT bytes
	}

	writer.batchSize = len(writer.stringFields[utils.EventSource])

	tickPoolIndex, ticks := writer.encoder.MemoryPool.AcquireINT32Pool(len(writer.stringFields[utils.PolicySeverity]))

	writer.encoder.MemoryPool.ResetINT32Pool(tickPoolIndex, len(writer.stringFields[utils.PolicySeverity]), utils.UnixToSeconds(writer.tick))

	defer writer.encoder.MemoryPool.ReleaseINT32Pool(tickPoolIndex)

	keys := make(map[string][]string)

	for field, values := range writer.stringFields {

		if field == utils.PolicyId {

			continue
		}

		for index, value := range values {

			key := writer.stringFields[utils.PolicyId][index] + utils.KeySeparator + field

			keys[key] = append(keys[key], value)
		}
	}

	for key, values := range keys {

		part := uint16(0)

		if store.ContainsMultipartKey() {

			part = store.GetMaxPart(utils.GetHash64([]byte(key)))
		}

		if err := writer.flushValues(ticks[:len(values)], values, key, part, store, false); err != nil {

			verticalWriterLogger.Error(fmt.Sprintf("err %v occurred while writing trap vertically for key %v", err.Error(), key))

			continue
		}
	}
}

//common functions

/*
We keep the data in cache for the time being and then flush it, or if the data exceeds the maximum cache value, we flush it.
As for strings, we write them directly into store.
*/
func (writer *VerticalWriter) writeMetricCacheEntries(key, metric, storeName string, value interface{}, objectId string, dataType DataType, status bool) error {

	currentDataType := Invalid

	position := -1

	/*
		as the cache is written in interface slice, we need to explicitly cast and check for it's type before it's used

		For, FLOAT it is float64 and for INT it is int64
	*/

	/*

		in-memory cache structure

		length of values = length of ticks +1 (first index stores datatype)
		[]ticks -> []interface{t1, t2, t3, t4....,tn} (where t will be int32)

		[]values -> []interface{DataType, v1, v2, v3...., vn} (where v will be int64)

	*/
	//means status metric so no need to calculate the dataType
	if status {

		currentDataType = dataType

		key += utils.GroupSeparator + writer.plugin + utils.GroupSeparator + INTToStringValue(int(writer.storeType))

	} else {

		if valueType := reflect.ValueOf(value).Kind(); valueType == reflect.Float64 {

			//if the metric is actually registered to be floating, then only it is stored as float
			if datastore.IsFloatingColumn(metric) {
				if value, ok := value.(float64); ok {

					currentDataType = GetDataTypeFloat(value) //Get the dataType (FLOAT8|FLOAT16|FLOAT64)

				}

			} else { // the metric value will be rounded off and stored as an integer, to avoid using unnecessary bytes

				value = int64(Round(value.(float64)))

				//Get the dataType (INT8|INT16|INT24|INT32|INT40|INT48|INT56|INT64)
				currentDataType = GetDataTypeINT(int(value.(int64)))
			}

		} else if valueType == reflect.Int64 || valueType == reflect.Uint64 {

			if value, ok := value.(int64); ok {

				//Get the dataType (INT8|INT16|INT24|INT32|INT40|INT48|INT56|INT64)
				currentDataType = GetDataTypeINT(int(value))

			}
		}

	}

	if currentDataType == Invalid {

		return errors.New(fmt.Sprintf(utils.ErrorInvalidDataType, value, key, storeName))
	}

	cacheKey := objectId + utils.KeySeparator + storeName

	entries, _ := writer.verticalCacheEntries[cacheKey]

	var cacheValues []interface{}

	var cacheEntries [][]interface{}

	if entries == nil {

		cacheEntries = make([][]interface{}, 2)

		cacheEntries[0] = make([]interface{}, 0)

		cacheEntries[1] = make([]interface{}, 0)

	} else {

		if _, ok := entries[key]; !ok {

			cacheEntries = make([][]interface{}, 2)

			cacheEntries[0] = make([]interface{}, 0)

			cacheEntries[1] = make([]interface{}, 0)

		} else {

			cacheEntries = entries[key]
		}
	}

	cacheValues = cacheEntries[1]

	cacheEntries[0], position = datastore.AppendTimeTick(utils.UnixToSeconds(writer.tick), cacheEntries[0])

	if len(cacheValues) == 0 {

		if currentDataType >= Float8 {

			cacheValues = append(append(cacheValues, currentDataType), ToFixed(value.(float64)))
		} else {

			cacheValues = append(append(cacheValues, currentDataType), value.(int64))

		}

	} else {

		previousDataType := cacheValues[0].(DataType)

		/*
			if current is integer and previous was floating datatype
			if previous was integer and current is floating datatype
		*/

		if previousDataType > currentDataType {

			if previousDataType >= Float8 && currentDataType < Float8 {

				value = float64(value.(int64))
			}

			if previousDataType == Float8 {

				if currentDataType == Int16 {

					cacheValues[0] = Float16

					currentDataType = Float16

				} else if currentDataType > Int16 {

					cacheValues[0] = Float64

					currentDataType = Float64
				}

			} else if previousDataType == Float16 {

				if currentDataType > Int16 {

					cacheValues[0] = Float64

					currentDataType = Float64

				}
			}

		}

		if currentDataType >= Float8 {

			cacheValues = datastore.AppendValue(position, ToFixed(value.(float64)), previousDataType, currentDataType, cacheValues)

		} else {

			cacheValues = datastore.AppendValue(position, value, previousDataType, currentDataType, cacheValues)
		}
	}

	cacheEntries[1] = cacheValues

	if _, ok := writer.verticalCacheEntries[cacheKey]; !ok {

		writer.verticalCacheEntries[cacheKey] = map[string][][]interface{}{}
	}

	writer.verticalCacheEntries[cacheKey][key] = cacheEntries

	// if the cache entries for a particular key exceed the limit (more than 32 poll)
	// or the keys in the cache exceed the limit (more than 500 monitor data),
	// then the cache will be flushed
	if len(cacheValues)-1 >= utils.MaxWriterCacheValues || len(writer.verticalCacheEntries) > utils.MaxVerticalWriteCacheEntries {

		writer.flushCaches(false)
	}

	return nil
}

// flush metric cache and status cache
func (writer *VerticalWriter) flushCaches(testPoolLeak bool) {

	if len(writer.verticalCacheEntries) > 0 {

		if utils.TraceEnabled() {

			verticalWriterLogger.Trace(fmt.Sprintf("writer %v flushing %v cache entries", writer.writerId, len(writer.verticalCacheEntries)))
		}

		defer func() {

			if testPoolLeak {

				writer.decoder.MemoryPool.TestPoolLeak()
			}

			// Here, we publish notification to MOTADATA the list of objects (monitors) that are flushed as per plugin.
			if !utils.GlobalShutdown && utils.PublisherNotifications != nil && writer.writerId < utils.VerticalWriters {

				for plugin, objects := range writer.flushedObjects {

					utils.PublisherNotifications <- utils.MotadataMap{

						utils.OperationType: utils.Flush,

						utils.EventContext: utils.MotadataMap{
							datastore.Object: objects,
							utils.Plugin:     plugin,
						},
					}
				}
			}

			clear(writer.flushedObjects)

		}()

		for key, entries := range writer.verticalCacheEntries {

			delete(writer.verticalCacheEntries, key)

			if err := writer.flushMetricCacheEntries(key, entries); err != nil {

				verticalWriterLogger.Error(fmt.Sprintf("error occurred while flushing transaction for object^store:- %v for writerId %v", key, writer.writerId))
			}
		}

		if utils.TraceEnabled() {

			verticalWriterLogger.Trace(fmt.Sprintf("writer %v flushed %v cache entries", writer.writerId, len(writer.verticalCacheEntries)))
		}
	}

	for key, entries := range writer.verticalAggregationStringCacheEntries {

		writer.notifyAggregator(key, entries)

		delete(writer.verticalAggregationStringCacheEntries, key)

	}

	return
}

// it is used in flushCaches for entry wise flushing data
func (writer *VerticalWriter) flushMetricCacheEntries(cacheKey string, cacheEntries map[string][][]interface{}) error {

	utils.Split(cacheKey, utils.KeySeparator, writer.tokenizers[0]) // cacheKey -> [objectId , storeName]

	store := datastore.GetStore(writer.tokenizers[0].Tokens[1], utils.None, false, true, writer.encoder, writer.tokenizers[1])

	if store == nil {

		return errors.New(fmt.Sprintf(utils.ErrorAcquireStore, writer.tokenizers[0].Tokens[1]))
	}

	defer func() {

		if r := recover(); r != nil {

			stackTraceBytes := make([]byte, 1<<20)

			verticalWriterLogger.Error(fmt.Sprintf("error %v occurred while flushing cache entries for store -%v, object -%v", r, writer.tokenizers[0].Tokens[1], writer.tokenizers[0].Tokens[0]))

			verticalWriterLogger.Error(fmt.Sprintf(StackTraceVerticalWriter, writer.writerId, string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))

		}
	}()

	var err error

	for key, entries := range cacheEntries {

		key = writer.notifyAggregator(key, entries)

		err = writer.flush(key, store, entries)

		if err != nil {

			verticalWriterLogger.Error(fmt.Sprintf("error %v occurred in writer %v, while flushing cache entries", err, writer.writerId))
		}

	}

	return nil
}

func (writer *VerticalWriter) cleanUp() {

	writer.position = 0

	writer.tick = utils.NotAvailable

	writer.bufferBytes = nil
}

// flushStringValues is used for flush the trap and policy data
func (writer *VerticalWriter) flushValues(ticks []int32, values []string, key string, part uint16, store *Store, overflowed bool) error {

	utils.Split(key, utils.KeySeparator, writer.tokenizers[0])

	column := writer.tokenizers[0].Tokens[writer.tokenizers[0].Counts-1]

	writer.keyBuffers[metricValueKeyIndex] = []byte(key + utils.KeySeparator + UINT16ToStringValue(part))

	writer.keyBuffers[metricTimeKeyIndex] = []byte(key + utils.KeySeparator + datastore.Time + utils.KeySeparator + UINT16ToStringValue(part))

	buffers, errs, err := store.GetMultiples(writer.keyBuffers[:2], writer.readBuffers[:2], writer.encoder, writer.events, &writer.waitGroup, writer.tokenizers[1], true)

	if err != nil {

		return err
	}

	if errs[metricValueKeyIndex] != nil && strings.EqualFold(errs[metricValueKeyIndex].Error(), utils.ErrorTooLarge) {

		blobPoolIndex, blobBytes := writer.encoder.MemoryPool.AcquireBlobPool(utils.NotAvailable)

		defer writer.encoder.MemoryPool.ReleaseBytePool(blobPoolIndex)

		_, buffers[metricValueKeyIndex], errs[metricValueKeyIndex] = store.Get(writer.keyBuffers[0], blobBytes, writer.encoder, writer.event, &writer.waitGroup, writer.tokenizers[1], true)
	}

	if datastore.IsBlobColumn(column) {

		encodedTickPoolIndex := utils.NotAvailable

		if errs[metricTimeKeyIndex] == nil {

			tickPoolIndex := 0

			var decodedTicks []int32

			tickPoolIndex, decodedTicks, err = datastore.GetTimeTicks(buffers[metricTimeKeyIndex], 0, writer.decoder)

			if err != nil {

				return err
			}

			// not found error check is for first insertion scenario, where tick is inserted but value is not inserted
			if errs[metricValueKeyIndex] != nil && !strings.Contains(errs[metricValueKeyIndex].Error(), "not found") {

				return errs[metricValueKeyIndex]
			}

			records, index := 0, 0

			for i := 1; i < len(buffers[metricValueKeyIndex]); i += 13 {

				index = 0

				count := ReadUINT16Value(buffers[metricValueKeyIndex][i : i+13][:2], &index)

				records += count
			}

			if records < len(decodedTicks) {

				verticalWriterLogger.Warn(fmt.Sprintf(" key : %s is corrupted, blob length is %d , tick length is %d", string(writer.keyBuffers[0]), records, len(decodedTicks)))

				decodedTicks = decodedTicks[:records]
			}

			if len(decodedTicks)+len(ticks) > writer.overflowPoolLength {

				writer.decoder.MemoryPool.ReleaseINT32Pool(tickPoolIndex)

				part++

				return writer.flushValues(ticks, values, key, part, store, true)
			}

			defer writer.decoder.MemoryPool.ReleaseINT32Pool(tickPoolIndex)

			tempPoolIndex, tempTicks := writer.encoder.MemoryPool.AcquireINT32Pool(len(decodedTicks) + len(ticks))

			copy(tempTicks, decodedTicks)

			copy(tempTicks[len(decodedTicks):], ticks)

			encodedTickPoolIndex, writer.writeBuffers[metricTimeKeyIndex] = writer.encodeTicks(tempTicks)

			defer writer.encoder.MemoryPool.ReleaseBytePool(encodedTickPoolIndex)

			writer.encoder.MemoryPool.ReleaseINT32Pool(tempPoolIndex)

		} else {

			encodedTickPoolIndex, writer.writeBuffers[metricTimeKeyIndex] = writer.encodeTicks(ticks)

			defer writer.encoder.MemoryPool.ReleaseBytePool(encodedTickPoolIndex)

		}

		bufferIndex := 0

		writer.blobEvent.KeyBytes = writer.keyBuffers[metricValueKeyIndex]

		writer.blobEvent.ValueBytes = writer.readBuffers[metricValueKeyIndex]

		writer.blobEvent.Values = values

		writer.blobEvent.Encoder = writer.encoder

		writer.blobEvent.Store = store

		writer.blobEvent.Padding = utils.MaxValueBytes

		writer.blobEvent.Tokenizer = writer.tokenizers[1]

		writer.blobEvent.DiskIOEvent = writer.event

		writer.blobEvent.WaitGroup = &writer.waitGroup

		writer.blobEvent.Encoding = datastore.GetBlobEncoding(writer.stringFieldSize[column], writer.batchSize)

		bufferIndex, writer.writeBuffers[metricValueKeyIndex], err = datastore.WriteBlobColumnValues(writer.blobEvent)

		defer writer.encoder.MemoryPool.ReleaseBytePool(bufferIndex)

		err = store.PutMultiples(writer.keyBuffers[:2], writer.writeBuffers[:2], writer.encoder, writer.tokenizers[1])

		if err != nil {

			return err
		}

	} else {

		tempPoolIndex := utils.NotAvailable

		var tempTicks []int32

		var tempValues []string

		encodedTickPoolIndex := utils.NotAvailable

		if errs[metricTimeKeyIndex] == nil && errs[metricValueKeyIndex] == nil {

			tickPoolIndex := 0

			var decodedTicks []int32

			tickPoolIndex, decodedTicks, err = datastore.GetTimeTicks(buffers[metricTimeKeyIndex], 0, writer.decoder)

			if err != nil {

				return err
			}

			defer writer.decoder.MemoryPool.ReleaseINT32Pool(tickPoolIndex)

			valuePoolIndex := 0

			var decodedValues []string

			valuePoolIndex, decodedValues, err = writer.decoder.DecodeStringValues(GetEncoding(buffers[metricValueKeyIndex][0]), buffers[metricValueKeyIndex][1:], string(writer.keyBuffers[metricValueKeyIndex]), store.GetName(), 0)

			if err != nil {

				return err
			}

			defer writer.decoder.MemoryPool.ReleaseStringPool(valuePoolIndex)

			length := len(decodedValues)

			// for corruption handling
			if len(decodedTicks) != len(decodedValues) {

				if len(decodedValues) < len(decodedTicks) {

					length = len(decodedValues)
				} else {

					length = len(decodedTicks)
				}
			}

			decodedValues = decodedValues[:length]

			decodedTicks = decodedTicks[:length]

			if len(decodedValues)+len(values) > writer.overflowPoolLength {

				part++

				return writer.flushValues(ticks, values, key, part, store, true)
			}

			tempPoolIndex, tempTicks = writer.encoder.MemoryPool.AcquireINT32Pool(len(decodedTicks) + len(ticks))

			copy(tempTicks, decodedTicks)

			copy(tempTicks[len(decodedTicks):], ticks)

			encodedTickPoolIndex, writer.writeBuffers[metricTimeKeyIndex] = writer.encodeTicks(tempTicks)

			defer writer.encoder.MemoryPool.ReleaseBytePool(encodedTickPoolIndex)

			writer.encoder.MemoryPool.ReleaseINT32Pool(tempPoolIndex)

			tempPoolIndex, tempValues = writer.encoder.MemoryPool.AcquireStringPool(len(decodedValues) + len(values))

			copy(tempValues, decodedValues)

			copy(tempValues[len(decodedValues):], values)

			bufferIndex := 0

			bufferIndex, writer.writeBuffers[metricValueKeyIndex], err = writer.encoder.EncodeStringValues(tempValues, GetEncoding(buffers[metricValueKeyIndex][0]), utils.MaxValueBytes, string(writer.keyBuffers[metricValueKeyIndex]))

			writer.decoder.MemoryPool.ReleaseStringPool(tempPoolIndex)

			if err != nil {

				return err
			}

			err = store.PutMultiples(writer.keyBuffers[:2], writer.writeBuffers[:2], writer.encoder, writer.tokenizers[1])

			writer.encoder.MemoryPool.ReleaseBytePool(bufferIndex)

			if err != nil {

				return err
			}

		} else {

			encodedTickPoolIndex, writer.writeBuffers[metricTimeKeyIndex] = writer.encodeTicks(ticks)

			defer writer.encoder.MemoryPool.ReleaseBytePool(encodedTickPoolIndex)

			bufferIndex := 0

			bufferIndex, writer.writeBuffers[metricValueKeyIndex], err = writer.encoder.EncodeStringValues(values, None, utils.MaxValueBytes, string(writer.keyBuffers[metricValueKeyIndex]))

			if err != nil {

				return err
			}

			err = store.PutMultiples(writer.keyBuffers[:2], writer.writeBuffers[:2], writer.encoder, writer.tokenizers[1])

			writer.encoder.MemoryPool.ReleaseBytePool(bufferIndex)

			if err != nil {

				return err
			}

		}

	}

	if overflowed {

		err = store.AddMultipartKey(utils.GetHash64([]byte(key)), 1)

		if err != nil {

			verticalWriterLogger.Error(fmt.Sprintf("error add multipart key %v for writerId %v", key, writer.writerId))

			return err
		}

		err = store.AddMultipartKey(utils.GetHash64([]byte(key+utils.KeySeparator+datastore.Time)), 1)

		if err != nil {

			verticalWriterLogger.Error(fmt.Sprintf("error add multipart key %v for writerId %v", key+utils.KeySeparator+datastore.Time, writer.writerId))

			return err
		}
	}

	return nil

}

func (writer *VerticalWriter) encodeTicks(ticks []int32) (int, []byte) {

	bufferIndex, bufferBytes := writer.encoder.WriteUINT32Value(uint32(len(ticks)), 0)

	var valueBytes []byte

	poolIndex := utils.NotAvailable

	if len(ticks) <= RLEDeltaEncodingMaxValues {

		poolIndex, valueBytes = writer.encoder.EncodeRLEDeltaINT32Values(ticks, ticks[0], len(bufferBytes)+utils.MaxValueBytes)

	} else {

		poolIndex, valueBytes = writer.encoder.EncodeBP32INTValues(ticks, len(bufferBytes)+utils.MaxValueBytes)
	}

	copy(valueBytes[utils.MaxValueBytes:], bufferBytes)

	writer.encoder.MemoryPool.ReleaseBytePool(bufferIndex)

	return poolIndex, valueBytes

}

// flush is used in flushMetricCacheEntries to flush the data as key-value pairs.
func (writer *VerticalWriter) flush(key string, store *Store, value interface{}) error {

	part := store.GetMaxPart(utils.GetHash64([]byte(key)))

	writer.keyBuffers[metricValueKeyIndex] = []byte(key + utils.KeySeparator + UINT16ToStringValue(part))

	writer.keyBuffers[metricTimeKeyIndex] = []byte(datastore.GetTimeTickKey(key) + utils.KeySeparator + UINT16ToStringValue(part))

	ticks := value.([][]interface{})[0]

	values := value.([][]interface{})[1]

	buffers, errs, err := store.GetMultiples(writer.keyBuffers[:2], writer.readBuffers[:2], writer.encoder, writer.events, &writer.waitGroup, writer.tokenizers[1], true)

	if err != nil {

		verticalWriterLogger.Error(fmt.Sprintf("error %v occurred while flushing key %v ", err, string(writer.keyBuffers[metricValueKeyIndex])))

		return err
	}

	currentDataType := values[0].(DataType)

	previousDataType := currentDataType

	values = values[1:]

	var timeTicks []int32

	var int8Values []int8

	var int16Values []int16

	var int32Values []int32

	var int64Values []int64

	var float64Values []float64

	var stringValues []string

	poolIndex := -1

	tickPoolIndex := -1

	encoding := None

	var bytes []byte

	length := 0

	if errs[metricValueKeyIndex] == nil && errs[metricTimeKeyIndex] == nil {

		header := buffers[metricValueKeyIndex][0]

		encoding = GetEncoding(header)

		poolIndex, currentDataType, int8Values, int16Values, int32Values, int64Values, float64Values, stringValues, err = datastore.GetColumns(currentDataType, header, buffers[metricValueKeyIndex][1:], key, store.GetName(), 0, writer.decoder)

		if err != nil || poolIndex == utils.NotAvailable {

			return errors.New(fmt.Sprintf(utils.ErrorDecodeValues, string(writer.keyBuffers[metricValueKeyIndex]), store.GetName(), err))
		}

		if errs[metricTimeKeyIndex] == nil && len(buffers[metricTimeKeyIndex]) > 0 {

			if tickPoolIndex, timeTicks, err = datastore.GetTimeTicks(buffers[metricTimeKeyIndex], 0, writer.decoder); err != nil {

				return errors.New(fmt.Sprintf(utils.ErrorGetKey, string(writer.keyBuffers[metricTimeKeyIndex]), store.GetName(), err))
			}

		}

		switch {

		case currentDataType == Int8:

			length = len(int8Values)

		case currentDataType == Int16:

			length = len(int16Values)

		case currentDataType == Int24 || currentDataType == Int32:

			length = len(int32Values)

		case currentDataType == Int40 || currentDataType == Int48 || currentDataType == Int56 || currentDataType == Int64:

			length = len(int64Values)

		case currentDataType == Float8 || currentDataType == Float16 || currentDataType == Float64:

			length = len(float64Values)

		case currentDataType == String:

			length = len(stringValues)
		}

	} else {

		// key not found in store

		tickPoolIndex, timeTicks = writer.decoder.MemoryPool.AcquireINT32Pool(len(ticks))

		switch {

		case currentDataType == Int8:

			poolIndex, int8Values = writer.decoder.MemoryPool.AcquireINT8Pool(len(values))

		case currentDataType == Int16:

			poolIndex, int16Values = writer.decoder.MemoryPool.AcquireINT16Pool(len(values))

		case currentDataType == Int24 || currentDataType == Int32:

			poolIndex, int32Values = writer.decoder.MemoryPool.AcquireINT32Pool(len(values))

		case currentDataType == Int40 || currentDataType == Int48 || currentDataType == Int56 || currentDataType == Int64:

			poolIndex, int64Values = writer.decoder.MemoryPool.AcquireINT64Pool(len(values))

		case currentDataType == Float8 || currentDataType == Float16 || currentDataType == Float64:

			poolIndex, float64Values = writer.decoder.MemoryPool.AcquireFLOAT64Pool(len(values))

		case currentDataType == String:

			poolIndex, stringValues = writer.decoder.MemoryPool.AcquireStringPool(len(values))
		}

	}

	/*
		Overflow is checked, and if the length of new data + existing data or new data length exceeds the pool length,
		we write data in the new part.
	*/
	overflowed := false

	if errs[metricValueKeyIndex] == nil && errs[metricTimeKeyIndex] == nil {

		if len(timeTicks)+len(ticks) > writer.encoder.MemoryPool.GetPoolLength() {

			overflowed = true

			part++

			writer.keyBuffers[metricValueKeyIndex] = []byte(key + utils.KeySeparator + UINT16ToStringValue(part))

			writer.keyBuffers[metricTimeKeyIndex] = []byte(datastore.GetTimeTickKey(key) + utils.KeySeparator + UINT16ToStringValue(part))

			writer.decoder.MemoryPool.ReleaseINT32Pool(tickPoolIndex)

			tickPoolIndex, timeTicks = writer.decoder.MemoryPool.AcquireINT32Pool(len(values))

			switch {

			case currentDataType == Int8:

				writer.decoder.MemoryPool.ReleaseINT8Pool(poolIndex)

				poolIndex, int8Values = writer.decoder.MemoryPool.AcquireINT8Pool(len(values))

			case currentDataType == Int16:

				writer.decoder.MemoryPool.ReleaseINT16Pool(poolIndex)

				poolIndex, int16Values = writer.decoder.MemoryPool.AcquireINT16Pool(len(values))

			case currentDataType == Int24 || currentDataType == Int32:

				writer.decoder.MemoryPool.ReleaseINT32Pool(poolIndex)

				poolIndex, int32Values = writer.decoder.MemoryPool.AcquireINT32Pool(len(values))

			case currentDataType == Int40 || currentDataType == Int48 || currentDataType == Int56 || currentDataType == Int64:

				writer.decoder.MemoryPool.ReleaseINT64Pool(poolIndex)

				poolIndex, int64Values = writer.decoder.MemoryPool.AcquireINT64Pool(len(values))

			case currentDataType == Float8 || currentDataType == Float16 || currentDataType == Float64:

				writer.decoder.MemoryPool.ReleaseFLOAT64Pool(poolIndex)

				poolIndex, float64Values = writer.decoder.MemoryPool.AcquireFLOAT64Pool(len(values))

			case currentDataType == String:

				writer.decoder.MemoryPool.ReleaseStringPool(poolIndex)

				poolIndex, stringValues = writer.decoder.MemoryPool.AcquireStringPool(len(values))
			}

			//in case of overflow length is 0
			length = 0

		} else {

			if len(timeTicks) != length { //means corrupted entry... so,we have to repair it...

				switch {

				case currentDataType == Int8:

					if len(int8Values) > len(timeTicks) {

						length = len(timeTicks)

						verticalWriterLogger.Warn(fmt.Sprintf(utils.ErrorCorruptedRecord, string(writer.keyBuffers[metricValueKeyIndex]), store.GetName(), len(int8Values)-length))

					} else {

						length = len(int8Values)

						verticalWriterLogger.Warn(fmt.Sprintf(utils.ErrorCorruptedRecord, string(writer.keyBuffers[metricValueKeyIndex]), store.GetName(), len(timeTicks)-length))

					}

					int8Values = int8Values[:length]

				case currentDataType == Int16:

					if len(int16Values) > len(timeTicks) {

						length = len(timeTicks)

						verticalWriterLogger.Warn(fmt.Sprintf(utils.ErrorCorruptedRecord, string(writer.keyBuffers[metricValueKeyIndex]), store.GetName(), len(int16Values)-length))

					} else {

						length = len(int16Values)

						verticalWriterLogger.Warn(fmt.Sprintf(utils.ErrorCorruptedRecord, string(writer.keyBuffers[metricValueKeyIndex]), store.GetName(), len(timeTicks)-length))

					}

					int16Values = int16Values[:length]

				case currentDataType == Int24 || currentDataType == Int32:

					if len(int32Values) > len(timeTicks) {

						length = len(timeTicks)

						verticalWriterLogger.Warn(fmt.Sprintf(utils.ErrorCorruptedRecord, string(writer.keyBuffers[metricValueKeyIndex]), store.GetName(), len(int32Values)-length))

					} else {

						length = len(int32Values)

						verticalWriterLogger.Warn(fmt.Sprintf(utils.ErrorCorruptedRecord, string(writer.keyBuffers[metricValueKeyIndex]), store.GetName(), len(timeTicks)-length))

					}

					int32Values = int32Values[:length]

				case currentDataType == Int40 || currentDataType == Int48 || currentDataType == Int56 || currentDataType == Int64:

					if len(int64Values) > len(timeTicks) {

						length = len(timeTicks)

						verticalWriterLogger.Warn(fmt.Sprintf(utils.ErrorCorruptedRecord, string(writer.keyBuffers[metricValueKeyIndex]), store.GetName(), len(int64Values)-length))

					} else {

						length = len(int64Values)

						verticalWriterLogger.Warn(fmt.Sprintf(utils.ErrorCorruptedRecord, string(writer.keyBuffers[metricValueKeyIndex]), store.GetName(), len(timeTicks)-length))

					}

					int64Values = int64Values[:length]

				case currentDataType == Float8 || currentDataType == Float16 || currentDataType == Float64:

					if len(float64Values) > len(timeTicks) {

						length = len(timeTicks)

						verticalWriterLogger.Warn(fmt.Sprintf(utils.ErrorCorruptedRecord, string(writer.keyBuffers[metricValueKeyIndex]), store.GetName(), len(float64Values)-length))

					} else {

						length = len(float64Values)

						verticalWriterLogger.Warn(fmt.Sprintf(utils.ErrorCorruptedRecord, string(writer.keyBuffers[metricValueKeyIndex]), store.GetName(), len(timeTicks)-length))

					}

					float64Values = float64Values[:length]

				}

				timeTicks = timeTicks[:length]

			}

			switch {

			case currentDataType == Int8:

				tempPoolIndex, tempValues := writer.decoder.MemoryPool.AcquireINT8Pool(len(int8Values) + len(values))

				copy(tempValues, int8Values)

				writer.decoder.MemoryPool.ReleaseINT8Pool(poolIndex)

				int8Values = tempValues

				poolIndex = tempPoolIndex

			case currentDataType == Int16:

				tempPoolIndex, tempValues := writer.decoder.MemoryPool.AcquireINT16Pool(len(int16Values) + len(values))

				copy(tempValues, int16Values)

				writer.decoder.MemoryPool.ReleaseINT16Pool(poolIndex)

				int16Values = tempValues

				poolIndex = tempPoolIndex

			case currentDataType == Int24 || currentDataType == Int32:

				tempPoolIndex, tempValues := writer.decoder.MemoryPool.AcquireINT32Pool(len(int32Values) + len(values))

				copy(tempValues, int32Values)

				writer.decoder.MemoryPool.ReleaseINT32Pool(poolIndex)

				int32Values = tempValues

				poolIndex = tempPoolIndex

			case currentDataType == Int40 || currentDataType == Int48 || currentDataType == Int56 || currentDataType == Int64:

				tempPoolIndex, tempValues := writer.decoder.MemoryPool.AcquireINT64Pool(len(int64Values) + len(values))

				copy(tempValues, int64Values)

				writer.decoder.MemoryPool.ReleaseINT64Pool(poolIndex)

				int64Values = tempValues

				poolIndex = tempPoolIndex

			case currentDataType == Float8 || currentDataType == Float16 || currentDataType == Float64:

				tempPoolIndex, tempValues := writer.decoder.MemoryPool.AcquireFLOAT64Pool(len(float64Values) + len(values))

				copy(tempValues, float64Values)

				writer.decoder.MemoryPool.ReleaseFLOAT64Pool(poolIndex)

				float64Values = tempValues

				poolIndex = tempPoolIndex

			case currentDataType == String:

				tempPoolIndex, tempValues := writer.decoder.MemoryPool.AcquireStringPool(len(stringValues) + len(values))

				copy(tempValues, stringValues)

				writer.decoder.MemoryPool.ReleaseStringPool(poolIndex)

				stringValues = tempValues

				poolIndex = tempPoolIndex
			}

			tempPoolIndex, tempValues := writer.decoder.MemoryPool.AcquireINT32Pool(len(timeTicks) + len(values))

			copy(tempValues, timeTicks)

			writer.decoder.MemoryPool.ReleaseINT32Pool(tickPoolIndex)

			timeTicks = tempValues

			tickPoolIndex = tempPoolIndex

		}
	}

	for tick := range ticks {

		position := -1

		timeTicks[length+tick] = ticks[tick].(int32)

		// We sorted data based on ticks. If the tick is older, we need to sort.
		if !utils.IsSortedINT32Values(timeTicks[:length+tick+1]) {

			utils.SortINT32Values(timeTicks[:length+tick+1])

			position, _ = utils.SearchINT32Value(timeTicks[:length+tick+1], ticks[tick].(int32))

		}

		switch {

		case currentDataType == Int8:

			int8Values[length+tick] = int8(values[tick].(int64))

			if position != -1 {

				index, swappedValues := writer.decoder.MemoryPool.AcquireINT8Pool(len(int8Values))

				int8Values = utils.SwapLastINT8Value(int8Values[:length+tick+1], swappedValues, position)

				writer.decoder.MemoryPool.ReleaseINT8Pool(poolIndex)

				poolIndex = index

			}

		case currentDataType == Int16:

			int16Values[length+tick] = int16(values[tick].(int64))

			if position != -1 {

				index, swappedValues := writer.decoder.MemoryPool.AcquireINT16Pool(len(int16Values))

				int16Values = utils.SwapLastINT16Value(int16Values[:length+tick+1], swappedValues, position)

				writer.decoder.MemoryPool.ReleaseINT16Pool(poolIndex)

				poolIndex = index
			}

		case currentDataType == Int24 || currentDataType == Int32:

			int32Values[length+tick] = int32(values[tick].(int64))

			if position != -1 {

				valuePoolIndex, swappedValues := writer.decoder.MemoryPool.AcquireINT32Pool(len(int32Values))

				int32Values = utils.SwapLastINT32Value(int32Values[:length+tick+1], swappedValues, position)

				writer.decoder.MemoryPool.ReleaseINT32Pool(poolIndex)

				poolIndex = valuePoolIndex
			}

		case currentDataType == Int40 || currentDataType == Int48 || currentDataType == Int56 || currentDataType == Int64:

			int64Values[length+tick] = values[tick].(int64)

			if position != -1 {

				index, swappedValues := writer.decoder.MemoryPool.AcquireINT64Pool(len(int64Values))

				int64Values = utils.SwapLastINT64Value(int64Values[:length+tick+1], swappedValues, position)

				writer.decoder.MemoryPool.ReleaseINT64Pool(poolIndex)

				poolIndex = index
			}

		case currentDataType == Float8 || currentDataType == Float16 || currentDataType == Float64:

			if previousDataType < Float8 {

				float64Values[length+tick] = float64(values[tick].(int64))

			} else {

				float64Values[length+tick] = values[tick].(float64)

			}

			if position != -1 {

				index, swappedValues := writer.decoder.MemoryPool.AcquireFLOAT64Pool(len(float64Values))

				float64Values = utils.SwapLastFLOAT64Value(float64Values[:length+tick+1], swappedValues, position)

				writer.decoder.MemoryPool.ReleaseFLOAT64Pool(poolIndex)

				poolIndex = index
			}

		case currentDataType == String:

			stringValues[length+tick] = values[tick].(string)

			if position != -1 {

				index, swappedValues := writer.decoder.MemoryPool.AcquireStringPool(len(stringValues))

				stringValues = utils.SwapLastStringValue(stringValues[:length+tick+1], swappedValues, position)

				writer.decoder.MemoryPool.ReleaseStringPool(poolIndex)

				poolIndex = index
			}

		}
	}

	length = len(timeTicks)

	bufferIndex, bufferBytes := writer.encoder.WriteUINT32Value(uint32(length), 0)

	defer writer.encoder.MemoryPool.ReleaseBytePool(bufferIndex)

	if length <= RLEDeltaEncodingMaxValues {

		bufferIndex, bytes = writer.encoder.EncodeRLEDeltaINT32Values(timeTicks, timeTicks[0], len(bufferBytes)+utils.MaxValueBytes)

	} else {

		bufferIndex, bytes = writer.encoder.EncodeBP32INTValues(timeTicks, len(bufferBytes)+utils.MaxValueBytes)
	}

	copy(bytes[utils.MaxValueBytes:], bufferBytes)

	writer.decoder.MemoryPool.ReleaseINT32Pool(tickPoolIndex)

	writer.writeBuffers[metricTimeKeyIndex] = bytes

	defer writer.encoder.MemoryPool.ReleaseBytePool(bufferIndex)

	bufferIndex = utils.NotAvailable

	switch {

	case currentDataType == Int8:

		bufferIndex, bytes, err = writer.encoder.EncodeINT8Values(encoding, int8Values, utils.MaxValueBytes)

		writer.decoder.MemoryPool.ReleaseINT8Pool(poolIndex)

	case currentDataType == Int16:

		bufferIndex, bytes, err = writer.encoder.EncodeINT16Values(encoding, int16Values, utils.MaxValueBytes)

		writer.decoder.MemoryPool.ReleaseINT16Pool(poolIndex)

	case currentDataType == Int24 || currentDataType == Int32:

		bufferIndex, bytes, err = writer.encoder.EncodeINT32Values(int32Values, encoding, currentDataType, GetDataTypeBits(currentDataType), utils.MaxValueBytes)

		writer.decoder.MemoryPool.ReleaseINT32Pool(poolIndex)

	case currentDataType == Int40 || currentDataType == Int48 || currentDataType == Int56 || currentDataType == Int64:

		bufferIndex, bytes, err = writer.encoder.EncodeINT64Values(int64Values, encoding, currentDataType, utils.MaxValueBytes)

		writer.decoder.MemoryPool.ReleaseINT64Pool(poolIndex)

	case currentDataType == Float8 || currentDataType == Float16 || currentDataType == Float64:

		bufferIndex, bytes, err = writer.encoder.EncodeFLOAT64Values(float64Values, encoding, currentDataType, utils.MaxValueBytes)

		writer.decoder.MemoryPool.ReleaseFLOAT64Pool(poolIndex)

	case currentDataType == String:

		bufferIndex, bytes, err = writer.encoder.EncodeStringValues(stringValues, encoding, utils.MaxValueBytes, string(writer.keyBuffers[metricValueKeyIndex]))

		writer.decoder.MemoryPool.ReleaseStringPool(poolIndex)
	}

	if bufferIndex == utils.NotAvailable {

		// 24779 -DB Crash | System provide continuous loader in motadata aiops server and Provide error like Shutdown message received from Memory Pool, message: shutting down datastore, reason: BYTE pool leaked...
		// possibility of header corruption case
		return errors.New(fmt.Sprintf(utils.ErrorInvalidDataType, "", string(writer.keyBuffers[metricValueKeyIndex]), store.GetName()))
	}

	if err != nil {

		return errors.New(fmt.Sprintf(utils.ErrorEncodeValues, string(writer.keyBuffers[metricValueKeyIndex]), store.GetName(), err))
	}

	writer.writeBuffers[metricValueKeyIndex] = bytes

	err = store.PutMultiples(writer.keyBuffers[:2], writer.writeBuffers[:2], writer.encoder, writer.tokenizers[1])

	writer.encoder.MemoryPool.ReleaseBytePool(bufferIndex)

	if err != nil {

		return errors.New(fmt.Sprintf(utils.ErrorWriteKey, string(writer.keyBuffers[0]), store.GetName(), err))
	}

	if overflowed {

		err = store.AddMultipartKey(utils.GetHash64([]byte(key)), 1)

		if err != nil {

			verticalWriterLogger.Error(fmt.Sprintf("failed to add multipart key for key %v, reason %v", key, err.Error()))
		}
	}

	return nil

}

func (writer *VerticalWriter) getTokens(key string) (string, string, string, string) {

	utils.Split(key, utils.GroupSeparator, writer.tokenizers[0])

	plugin := writer.tokenizers[0].Tokens[1]

	datastoreType := writer.tokenizers[0].Tokens[2]

	utils.Split(writer.tokenizers[0].Tokens[0], utils.KeySeparator, writer.tokenizers[1])

	if writer.tokenizers[1].Counts == 3 {

		return writer.tokenizers[1].Tokens[2], plugin, datastoreType, writer.tokenizers[0].Tokens[0]
	}

	return writer.tokenizers[1].Tokens[1], plugin, datastoreType, writer.tokenizers[0].Tokens[0]

}

// Aggregation metric entries are sent to the metric aggregator.
func (writer *VerticalWriter) notifyAggregator(key string, entries [][]interface{}) string {

	metric, plugin, storeType, key := writer.getTokens(key)

	if datastore.IsAggregationMetric(metric) {

		utils.MetricAggregationRequests[utils.GetFastModN(utils.GetHash64([]byte(metric)), utils.MetricAggregators)] <- utils.MetricAggregationRequest{

			StoreType: storeType,

			Column: metric,

			Key: key,

			Plugin: plugin,

			Entries: entries,
		}
	}

	return key
}

func (writer *VerticalWriter) readStringColumn() (result string, err error) {

	if writer.position < len(writer.bufferBytes) && len(writer.bufferBytes[writer.position:]) >= 4 {

		writer.length = int(binary.LittleEndian.Uint32(writer.bufferBytes[writer.position : writer.position+4]))

		writer.position += 4

		if writer.position+writer.length <= len(writer.bufferBytes) {

			result = string(writer.bufferBytes[writer.position : writer.position+writer.length])

			writer.position += writer.length

			return result, nil

		}

	}

	return "", errors.New(InvalidStringColumn)
}

func (writer *VerticalWriter) read(fileName string) error {

	if utils.TraceEnabled() {

		verticalWriterLogger.Trace(fmt.Sprintf("writer %v started reading file %v", writer.writerId, fileName))
	}

	file, err := os.OpenFile(fileName, os.O_RDWR, 0666)

	defer func(file *os.File) {

		if file != nil {

			_ = file.Close()
		}

		if utils.TraceEnabled() {

			verticalWriterLogger.Trace(fmt.Sprintf("writer %v deleting file %v", writer.writerId, fileName))
		}

		err = os.Remove(fileName)

		if err != nil {

			verticalWriterLogger.Error(fmt.Sprintf("writer %v failed to delete file %v, reason: %v", writer.writerId, fileName, err))
		}
	}(file)

	if err != nil {

		verticalWriterLogger.Error(fmt.Sprintf("writer %v failed to open the file %v, reason: %v", writer.writerId, fileName, err))

		return err
	}

	var stat os.FileInfo

	if stat, err = file.Stat(); err == nil && int(stat.Size()) > len(writer.valueBufferBytes) {

		writer.bufferBytes, err = os.ReadFile(fileName)

		return err
	}

	length, err := file.ReadAt(writer.valueBufferBytes, 0)

	if err != nil && !strings.EqualFold(err.Error(), "EOF") {

		return err
	}

	writer.bufferBytes = writer.valueBufferBytes[:length]

	return nil

}

func (writer *VerticalWriter) processNetRouteEvent(netRouteId, field string) {

	var err error

	writer.stringValues[0], err = writer.readStringColumn()

	if err != nil {

		verticalWriterLogger.Error(fmt.Sprintf("writer %v failed to read string column, reason: %v , for plugin %v", writer.writerId, err, writer.plugin))

		return
	}

	key := datastore.GetMetricKey(netRouteId, field, INT64ToStringValue(writer.tick))

	writer.keyBuffers[0] = []byte(key + datastore.DefaultKeyPart)

	writer.keyBuffers[1] = []byte(datastore.GetTimeTickKey(key) + datastore.DefaultKeyPart)

	store := datastore.GetStore(datastore.GetStoreById(writer.tick, writer.plugin, datastore.VerticalStore), writer.storeType, true, true, writer.encoder, writer.tokenizers[1])

	if store == nil {

		verticalWriterLogger.Error(fmt.Sprintf(utils.ErrorAcquireStore, datastore.GetStoreById(writer.tick, writer.plugin, datastore.VerticalStore)) + fmt.Sprintf(MessageWriterId, writer.writerId))

		return
	}

	tickPoolIndex, ticks := writer.encoder.MemoryPool.AcquireINT32Pool(1)

	ticks[0] = utils.UnixToSeconds(writer.tick)

	bufferIndex := utils.NotAvailable

	writer.blobEvent.KeyBytes = writer.keyBuffers[metricValueKeyIndex]

	writer.blobEvent.ValueBytes = writer.readBuffers[metricValueKeyIndex]

	writer.blobEvent.Values = writer.stringValues

	writer.blobEvent.Encoder = writer.encoder

	writer.blobEvent.Store = store

	writer.blobEvent.Padding = utils.MaxValueBytes

	writer.blobEvent.Tokenizer = writer.tokenizers[1]

	writer.blobEvent.DiskIOEvent = writer.event

	writer.blobEvent.WaitGroup = &writer.waitGroup

	writer.blobEvent.Encoding = datastore.GetBlobEncoding(len(writer.stringValues[0]), writer.batchSize)

	bufferIndex, writer.writeBuffers[metricValueKeyIndex], err = datastore.WriteBlobColumnValues(writer.blobEvent)

	defer writer.encoder.MemoryPool.ReleaseBytePool(bufferIndex)

	encodedTickPoolIndex := utils.NotAvailable

	encodedTickPoolIndex, writer.writeBuffers[metricTimeKeyIndex] = writer.encodeTicks(ticks)

	defer writer.encoder.MemoryPool.ReleaseBytePool(encodedTickPoolIndex)

	writer.encoder.MemoryPool.ReleaseINT32Pool(tickPoolIndex)

	if err != nil {

		verticalWriterLogger.Error(fmt.Sprintf(utils.ErrorWriteKey, string(writer.keyBuffers[1]), store.GetName(), err.Error()) + fmt.Sprintf(MessageWriterId, writer.writerId))

		return
	}

	if err = store.PutMultiples(writer.keyBuffers[:2], writer.writeBuffers[:2], writer.encoder, writer.tokenizers[1]); err != nil {

		verticalWriterLogger.Error(fmt.Sprintf("writer %v failed to write netroute event, reason: %v", writer.writerId, err))

		return
	}

}

//store type
// performance
// status
// status flap
// trap
// policy
