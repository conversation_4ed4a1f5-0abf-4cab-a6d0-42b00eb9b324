/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/* Change Logs:
* Date                   Author          		Notes
* 2025-03-05			 <PERSON><PERSON><PERSON>tadata-5190  Migrated constants from datastore to utils to match SonarQube Standard
* 2025-04-09			 Dhaval <PERSON>ra			Motadata-4859  Refactoring "fmt.Sprintf" to update go version to 1.24
* 2025-05-05			 Swapnil <PERSON>. <PERSON>		M<PERSON>ADATA-6078 passed defaultblobpool const as the function parameter in memory pool initialization
* 2025-05-10             <PERSON><PERSON><PERSON>OTADATA-6174  Added Unmap function to release memory-mapped resources
 */

/*
	In the case of log plugins, columns are not predefined, so we don't know which columns to put in aggregation
	and indexable, so we run index jobs in which we observe data for a specific number of records and decide which
	columns to put in indexable and aggregation.

	For logs, when a batch arrives at a horizontal writer, we send it to an indexjob, which examines the data for a
	specific batch number and places columns based on it.
*/

package job

import (
	"encoding/json"
	"fmt"
	"motadatadatastore/codec"
	"motadatadatastore/datastore"
	"motadatadatastore/utils"
	"os"
	"time"
)

const (
	IndexJobCTX = "index-jobs.ctx"
)

type IndexJob struct {
	tokenizer *utils.Tokenizer

	logger utils.Logger

	context utils.MotadataMap

	encoder codec.Encoder

	ShutdownNotifications chan bool

	shutdown bool
}

func NewIndexJob() *IndexJob {

	pool := utils.NewMemoryPool(2, utils.MaxPoolLength, false, utils.DefaultBlobPools)

	return &IndexJob{

		encoder: codec.NewEncoder(pool),

		context: utils.MotadataMap{},

		logger: utils.NewLogger("Index Job", "job"),

		ShutdownNotifications: make(chan bool, 5),
	}
}

func (job *IndexJob) Start() {

	job.tokenizer = &utils.Tokenizer{

		Tokens: make([]string, utils.TokenizerLength),
	}

	go func() {

		utils.JobEngineShutdownMutex.Add(1)

		for {

			if job.shutdown || utils.GlobalShutdown {

				break
			}

			job.run()

		}

		utils.JobEngineShutdownMutex.Done()

	}()
}

func (job *IndexJob) run() {

	defer func() {

		if r := recover(); r != nil {

			job.logger.Error(fmt.Sprintf("err %v occurred while processing indices", r))

		}

	}()

	if !job.loadJobs() {

		job.logger.Error("failed to load indexing jobs...")

		return
	}

	for {

		select {

		case request := <-utils.IndexJobRequests:

			job.updateIndexerProbeRecords(request)

		case <-job.ShutdownNotifications:

			job.encoder.MemoryPool.Unmap()

			job.logger.Info("shutting down...")

			job.shutdown = true

			return

		}

	}

}

func (job *IndexJob) loadJobs() bool {

	_, err := os.Stat(utils.JobDir)

	if os.IsNotExist(err) {

		err = os.MkdirAll(utils.JobDir, 0755)

		if err != nil {

			job.logger.Error(fmt.Sprintf("failed to create jobs dir, reason: %v", err.Error()))

			return false

		}

		return true
	}

	bytes, err := os.ReadFile(utils.JobDir + utils.PathSeparator + IndexJobCTX)

	if err != nil && !os.IsNotExist(err) {

		job.logger.Error(fmt.Sprintf("failed to read the job file, reason: %v", err.Error()))
	}

	if bytes != nil && len(bytes) > 0 {

		err = json.Unmarshal(bytes, &job.context)

		if err != nil {

			job.logger.Error(fmt.Sprintf("failed to load indexer jobs, reason %v", err.Error()))

			return false
		}
	}

	return true
}

func (job *IndexJob) sync() {

	bytes, err := json.MarshalIndent(job.context, "", " ")

	if err != nil {

		job.logger.Error(fmt.Sprintf("failed to sync jobs, reason: %v", err.Error()))
	}

	err = os.WriteFile(utils.JobDir+utils.PathSeparator+IndexJobCTX, bytes, 0644)

	if err != nil {

		job.logger.Error(fmt.Sprintf("failed to sync jobs, reason: %v", err.Error()))
	}

}

/*
We first create a posting list for each column for specific numbers of batches, and once the index has completed its task,
only selected columns will remain as indexable, and the remaining columns' posting lists will be discontinued.
*/
func (job *IndexJob) index(plugin string) {

	timestamp := time.Now().UTC()

	timestamp = time.Date(timestamp.Year(), timestamp.Month(), timestamp.Day(), 0, 0, 0, 0, time.UTC)

	stores := make([]string, 2)

	columns := job.context.GetMapValue(plugin).GetMapValue(utils.Columns)

	records := make(map[string]int, len(columns))

	indices := make(map[string][]string)

	for {

		found := false

		for column := range columns {

			if !datastore.IsInvalidIndexableColumn(plugin, column) {

				stores[0] = datastore.GetStoreName(utils.UnixToSeconds(timestamp.Unix()), column+utils.HyphenSeparator+plugin, datastore.HorizontalStore) + utils.HyphenSeparator + codec.INTToStringValue(int(codec.Int64))

				stores[1] = datastore.GetStoreName(utils.UnixToSeconds(timestamp.Unix()), column+utils.HyphenSeparator+plugin, datastore.HorizontalStore) + utils.HyphenSeparator + codec.INTToStringValue(int(codec.String))

				for _, storeName := range stores {

					if datastore.IsStoreAvailable(storeName) {

						found = true

						// we get number of unique values from length of posting list store's keys
						if store := datastore.GetStore(storeName, utils.None, false, true, job.encoder, job.tokenizer); store != nil {

							buffers, _ := store.GetKeys(nil, nil, false, codec.Invalid)

							records[column] += len(buffers)

							indices[column] = append(indices[column], storeName)

						}

					}
				}

			} else {

				delete(columns, column)

				job.removeStore(datastore.GetStoreName(utils.UnixToSeconds(timestamp.Unix()), column+utils.HyphenSeparator+plugin, datastore.HorizontalStore) + utils.HyphenSeparator + codec.INTToStringValue(int(codec.Int64)))

				job.removeStore(datastore.GetStoreName(utils.UnixToSeconds(timestamp.Unix()), column+utils.HyphenSeparator+plugin, datastore.HorizontalStore) + utils.HyphenSeparator + codec.INTToStringValue(int(codec.String)))

			}

		}

		if !found {

			break
		}

		timestamp = timestamp.AddDate(0, 0, -1)

	}

	for column, count := range records {

		// column available in invalidIndexableColumns will be discarded from indexable columns
		if count >= (utils.MaxIndexProbes*utils.IndexerThresholdPercent)/100 {

			/*
				delete the column that contain search terms more than the threshold count and close corresponding store
			*/

			delete(columns, column)

			for _, storeName := range indices[column] {

				job.removeStore(storeName)

			}
		}
	}

	for column := range datastore.LogDefaultColumns {

		columns[column] = utils.Empty
	}

	datastore.AlterIndexableColumns(plugin, columns, utils.Add)

	delete(job.context, plugin)
}

func (job *IndexJob) updateIndexerProbeRecords(request utils.MotadataMap) {

	plugin := request.GetStringValue(utils.Plugin)

	if datastore.IsIndexablePlugin(plugin) { //safe side check

		return
	}

	if request.Contains(utils.Columns) {

		if job.context.Contains(plugin) {

			contexts := job.context.GetMapValue(plugin)

			contexts[utils.BatchSize] = contexts.GetIntValue(utils.BatchSize) + request.GetIntValue(utils.BatchSize)

			columns := contexts.GetMapValue(utils.Columns)

			// while probing updating column, to add column which was unavailable in initial request
			for column := range utils.ToMap(request[utils.Columns]) {

				columns[column] = utils.Empty

			}

			if contexts.GetIntValue(utils.BatchSize) >= utils.MaxIndexProbes {

				job.index(plugin)

			}

		} else {

			job.context[plugin] = request
		}

		job.sync()

	}
}

func (job *IndexJob) removeStore(storeName string) {

	if datastore.IsStoreAvailable(storeName) {

		if store := datastore.GetStore(storeName, utils.None, false, true, job.encoder, job.tokenizer); store != nil {

			store.Close(job.encoder)

			if store.IsClosed() {

				datastore.RemoveStore(storeName)
			}
		}
	}

}
