/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/*

	When a store has many segment files and a large amount of key, value data that has been updated many times,
	there will be many fragments in the segment files that are unusable and take up disc space, so we append all values
	from the segment files to one file named 255, which is an append only file.

	At regular intervals, we check for dirty stores and clean them up.

*/

/* Change Logs:
* Date                   Author          		Notes
* 2025-02-10			 Dhaval <PERSON>ra			Motadata-4913  Altered modulo operator with new modulo function
* 2025-05-05			 <PERSON><PERSON><PERSON><PERSON>l <PERSON><PERSON>		M<PERSON>ADATA-6078 passed defaultb<PERSON>bpool const as the function parameter in memory pool initialization
* 2025-05-10             <PERSON><PERSON>val <PERSON>ra            MOTADATA-6174  Added Unmap function to release memory-mapped resources
 */

package job

import (
	"fmt"
	"golang.org/x/sys/unix"
	"motadatadatastore/codec"
	"motadatadatastore/datastore"
	"motadatadatastore/storage"
	"motadatadatastore/utils"
	"os"
	"runtime"
	"strings"
	"sync"
	"time"
)

type StoreCleanUpJob struct {
	tokenizer *utils.Tokenizer

	encoder codec.Encoder

	logger utils.Logger

	duration time.Duration

	ShutdownNotifications chan bool

	keyBuffers, valueBuffers [][]byte

	id int

	batchIOEvent storage.DiskIOEventBatch

	event storage.DiskIOEvent

	waitGroup *sync.WaitGroup

	shutdown bool
}

func NewStoreCleanUpJob(id int) *StoreCleanUpJob {

	pool := utils.NewMemoryPool(6, utils.CleanupJobPoolLength, false, utils.DefaultBlobPools)

	valueBuffers := make([][]byte, utils.MaxWorkerEventKeyGroupLength)

	for index := range valueBuffers {

		bytes, err := unix.Mmap(-1, 0, utils.MaxValueBufferBytes, unix.PROT_READ|unix.PROT_WRITE, unix.MAP_PRIVATE|unix.MAP_ANON)

		if err != nil {

			bytes = make([]byte, utils.MaxValueBufferBytes)
		}

		valueBuffers[index] = bytes

	}

	return &StoreCleanUpJob{

		id: id,

		encoder: codec.NewEncoder(pool),

		logger: utils.NewLogger(fmt.Sprintf("CleanUp Job-%v", id), "job"),

		keyBuffers: make([][]byte, utils.MaxWorkerEventKeyGroupLength),

		valueBuffers: valueBuffers,

		duration: time.Hour * 1,

		ShutdownNotifications: make(chan bool, 5),

		batchIOEvent: storage.DiskIOEventBatch{},

		event: storage.DiskIOEvent{},

		waitGroup: &sync.WaitGroup{},
	}
}

func (job *StoreCleanUpJob) Start() {

	job.tokenizer = &utils.Tokenizer{

		Tokens: make([]string, utils.TokenizerLength),
	}

	go func() {

		utils.JobEngineShutdownMutex.Add(1)

		for {

			if job.shutdown || utils.GlobalShutdown {

				break
			}

			job.run()
		}

		utils.JobEngineShutdownMutex.Done()
	}()
}

func (job *StoreCleanUpJob) run() {

	ticker := time.NewTicker(job.duration)

	defer func() {

		if err := recover(); err != nil {

			ticker.Stop()

			stackTraceBytes := make([]byte, 1<<20)

			job.logger.Error(fmt.Sprintf("error %v occurred", err))

			job.logger.Error(fmt.Sprintf("!!!STACK TRACE for store cleanup job !!! \n %v", string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))

			job.logger.Error(fmt.Sprintf("restarting store cleanup job - %v", job.id))

		}
	}()

	dataStoreDir := utils.CurrentDir + utils.PathSeparator + utils.DatastoreDir

	for {

		select {

		case <-ticker.C:

			_, err := os.Stat(dataStoreDir)

			if os.IsNotExist(err) {

				continue
			}

			dirs, err := os.ReadDir(dataStoreDir)

			if err != nil {

				continue
			}

			for _, dir := range dirs {

				if dir.IsDir() && utils.GetFastModN(utils.GetHash64([]byte(dir.Name())), utils.StoreCleanUpJobs) == job.id && datastore.IsStoreAvailable(dir.Name()) {

					store := datastore.GetStore(dir.Name(), utils.None, false, false, job.encoder, job.tokenizer)

					if store == nil || store.IsClosed() {

						continue
					}

					storeName := store.GetName()

					//For mapping, config store If the store is not used for a long time, we close it to release the memory held by the store.
					if store.GetDatastoreType() == utils.Mapping || store.GetDatastoreType() == utils.StaticMetric {

						if time.Now().Unix()-store.GetLastUsedTimestamps() >= utils.IdleStoreDetectionThresholdSeconds {

							store.Close(job.encoder)

							if store.IsClosed() {

								datastore.RemoveStore(storeName)
							}
						}

						continue
					}

					utils.Split(storeName, utils.HyphenSeparator, job.tokenizer)

					// here we delete the store related the view which are no longer needed
					if strings.Contains(job.tokenizer.Tokens[3], utils.AggregationSeparator) {

						aggregation := strings.Join(job.tokenizer.Tokens[2:4], utils.HyphenSeparator)

						utils.Split(aggregation, utils.AggregationSeparator, job.tokenizer)

						if !datastore.IsHorizontalAggregationExist(job.tokenizer.Tokens[0], aggregation) {

							store.Close(job.encoder)

							datastore.RemoveStore(storeName)

							//deleting store in order to claim disk space as particular store is not of use as we have deleted that aggregation view
							datastore.DeleteStore(storeName)

							continue
						}

					}

					if utils.DebugEnabled() {

						job.logger.Debug(fmt.Sprintf("cleanup started for store %v", dir.Name()))
					}

					store.Cleanup(job.encoder, job.keyBuffers, job.valueBuffers, job.batchIOEvent, job.event, job.waitGroup)

					if store.IsClosed() {

						datastore.RemoveStore(store.GetName())
					}

					if utils.DebugEnabled() {

						job.logger.Debug(fmt.Sprintf("cleanup finished for store %v", dir.Name()))
					}
				}
			}

		case <-job.ShutdownNotifications:

			job.logger.Info("shutting down...")

			for index := range job.valueBuffers {

				if err := unix.Munmap(job.valueBuffers[index]); err != nil {

					job.logger.Warn(fmt.Sprintf("failed to unmap anonymous mapped buffer,reason: %v", err.Error()))

				}
			}

			job.encoder.MemoryPool.Unmap()

			job.shutdown = true

			return

		}

	}
}
