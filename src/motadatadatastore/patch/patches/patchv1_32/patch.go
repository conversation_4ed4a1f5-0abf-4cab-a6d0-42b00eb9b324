/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/* Change Logs:
* Date                   Author          		Notes
* 2025-03-05			 <PERSON><PERSON><PERSON>-5190  Migrated constants from datastore to utils to match SonarQube Standard
 */

package patchv1_32

import (
	"encoding/json"
	"motadatadatastore/patch/patches"
	"motadatadatastore/utils"
	"os"
)

const (
	DatastoreDataWriterValueBufferBytesLength = "datastore.data.writer.value.buffer.bytes.length"

	DatastoreDataWriterTXNBufferBytesLength = "datastore.data.writer.txn.buffer.bytes.length"

	DatastoreConfigWriters = "datastore.config.writers"

	DatastoreHealthWriters = "datastore.health.writers"

	HealthJobFlushTimerSeconds = "health.job.flush.timer.seconds"
)

func Patch() error {

	//update motadata-datastore.json

	bytes, err := os.ReadFile(utils.CurrentDir + utils.PathSeparator + utils.ConfigDir + utils.PathSeparator + utils.MotadataDatastoreConfigFile)

	if err != nil {

		return err
	}

	configs := make(utils.MotadataMap)

	if err = json.Unmarshal(bytes, &configs); err != nil {

		return err
	}

	//DatastoreDataWriterTXNBufferBytesLength

	if configs.Contains(DatastoreDataWriterValueBufferBytesLength) {

		configs["datastore.data.writer.value.buffer.bytes"] = configs.GetIntValue(DatastoreDataWriterValueBufferBytesLength)

		delete(configs, DatastoreDataWriterValueBufferBytesLength)
	}

	if configs.Contains(DatastoreDataWriterTXNBufferBytesLength) {

		configs["datastore.data.writer.txn.buffer.bytes"] = configs.GetIntValue(DatastoreDataWriterTXNBufferBytesLength)

		delete(configs, DatastoreDataWriterTXNBufferBytesLength)

	}

	if configs.Contains(DatastoreConfigWriters) {

		configs["datastore.static.metric.writers"] = configs.GetIntValue(DatastoreConfigWriters)

		delete(configs, DatastoreConfigWriters)
	}

	if configs.Contains(DatastoreHealthWriters) {

		configs["datastore.health.metric.writers"] = configs.GetIntValue(DatastoreHealthWriters)

		delete(configs, DatastoreHealthWriters)
	}

	if configs.Contains(HealthJobFlushTimerSeconds) {

		configs["health.metric.flush.timer.seconds"] = configs.GetIntValue(HealthJobFlushTimerSeconds)

		delete(configs, HealthJobFlushTimerSeconds)
	}

	err = patches.UpdateDatastoreConfigs(configs)

	if err != nil {

		return err
	}

	//remove old health statistics folder

	_ = os.RemoveAll(utils.EventDir + utils.PathSeparator + utils.HorizontalFormat + utils.HyphenSeparator + "health.statistics")

	return nil
}
