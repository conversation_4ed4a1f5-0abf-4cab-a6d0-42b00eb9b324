/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

/*
	In DATASTORE store values directly in segment file but for some cases we do nto write data directly into segment file

	For example, with string data type, we have 100 values in one key, and due to the nature of that data, some repetitive strings occur,
	so we do not write all data, we simply map all strings with numeric values and put numeric values in with key, while all the mapped strings stored in mapping store.

	We also use mappings in cases where a column is indexable, which allows the query module to easily parse the query.
*/

/* Change Logs:
* Date                   Author          		Notes
* 2025-04-09			 <PERSON><PERSON><PERSON>-4859  Refactoring "fmt.Sprintf" to update go version to 1.24
* 2025-05-01			 <PERSON><PERSON><PERSON>-6077  Added Panic Recover In Case Of Rebuild Mapping
* 2025-05-15			 Vedant D. Dokania		Motadata-6251 Sonar Error Fixing

 */

package storage

import (
	bytes2 "bytes"
	"errors"
	"fmt"
	"github.com/blevesearch/vellum"
	"github.com/blevesearch/vellum/regexp"
	"github.com/dolthub/swiss"
	"github.com/kamstrup/intmap"
	"github.com/kelindar/bitmap"
	"motadatadatastore/codec"
	"motadatadatastore/utils"
	"os"
	"runtime"
	"sort"
	"strings"
)

const (
	DummyStringMappingOrdinal = 1

	DummyString = "1"

	FileMapping112 = "mapping.112"

	FileMapping176 = "mapping.176"

	FileMerged112 = "merged.112"

	FileMerged176 = "merged.176"

	FileTemp176 = "temp.176"

	FileTemp112 = "temp.112"

	FileBKP112 = "bkp.112"

	FileBKP176 = "bkp.176"

	FileBKPTemp176 = "temp.bkp.176"

	FileBKPTemp112 = "temp.bkp.112"
)

var mappingLogger = utils.NewLogger("Mapping", "storage")

func resolveMapping(store *Store, poolIndex int, encoder codec.Encoder, stringMappings, numericMappings *swiss.Map[int32, []int], size int) (error, codec.DataType, int) {

	ordinals := encoder.MemoryPool.GetINT32Pool(poolIndex)[:size]

	dataType := codec.Int64

	var err error

	var iterator vellum.Iterator

	for i, ordinal := range ordinals {

		if ordinal%2 == 0 {

			values, _ := numericMappings.Get(ordinal)

			values = append(values, i)

			numericMappings.Put(ordinal, values)

		} else {

			if ordinal != DummyStringMappingOrdinal {

				values, _ := stringMappings.Get(ordinal)

				values = append(values, i)

				stringMappings.Put(ordinal, values)
			}

		}

	}

	found := false

	if stringMappings.Count() > 0 {

		found = true

		dataType = codec.String

		valuePoolIndex, values := encoder.MemoryPool.AcquireStringPool(len(ordinals))

		poolIndex = valuePoolIndex

		//lookup in cache entries
		if store.cacheStringMappings != nil && store.cacheStringMappings.Count() > 0 {

			stringMappings.Iter(func(ordinal int32, indices []int) (stop bool) {

				if bytes, ok := store.cacheStringMappings.Get(ordinal); ok {

					for _, index := range indices {

						values[index] = bytes
					}

					stringMappings.Delete(ordinal)

				}

				return stop

			})

		}

		//lookup in memory fst . Becomes nil when store sync
		if stringMappings.Count() > 0 && store.stringMappings != nil {

			store.stringMappings.Iter(func(value string, ordinal int32) (stop bool) {

				if indices, ok := stringMappings.Get(ordinal); ok {

					for _, index := range indices {

						values[index] = value
					}

					stringMappings.Delete(ordinal)
				}

				return stop
			})
		}

		//lookup in store fst
		if stringMappings.Count() > 0 && store.stringMapping != nil {

			iterator, err = store.stringMapping.Iterator(nil, nil)

			for err == nil {

				bytes, ordinal := iterator.Current()

				if indices, ok := stringMappings.Get(int32(ordinal)); ok {

					for _, index := range indices {

						values[index] = string(bytes)
					}

					stringMappings.Delete(int32(ordinal))
				}

				if stringMappings.Count() == 0 {

					err = nil

					break
				}

				err = iterator.Next()
			}
		}
	}

	if numericMappings.Count() > 0 {

		found = true

		if dataType == codec.String {

			values := encoder.MemoryPool.GetStringPool(poolIndex)

			//lookup in cache entries
			if store.cacheNumericMappings != nil && store.cacheNumericMappings.Len() > 0 {

				numericMappings.Iter(func(ordinal int32, indices []int) (stop bool) {

					if value, ok := store.cacheNumericMappings.Get(ordinal); ok {

						value := codec.INT64ToStringValue(value)

						for _, index := range indices {

							values[index] = value
						}

						numericMappings.Delete(ordinal)

					}

					return stop
				})

			}

			//lookup in memory fst . Becomes nil when store sync
			if numericMappings.Count() > 0 && store.numericMappings != nil {

				for value, ordinal := range store.numericMappings.All() {

					if indices, ok := numericMappings.Get(ordinal); ok {

						numericValue := codec.INT64ToStringValue(value)

						for _, index := range indices {

							values[index] = numericValue
						}

						numericMappings.Delete(ordinal)
					}
				}
			}

			//lookup in store fst
			if numericMappings.Count() > 0 && store.numericMapping != nil {

				iterator, err = store.numericMapping.Iterator(nil, nil)

				for err == nil {

					bytes, ordinal := iterator.Current()

					if indices, ok := numericMappings.Get(int32(ordinal)); ok {

						for _, index := range indices {

							values[index] = codec.INT64ToStringValue(codec.ReadBigEndianINT64Value(bytes))
						}

						numericMappings.Delete(int32(ordinal))
					}

					if numericMappings.Count() == 0 {

						err = nil

						break
					}

					err = iterator.Next()
				}
			}
		} else {

			valuePoolIndex, values := encoder.MemoryPool.AcquireINT64Pool(len(ordinals))

			/*Scenario - Store has only numeric ordinals and we have set dummy String ordinal in case of missing column , if we don't reset the pool than we will get the previous values set in the pool*/
			encoder.MemoryPool.ResetINT64Pool(valuePoolIndex, len(ordinals), 0)

			poolIndex = valuePoolIndex

			//lookup in cache entries
			if store.cacheNumericMappings != nil && store.cacheNumericMappings.Len() > 0 {

				numericMappings.Iter(func(ordinal int32, indices []int) (stop bool) {

					if value, ok := store.cacheNumericMappings.Get(ordinal); ok {

						for _, index := range indices {

							values[index] = value
						}

						numericMappings.Delete(ordinal)

					}

					return stop
				})

			}

			//lookup in memory fst . Becomes nil when store sync
			if numericMappings.Count() > 0 && store.numericMappings != nil {

				for value, ordinal := range store.numericMappings.All() {

					if indices, ok := numericMappings.Get(ordinal); ok {

						for _, index := range indices {

							values[index] = value
						}

						numericMappings.Delete(ordinal)
					}
				}
			}

			//lookup in store fst
			if numericMappings.Count() > 0 && store.numericMapping != nil {

				iterator, err = store.numericMapping.Iterator(nil, nil)

				for err == nil {

					bytes, ordinal := iterator.Current()

					if indices, ok := numericMappings.Get(int32(ordinal)); ok {

						for _, index := range indices {

							values[index] = codec.ReadBigEndianINT64Value(bytes)
						}

						numericMappings.Delete(int32(ordinal))
					}

					if numericMappings.Count() == 0 {

						err = nil

						break
					}

					err = iterator.Next()
				}
			}
		}

	}

	// means all reserved mappings ordinal of string
	if !found {

		valuePoolIndex, _ := encoder.MemoryPool.AcquireStringPool(size)

		poolIndex = valuePoolIndex

		return nil, codec.String, poolIndex
	}

	return err, dataType, poolIndex
}

////////////////////////////////// String Mapping //////////////////////////////////////////

func writeStringMapping(value string, ordinal int32, store *Store, encoder codec.Encoder) error {

	var err error

	if store.stringMappingTempFile == nil {

		store.stringMappingTempFile, err = os.Create(store.path + utils.PathSeparator + FileTemp176)

		if err != nil {

			return errors.New(fmt.Sprintf("failed to create temp string mapping file for the store %v, reason:%v", store.name, err.Error()))

		}

		_ = store.stringMappingTempFile.Truncate(int64(2+len(value)+4) * 4)

		store.stringMappingMemoryMappedBytes, err = Map(store.stringMappingTempFile, ReadWrite)

		if err != nil {

			_ = store.stringMappingTempFile.Close()

			_ = os.Remove(store.path + utils.PathSeparator + FileTemp176)

			if store.stringMappingMemoryMappedBytes != nil {

				err1 := store.stringMappingMemoryMappedBytes.unmap()

				if err1 != nil {

					err = errors.Join(err, err1)
				}

			}

			mappingLogger.Error(fmt.Sprintf("failed to create string mapping for the store %v, reason:%v", store.name, err.Error()))

			return errors.New(fmt.Sprintf("failed to create string mapping for the store %v, reason:%v", store.name, err.Error()))

		}

		store.stringMappings = swiss.NewMap[string, int32](500)
	} else {

		if len(store.stringMappingMemoryMappedBytes) < store.stringMappingSize+(2+len(value)+4) {

			store.stringMappingMemoryMappedBytes, err = Remap(store.stringMappingMemoryMappedBytes, (len(store.stringMappingMemoryMappedBytes)+2+len(value)+4)*4, store.stringMappingTempFile)

			if err != nil {

				_ = store.stringMappingTempFile.Close()

				if store.stringMappingMemoryMappedBytes != nil {

					_ = store.stringMappingMemoryMappedBytes.unmap()
				}

				mappingLogger.Error(fmt.Sprintf("failed to extend the string mappping for the store %v, reason:%v", store.name, err.Error()))

				return errors.New(fmt.Sprintf("failed to extend the string mappping for the store %v, reason:%v", store.name, err.Error()))

			}
		}
	}

	index, encodedBytes := writeStringMappingItem(value, ordinal, encoder)

	if _, err = store.stringMappingBackupFile.Write(encodedBytes); err != nil {

		mappingLogger.Error(fmt.Sprintf(utils.ErrorWriteStringMapping, err.Error()))
	}

	copy(store.stringMappingMemoryMappedBytes[store.stringMappingSize:], encodedBytes)

	store.stringMappingSize += len(encodedBytes)

	store.stringMappings.Put(value, ordinal)

	if store.cacheStringMappings.Count()+1 <= store.maxMappingCacheRecords {

		store.cacheStringMappings.Put(ordinal, value)

	}

	encoder.MemoryPool.ReleaseBytePool(index)

	return err
}

/*
existing :-

Insertion:-
2 bytes length of key
next bytes key itself
8 bytes offset/ value
EOT
Retrieval :-
 1. Splits from EOT bytes and do the operation

NEW:-

Insertion :-
 1. First 4 bytes ( length of key +key itself + offset length )
 2. 4 bytes length of key
 3. next bytes key itself
 4. 8 bytes offset/value
 5. EOT bytes

Retrieval :-
 1. First read 4 bytes of length and after that length if EOT is present than only we will consider it as healthy record and we will continue till end of the buffer. No split will be there.
*/
func writeStringMappingItem(value string, ordinal int32, encoder codec.Encoder) (int, []byte) {

	index, bytes := encoder.MemoryPool.AcquireBytePool(4 + 2 + len(value) + 4 + 3)

	bufferIndex, bufferBytes := encoder.WriteINT32Value(int32(2+len(value)+4), 0)

	copy(bytes, bufferBytes)

	encoder.MemoryPool.ReleaseBytePool(bufferIndex)

	bufferIndex, bufferBytes = encoder.WriteUINT16Value(uint16(len(value)), 0)

	copy(bytes[4:], bufferBytes)

	encoder.MemoryPool.ReleaseBytePool(bufferIndex)

	copy(bytes[4+2:], value)

	bufferIndex, bufferBytes = encoder.WriteINT32Value(ordinal, 0)

	copy(bytes[4+2+len(value):], bufferBytes)

	copy(bytes[len(bytes)-3:], utils.EOTBytes)

	encoder.MemoryPool.ReleaseBytePool(bufferIndex)

	return index, bytes

}

func mergeStringMapping(store *Store, encoder codec.Encoder, recoveryMode MappingRecoveryMode) {

	defer func() {

		if err := recover(); err != nil {

			mappingLogger.Error(fmt.Sprintf("error %v occurred while merge string mapping for store %v", err, store.name))
		}
	}()

	if store.stringMappings != nil && store.stringMappings.Count() > 0 {

		if recoveryMode == Normal && utils.EnvironmentType != utils.DatastoreBenchUnitEnvironment {

			err := store.stringMappingMemoryMappedBytes.unmap()

			if err != nil {

				mappingLogger.Fatal(fmt.Sprintf("failed to unmap string mapping memory mapped file for store %v, reason : %v", store.name, err.Error()))
			}

			_ = store.stringMappingTempFile.Close()

			store.stringMappingMemoryMappedBytes = nil

			store.stringMappingTempFile = nil

			store.stringMappingSize = 0
		}

		var buffer bytes2.Buffer

		tempMappingWriter, err := vellum.New(&buffer, nil)

		if err != nil {

			mappingLogger.Error(fmt.Sprintf("failed to create the temp string mapping writer for the store %v, reason:%v", store.name, err.Error()))

			return
		}

		poolIndex := utils.NotAvailable

		var values []string

		if store.stringMappings.Count() > encoder.MemoryPool.GetPoolLength() {

			values = make([]string, store.stringMappings.Count(), store.stringMappings.Count())

		} else {

			poolIndex, values = encoder.MemoryPool.AcquireStringPool(store.stringMappings.Count())
		}

		i := 0

		store.stringMappings.Iter(func(value string, _ int32) (stop bool) {

			values[i] = value

			i++

			return stop
		})

		sort.Strings(values)

		for j := range values {

			value, _ := store.stringMappings.Get(values[j])

			_ = tempMappingWriter.Insert([]byte(values[j]), uint64(value))
		}

		if poolIndex != utils.NotAvailable {

			encoder.MemoryPool.ReleaseStringPool(poolIndex)
		}

		_ = tempMappingWriter.Close()

		if utils.EnvironmentType != utils.DatastoreBenchUnitEnvironment {

			store.stringMappings = nil
		}

		if recoveryMode != Full && store.stringMapping != nil && store.stringMapping.Len() > 0 {

			iterators := make([]vellum.Iterator, 2)

			iterator, _ := store.stringMapping.Iterator(nil, nil)

			iterators[0] = iterator

			tempMapping, err := vellum.Load(buffer.Bytes())

			if err != nil {

				mappingLogger.Error(fmt.Sprintf("failed to load temp string mapping of the store %v, reason:%v", store.name, err.Error()))

				return
			}

			iterator, _ = tempMapping.Iterator(nil, nil)

			iterators[1] = iterator

			file, err := os.Create(store.path + utils.PathSeparator + FileMerged176)

			if err != nil {

				mappingLogger.Error(fmt.Sprintf("failed to create the merged string mapping file for the store %v, reason:%v", store.name, err.Error()))

				_ = tempMapping.Close()

				return
			}

			err = vellum.Merge(file, nil, iterators, func(values []uint64) uint64 {

				return values[0]
			})

			if err != nil {

				mappingLogger.Error(fmt.Sprintf(utils.ErrorMergeNumericMapping, store.name, err.Error()))

				_ = file.Close()

				_ = os.Remove(store.path + utils.PathSeparator + FileMerged176)

				_ = tempMapping.Close()

				return
			}

			_ = file.Close()

			_ = tempMapping.Close()

			_ = store.stringMapping.Close()

			err = os.Rename(store.path+utils.PathSeparator+FileMerged176,
				store.path+utils.PathSeparator+FileMapping176)

			if err != nil {

				err = os.Remove(store.path + utils.PathSeparator + FileMerged176)

				mappingLogger.Error(fmt.Sprintf("failed to rename the merged string mapping of the store %v, reason:%v", store.name, err.Error()))

				return
			}

		} else {

			//closed previously opened file if any
			if store.stringMapping != nil {

				_ = store.stringMapping.Close()

			}

			err = os.WriteFile(store.path+utils.PathSeparator+FileMapping176, buffer.Bytes(), 0666)

			if err != nil {

				mappingLogger.Error(fmt.Sprintf("failed to write the string mapping of the store %v, reason:%v", store.name, err.Error()))

				return
			}
		}

		store.stringMapping, err = vellum.Open(store.path + utils.PathSeparator + FileMapping176)

		if err != nil {

			mappingLogger.Error(fmt.Sprintf("failed to open the string mapping of the store %v, reason:%v", store.name, err.Error()))

			return

		}

		if utils.EnvironmentType != utils.DatastoreBenchUnitEnvironment {

			_ = os.Remove(store.path + utils.PathSeparator + FileTemp176)
		}

	}

}

func readStringMappingItem(bytes []byte) (string, int32) {

	index := 0

	if len(bytes) > 2 {

		length := codec.ReadUINT16Value(bytes[:2], &index)

		if len(bytes[index:]) == length+4 {

			return string(bytes[index : index+length]), codec.ReadINT32Value(bytes[index+length : index+length+4])

		}

	}

	return "", DummyStringMappingOrdinal

}

func rebuildStringMapping(store *Store, encoder codec.Encoder, recoveryMode MappingRecoveryMode) error {

	defer func() {

		if err := recover(); err != nil {

			if recoveryMode == Partial {

				if store.stringMappingTempFile != nil {

					_ = store.stringMappingTempFile.Close()

					store.stringMappingTempFile = nil
				}

				_ = os.Remove(store.path + utils.PathSeparator + FileTemp176)

			} else {

				if store.stringMappingBackupFile != nil {

					_ = store.stringMappingBackupFile.Close()

					store.stringMappingBackupFile = nil

				}

				_ = os.Remove(store.path + utils.PathSeparator + FileBKP176)

				constructStringMappingBackup(store, encoder)

			}

			mappingLogger.Error(fmt.Sprintf("error %v occurred while rebuilding string mapping for store %v", err, store.name))

			stackTraceBytes := make([]byte, 1<<20)

			mappingLogger.Error(fmt.Sprintf("!!!STACK TRACE for store %v while rebuilding string mapping!!! \n %v", store.name, string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))

		}
	}()

	var bytes []byte

	var err error

	if recoveryMode == Partial {

		bytes, err = os.ReadFile(store.path + utils.PathSeparator + FileTemp176)

		if err != nil {

			mappingLogger.Error(fmt.Sprintf("failed to open the temp string mapping for the store %v, reason:%v", store.name, err.Error()))

			return errors.New(fmt.Sprintf("failed to open the temp string mapping for the store %v, reason:%v", store.name, err.Error()))

		}

	} else {

		bytes, err = os.ReadFile(store.path + utils.PathSeparator + FileBKP176)

		if err != nil {

			mappingLogger.Error(fmt.Sprintf("failed to open the bkp string mapping for the store %v, reason:%v", store.name, err.Error()))

			return errors.New(fmt.Sprintf("failed to open the bkp string mapping for the store %v, reason:%v", store.name, err.Error()))

		}
	}

	store.stringMappings = swiss.NewMap[string, int32](500)

	// initially if store is opened and variant is not updated as we are not doing patch, we need to read the TEMP file with old manner
	if store.variant < version2 {

		bufferBytes := bytes2.Split(bytes, utils.EOTBytes)

		// if last buffers has eot then we get last tokens as an empty so avoid it or if buffers not end with eot means corrupted record so skip that last record

		for i := 0; i < len(bufferBytes)-1; i++ {

			value, ordinal := readStringMappingItem(bufferBytes[i])

			if len(value) > 0 {

				store.stringMappings.Put(value, ordinal)

			} else {

				break
			}
		}

	} else {

		position := int32(0)

		for position < int32(len(bytes)) && len(bytes[position:]) > 4 {

			length := codec.ReadINT32Value(bytes[position : position+4])

			position += 4

			if int32(len(bytes[position:])) >= length+3 && bytes2.Equal(bytes[position+length:position+length+3], utils.EOTBytes) {

				value, ordinal := readStringMappingItem(bytes[position : position+length])

				if len(value) > 0 {

					store.stringMappings.Put(value, ordinal)

					position += length + 3

				} else {

					break

				}
			} else {

				break

			}
		}
	}

	if store.stringMappings.Count() == 0 {

		return errors.New("failed to recover, reason: recovery file is empty.")
	}

	mergeStringMapping(store, encoder, recoveryMode)

	return nil

}

func getStringMapping(value string, store *Store) (bool, int32, error) {

	if store.stringMappings != nil && store.stringMappings.Count() > 0 {

		ordinal, found := store.stringMappings.Get(value)

		if found {

			return found, ordinal, nil

		}
	}

	if store.stringMapping != nil {

		ordinal, found, err := store.stringMapping.Get([]byte(value))

		if err != nil {

			return found, -1, nil
		}

		return found, int32(ordinal), nil
	}

	return false, -1, nil

}

func existStringMapping(store *Store, value string) (bool, int32) {

	found := false

	ordinal := int32(-1)

	if store.stringMappings != nil && store.stringMappings.Count() > 0 {

		ordinal, found = store.stringMappings.Get(value)
	}

	if !found && store.stringMapping != nil {

		var tempValue uint64

		tempValue, found, _ = store.stringMapping.Get([]byte(value))

		if found {

			ordinal = int32(tempValue)
		}

	}

	return found, ordinal

}

func mapStringValues(store *Store, poolIndex int, encoder codec.Encoder, size int) (int, int, []int32, []int) {

	defer func() {

		if err := recover(); err != nil {

			stackTraceBytes := make([]byte, 1<<20)

			mappingLogger.Error(fmt.Sprintf("error %v occurred while mapping string values for store %v", err, store.name))

			mappingLogger.Error(fmt.Sprintf("!!!STACK TRACE for map string values !!! \n %v", string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))

		}
	}()

	values := encoder.MemoryPool.GetStringPool(poolIndex)

	if size != utils.NotAvailable {

		values = values[:size]
	}

	missingItemPoolIndex, missingItems := encoder.MemoryPool.AcquireINTPool(len(values))

	defer encoder.MemoryPool.ReleaseINTPool(missingItemPoolIndex)

	index := 0

	ordinalPoolIndex, ordinals := encoder.MemoryPool.AcquireINT32Pool(len(values))

	if store.stringMappings != nil && store.stringMappings.Count() > 0 {

		for i := range values {

			if values[i] != utils.Empty {

				if ordinal, ok := store.stringMappings.Get(values[i]); ok {

					ordinals[i] = ordinal

					size--
				} else {

					missingItems[index] = i

					index++
				}
			} else {

				ordinals[i] = DummyStringMappingOrdinal // reserved

				size--
			}

		}
	}

	if size > 0 {

		if store.stringMapping != nil && store.stringMapping.Len() > 0 {

			if size == len(values) {

				unmappedPoolIndex, unmappedItems := encoder.MemoryPool.AcquireINTPool(size)

				index = 0

				for i := range values {

					if values[i] != utils.Empty {

						if ordinal, found, _ := store.stringMapping.Get([]byte(values[i])); found {

							ordinals[i] = int32(ordinal)

							size--
						} else {

							unmappedItems[index] = i

							index++
						}
					} else {

						ordinals[i] = DummyStringMappingOrdinal

						size--
					}

				}

				if index > 0 {

					return ordinalPoolIndex, unmappedPoolIndex, ordinals, unmappedItems[:index]
				}

				encoder.MemoryPool.ReleaseINTPool(unmappedPoolIndex)

				return ordinalPoolIndex, -1, ordinals, nil

			} else {

				unmappedPoolIndex, unmappedItems := encoder.MemoryPool.AcquireINTPool(index)

				missingItems = missingItems[:index]

				index = 0

				for _, i := range missingItems {

					if ordinal, found, _ := store.stringMapping.Get([]byte(values[i])); found {

						ordinals[i] = int32(ordinal)

						size--
					} else {

						unmappedItems[index] = i

						index++
					}
				}

				if index > 0 {

					return ordinalPoolIndex, unmappedPoolIndex, ordinals, unmappedItems[:index]
				}

				encoder.MemoryPool.ReleaseINTPool(unmappedPoolIndex)

				return ordinalPoolIndex, -1, ordinals, nil

			}

		} else {

			unmappedPoolIndex, unmappedItems := encoder.MemoryPool.AcquireINTPool(size)

			missingItems = missingItems[:size]

			index = 0

			if size == len(values) {

				for i := range values {

					unmappedItems[i] = i
				}

				return ordinalPoolIndex, unmappedPoolIndex, ordinals, unmappedItems

			} else {

				for _, i := range missingItems {

					unmappedItems[index] = i

					index++
				}

				return ordinalPoolIndex, unmappedPoolIndex, ordinals, unmappedItems[:index]

			}

		}

	}

	return ordinalPoolIndex, -1, ordinals, nil

}

func doPrefixMapping(store *Store, conditionValue string, mappings *swiss.Map[string, int32]) {

	if store.stringMappings != nil && store.stringMappings.Count() > 0 {

		store.stringMappings.Iter(func(value string, ordinal int32) (stop bool) {

			if strings.HasPrefix(strings.ToLower(value), conditionValue) {

				mappings.Put(value, ordinal)
			}

			return stop
		})
	}

	if store.stringMapping != nil {

		query, _ := regexp.New("(?i)" + conditionValue + ".*")

		iterator, err := store.stringMapping.Search(query, nil, nil)

		for err == nil {

			bytes, ordinal := iterator.Current()

			mappings.Put(string(bytes), int32(ordinal))

			err = iterator.Next()
		}

	}
}

func doAllPrefixMapping(store *Store, values utils.MotadataMap, mappings *swiss.Map[string, int32], tokenizer *utils.Tokenizer, separator string) {

	if store.stringMappings != nil && store.stringMappings.Count() > 0 {

		store.stringMappings.Iter(func(value string, ordinal int32) (stop bool) {

			utils.Split(value, separator, tokenizer)

			if _, ok := values[tokenizer.Tokens[0]]; ok {

				mappings.Put(value, ordinal)
			}

			return stop
		})
	}

	if store.stringMapping != nil {

		iterator, err := store.stringMapping.Iterator(nil, nil)

		for err == nil {

			bytes, ordinal := iterator.Current()

			value := string(bytes)

			utils.Split(value, separator, tokenizer)

			if _, ok := values[tokenizer.Tokens[0]]; ok {

				mappings.Put(value, int32(ordinal))
			}

			err = iterator.Next()
		}

	}
}

func doStringEqualMapping(store *Store, conditionValue string, mappings *swiss.Map[string, int32]) {

	if store.stringMappings != nil && store.stringMappings.Count() > 0 {

		store.stringMappings.Iter(func(value string, ordinal int32) (stop bool) {

			if strings.EqualFold(value, conditionValue) {

				mappings.Put(value, ordinal)
			}

			return stop
		})
	}

	if store.stringMapping != nil {

		query, _ := regexp.New("(?i)" + conditionValue)

		iterator, err := store.stringMapping.Search(query, nil, nil)

		for err == nil {

			bytes, ordinal := iterator.Current()

			mappings.Put(string(bytes), int32(ordinal))

			err = iterator.Next()
		}

	}
}

func doSuffixMapping(store *Store, conditionValue string, mappings *swiss.Map[string, int32]) {

	if store.stringMappings != nil && store.stringMappings.Count() > 0 {

		store.stringMappings.Iter(func(value string, ordinal int32) (stop bool) {

			if strings.HasSuffix(strings.ToLower(value), conditionValue) {

				mappings.Put(value, ordinal)
			}

			return stop
		})
	}

	if store.stringMapping != nil {

		query, _ := regexp.New("(?i).*" + conditionValue)

		iterator, err := store.stringMapping.Search(query, nil, nil)

		for err == nil {

			bytes, ordinal := iterator.Current()

			mappings.Put(string(bytes), int32(ordinal))

			err = iterator.Next()
		}
	}
}

func doContainMapping(store *Store, conditionValue string, mappings *swiss.Map[string, int32]) {

	if store.stringMappings != nil && store.stringMappings.Count() > 0 {

		store.stringMappings.Iter(func(value string, ordinal int32) (stop bool) {

			if strings.Contains(strings.ToLower(value), conditionValue) {

				mappings.Put(value, ordinal)
			}

			return stop
		})
	}

	if store.stringMapping != nil {

		query, _ := regexp.New("(?i).*" + conditionValue + ".*")

		iterator, err := store.stringMapping.Search(query, nil, nil)

		for err == nil {

			bytes, ordinal := iterator.Current()

			mappings.Put(string(bytes), int32(ordinal))

			err = iterator.Next()
		}

	}
}

func repairStringMappings(store *Store, usedMappingOrdinals *bitmap.Bitmap, encoder codec.Encoder, maxStringMappingOrdinal uint64) error {

	if store.stringMapping != nil {

		tempBackupFile, err := os.Create(store.path + utils.PathSeparator + FileBKPTemp176)

		if err != nil {

			mappingLogger.Error(fmt.Sprintf(utils.ErrorCreateTempMappingBackup, store.name, err.Error()))

		}

		iterator, err := store.stringMapping.Iterator(nil, nil)

		if err != nil {

			return err
		}

		availableStringMappingOrdinals := &bitmap.Bitmap{}

		for err == nil {

			bytes, ordinal := iterator.Current()

			//ordinal >= maxStringMappingOrdinal means new ordinal is there

			if usedMappingOrdinals.Contains(uint32(ordinal)) || ordinal >= maxStringMappingOrdinal || (store.tempMappingOrdinals != nil && store.tempMappingOrdinals.Contains(uint32(ordinal))) {

				index, encodedBytes := writeStringMappingItem(string(bytes), int32(ordinal), encoder)

				if _, err = tempBackupFile.Write(encodedBytes); err != nil {

					mappingLogger.Error(fmt.Sprintf(utils.ErrorWriteStringMapping, err.Error()))

				}

				encoder.MemoryPool.ReleaseBytePool(index)
			} else {

				availableStringMappingOrdinals.Set(uint32(int32(ordinal)))
			}

			err = iterator.Next()
		}

		_ = tempBackupFile.Close()

		if store.stringMappingBackupFile != nil {

			_ = store.stringMappingBackupFile.Close()

		}

		if err = os.Rename(store.path+utils.PathSeparator+FileBKPTemp176, store.path+utils.PathSeparator+FileBKP176); err != nil {

			mappingLogger.Error(fmt.Sprintf(utils.ErrorRenameTempMappingBackup, store.name, err.Error()))
		}

		if store.stringMappingBackupFile, err = os.OpenFile(store.path+utils.PathSeparator+FileBKP176, os.O_RDWR|os.O_APPEND, 0666); err != nil {

			mappingLogger.Error(fmt.Sprintf("failed to open bkp.176 file store : %s , reason : %s", store.name, err.Error()))
		}

		if err = rebuildStringMapping(store, encoder, Full); err != nil {

			return err
		}

		//if there is no error anywhere than resetting availableStringMappingOrdinals

		availableStringMappingOrdinals.Range(func(ordinal uint32) {

			store.availableStringMappingOrdinals.Set(ordinal)
		})

		store.cacheStringMappings.Clear()

	}

	return nil
}

////////////////////////////////// Numeric Mapping //////////////////////////////////////////

func existNumericMapping(store *Store, value int64, encoder codec.Encoder) (bool, int32) {

	found := false

	ordinal := int32(-1)

	if store.numericMappings != nil && store.numericMappings.Len() > 0 {

		ordinal, found = store.numericMappings.Get(value)
	}

	if !found && store.numericMapping != nil {

		index, bytes := encoder.MemoryPool.AcquireBytePool(8)

		codec.WriteBigEndianINT64Value(value, 0, bytes)

		var tempValue uint64

		tempValue, found, _ = store.numericMapping.Get(bytes)

		encoder.MemoryPool.ReleaseBytePool(index)

		if found {

			ordinal = int32(tempValue)
		}
	}

	return found, ordinal

}

func writeNumericMapping(value int64, ordinal int32, store *Store, encoder codec.Encoder) error {

	var err error

	if store.numericMappingTempFile == nil {

		store.numericMappingTempFile, err = os.Create(store.path + utils.PathSeparator + FileTemp112)

		if err != nil {

			return errors.New(fmt.Sprintf("failed to create temp numeric mapping file for the store %v, reason:%v", store.name, err.Error()))

		}

		_ = store.numericMappingTempFile.Truncate(int64(12 * 4))

		store.numericMappingMemoryMappedBytes, err = Map(store.numericMappingTempFile, ReadWrite)

		if err != nil {

			_ = store.numericMappingTempFile.Close()

			_ = os.Remove(store.path + utils.PathSeparator + FileTemp112)

			if store.numericMappingMemoryMappedBytes != nil {

				_ = store.numericMappingMemoryMappedBytes.unmap()
			}

			mappingLogger.Error(fmt.Sprintf("failed to create numeric mapping for the store %v, reason:%v", store.name, err.Error()))

			return errors.New(fmt.Sprintf("failed to create numeric mapping for the store %v, reason:%v", store.name, err.Error()))

		}

		store.numericMappings = intmap.New[int64, int32](5000)
	} else {

		if len(store.numericMappingMemoryMappedBytes) < store.numericMappingSize+12 {

			store.numericMappingMemoryMappedBytes, err = Remap(store.numericMappingMemoryMappedBytes, (len(store.numericMappingMemoryMappedBytes)+12)*4, store.numericMappingTempFile)

			if err != nil {

				_ = store.numericMappingTempFile.Close()

				if store.numericMappingMemoryMappedBytes != nil {

					_ = store.numericMappingMemoryMappedBytes.unmap()
				}

				mappingLogger.Error(fmt.Sprintf("failed to extend the numeric mappping for the store %v, reason:%v", store.name, err.Error()))

				return errors.New(fmt.Sprintf("failed to extend the numeric mappping for the store %v, reason:%v", store.name, err.Error()))

			}
		}
	}

	index, encodedBytes := writeNumericMappingItem(value, ordinal, encoder)

	if _, err = store.numericMappingBackupFile.Write(encodedBytes); err != nil {

		mappingLogger.Error(fmt.Sprintf(utils.ErrorWriteNumericMapping, err.Error()))
	}

	copy(store.numericMappingMemoryMappedBytes[store.numericMappingSize:], encodedBytes)

	store.numericMappingSize += len(encodedBytes)

	store.numericMappings.Put(value, ordinal)

	encoder.MemoryPool.ReleaseBytePool(index)

	if store.cacheNumericMappings.Len()+1 <= store.maxMappingCacheRecords {

		store.cacheNumericMappings.Put(ordinal, value)

	}

	return err
}

func writeNumericMappingItem(value int64, ordinal int32, encoder codec.Encoder) (int, []byte) {

	index, bytes := encoder.MemoryPool.AcquireBytePool(8 + 4 + 3)

	codec.WriteINT64Value(value, 0, bytes)

	codec.WriteINT32Value(ordinal, 8, bytes)

	copy(bytes[4+8:], utils.EOTBytes)

	return index, bytes

}

func mergeNumericMapping(store *Store, encoder codec.Encoder, recoveryMode MappingRecoveryMode) {

	defer func() {

		if err := recover(); err != nil {

			mappingLogger.Error(fmt.Sprintf("error %v occurred while merge numeric mapping for store %v", err, store.name))
		}
	}()

	if store.numericMappings != nil && store.numericMappings.Len() > 0 {

		if recoveryMode == Normal && utils.EnvironmentType != utils.DatastoreBenchUnitEnvironment {

			err := store.numericMappingMemoryMappedBytes.unmap()

			if err != nil {

				mappingLogger.Fatal(fmt.Sprintf("failed to unmap numeric mapping memory mapped file for store %v, reason : %v", store.name, err.Error()))
			}

			_ = store.numericMappingTempFile.Close()

			store.numericMappingMemoryMappedBytes = nil

			store.numericMappingTempFile = nil

			store.numericMappingSize = 0
		}

		var buffer bytes2.Buffer

		tempMappingWriter, err := vellum.New(&buffer, nil)

		if err != nil {

			mappingLogger.Error(fmt.Sprintf("failed to create the temp numeric mapping writer for the store %v, reason:%v", store.name, err.Error()))

			return
		}

		poolIndex := utils.NotAvailable

		var values []int64

		if store.numericMappings.Len() > encoder.MemoryPool.GetPoolLength() {

			values = make([]int64, store.numericMappings.Len(), store.numericMappings.Len())

		} else {

			poolIndex, values = encoder.MemoryPool.AcquireINT64Pool(store.numericMappings.Len())
		}

		i := 0

		for value := range store.numericMappings.Keys() {

			values[i] = value

			i++
		}

		utils.SortINT64Values(values)

		index, valueBytes := encoder.MemoryPool.AcquireBytePool(8)

		defer encoder.MemoryPool.ReleaseBytePool(index)

		for j := range values {

			codec.WriteBigEndianINT64Value(values[j], 0, valueBytes)

			value, _ := store.numericMappings.Get(values[j])

			_ = tempMappingWriter.Insert(valueBytes, uint64(value))
		}

		if poolIndex != utils.NotAvailable {

			encoder.MemoryPool.ReleaseINT64Pool(poolIndex)
		}

		_ = tempMappingWriter.Close()

		if utils.EnvironmentType != utils.DatastoreBenchUnitEnvironment {

			store.numericMappings = nil
		}

		if recoveryMode != Full && store.numericMapping != nil && store.numericMapping.Len() > 0 {

			iterators := make([]vellum.Iterator, 2)

			iterator, _ := store.numericMapping.Iterator(nil, nil)

			iterators[0] = iterator

			tempMapping, err := vellum.Load(buffer.Bytes())

			if err != nil {

				mappingLogger.Error(fmt.Sprintf("failed to load temp numeric mapping of the store %v, reason:%v", store.name, err.Error()))

				return
			}

			iterator, _ = tempMapping.Iterator(nil, nil)

			iterators[1] = iterator

			file, err := os.Create(store.path + utils.PathSeparator + FileMerged112)

			if err != nil {

				mappingLogger.Error(fmt.Sprintf("failed to create the merged numeric mapping file for the store %v, reason:%v", store.name, err.Error()))

				_ = tempMapping.Close()

				return
			}

			err = vellum.Merge(file, nil, iterators, func(values []uint64) uint64 {

				return values[0]
			})

			if err != nil {

				mappingLogger.Error(fmt.Sprintf(utils.ErrorMergeNumericMapping, store.name, err.Error()))

				_ = file.Close()

				_ = os.Remove(store.path + utils.PathSeparator + FileMerged112)

				_ = tempMapping.Close()

				return
			}

			_ = file.Close()

			_ = tempMapping.Close()

			_ = store.numericMapping.Close()

			err = os.Rename(store.path+utils.PathSeparator+FileMerged112,
				store.path+utils.PathSeparator+FileMapping112)

			if err != nil {

				err = os.Remove(store.path + utils.PathSeparator + FileMerged112)

				mappingLogger.Error(fmt.Sprintf("failed to rename the merged numeric mapping of the store %v, reason:%v", store.name, err.Error()))

				return
			}

		} else {

			//closed previously opened file if any
			if store.numericMapping != nil {

				_ = store.numericMapping.Close()

			}

			err = os.WriteFile(store.path+utils.PathSeparator+FileMapping112, buffer.Bytes(), 0666)

			if err != nil {

				mappingLogger.Error(fmt.Sprintf("failed to write the numeric mapping of the store %v, reason:%v", store.name, err.Error()))

				return
			}
		}

		store.numericMapping, err = vellum.Open(store.path + utils.PathSeparator + FileMapping112)

		if err != nil {

			mappingLogger.Error(fmt.Sprintf("failed to open the numeric mapping of the store %v, reason:%v", store.name, err.Error()))

			return

		}

		if utils.EnvironmentType != utils.DatastoreBenchUnitEnvironment {

			_ = os.Remove(store.path + utils.PathSeparator + FileTemp112)
		}

	}

}

func readNumericMappingItem(bytes []byte) (int64, int32) {

	return codec.ReadINT64Value(bytes[:8]), codec.ReadINT32Value(bytes[8:12])

}

func rebuildNumericMapping(store *Store, encoder codec.Encoder, recoveryMode MappingRecoveryMode) error {

	defer func() {

		if err := recover(); err != nil {

			if recoveryMode == Partial {

				if store.numericMappingTempFile != nil {

					_ = store.numericMappingTempFile.Close()

					store.numericMappingTempFile = nil
				}

				_ = os.Remove(store.path + utils.PathSeparator + FileTemp112)

			} else {

				if store.numericMappingBackupFile != nil {

					_ = store.numericMappingBackupFile.Close()

					store.numericMappingBackupFile = nil

				}

				_ = os.Remove(store.path + utils.PathSeparator + FileBKP112)

				constructNumericMappingBackup(store, encoder)

			}

			mappingLogger.Warn(fmt.Sprintf("error %v occurred while rebuilding numeric mapping for store %v", err, store.name))

			stackTraceBytes := make([]byte, 1<<20)

			mappingLogger.Error(fmt.Sprintf("!!!STACK TRACE for store %v while rebuilding numeric mapping!!! \n %v", store.name, string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))

		}
	}()

	var bytes []byte

	var err error

	if recoveryMode == Partial {

		bytes, err = os.ReadFile(store.path + utils.PathSeparator + FileTemp112)

		if err != nil {

			mappingLogger.Error(fmt.Sprintf("failed to open the temp numeric mapping for the store %v, reason:%v", store.name, err.Error()))

			return errors.New(fmt.Sprintf("failed to open the temp numeric mapping for the store %v, reason:%v", store.name, err.Error()))

		}
	} else {

		bytes, err = os.ReadFile(store.path + utils.PathSeparator + FileBKP112)

		if err != nil {

			mappingLogger.Error(fmt.Sprintf("failed to open the bkp numeric mapping for the store %v, reason:%v", store.name, err.Error()))

			return errors.New(fmt.Sprintf("failed to open the bkp numeric mapping for the store %v, reason:%v", store.name, err.Error()))

		}
	}

	store.numericMappings = intmap.New[int64, int32](5000)

	//We have not changed writing format in case of numeric mappings so no version check is needed
	//for numeric mappings at every 12 bytes there should be EOT bytes if it is not there than record is corrupted

	position := int32(0)

	for position < int32(len(bytes)) {

		if int32(len(bytes[position:])) >= 12+3 && bytes2.Equal(bytes[position+12:position+12+3], utils.EOTBytes) {

			value, ordinal := readNumericMappingItem(bytes[position : position+12])

			store.numericMappings.Put(value, ordinal)

			position += 12 + 3

		} else {

			break
		}

	}

	if store.numericMappings.Len() == 0 {

		return errors.New("failed to recover, reason: recovery file is empty.")
	}

	mergeNumericMapping(store, encoder, recoveryMode)

	return nil

}

func getNumericMapping(value int64, store *Store, encoder codec.Encoder) (bool, int32, error) {

	if store.numericMappings != nil && store.numericMappings.Len() > 0 {

		ordinal, found := store.numericMappings.Get(value)

		if found {

			return found, ordinal, nil

		}
	}

	if store.numericMapping != nil {

		index, bytes := encoder.MemoryPool.AcquireBytePool(8)

		codec.WriteBigEndianINT64Value(value, 0, bytes)

		ordinal, found, err := store.numericMapping.Get(bytes)

		encoder.MemoryPool.ReleaseBytePool(index)

		if err != nil {

			return found, -1, err
		}

		return found, int32(ordinal), nil
	}

	return false, -1, nil

}

func mapNumericValues(store *Store, poolIndex int, encoder codec.Encoder, size int) (int, int, []int32, []int) {

	defer func() {

		if err := recover(); err != nil {

			stackTraceBytes := make([]byte, 1<<20)

			mappingLogger.Error(fmt.Sprintf("error %v occurred while mapping numeric values for store %v", err, store.name))

			mappingLogger.Error(fmt.Sprintf("!!!STACK TRACE for map numeric values !!! \n %v", string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))
		}
	}()

	values := encoder.MemoryPool.GetINT64Pool(poolIndex)

	values = values[:size]

	missingPoolIndex, missingItems := encoder.MemoryPool.AcquireINTPool(len(values))

	defer encoder.MemoryPool.ReleaseINTPool(missingPoolIndex)

	index := 0

	ordinalPoolIndex, ordinals := encoder.MemoryPool.AcquireINT32Pool(len(values))

	if store.numericMappings != nil && store.numericMappings.Len() > 0 {

		for i := range values {

			if ordinal, ok := store.numericMappings.Get(values[i]); ok {

				ordinals[i] = ordinal

				size--
			} else {

				missingItems[index] = i

				index++
			}
		}
	}

	if size > 0 {

		if store.numericMapping != nil && store.numericMapping.Len() > 0 {

			if size == len(values) {

				unmappedPoolIndex, unmappedItems := encoder.MemoryPool.AcquireINTPool(size)

				index = 0

				bufferPoolIndex, bytes := encoder.MemoryPool.AcquireBytePool(8)

				defer encoder.MemoryPool.ReleaseBytePool(bufferPoolIndex)

				for i := range values {

					codec.WriteBigEndianINT64Value(values[i], 0, bytes)

					if ordinal, found, _ := store.numericMapping.Get(bytes); found {

						ordinals[i] = int32(ordinal)

						size--
					} else {

						unmappedItems[index] = i

						index++
					}
				}

				if index > 0 {

					return ordinalPoolIndex, unmappedPoolIndex, ordinals, unmappedItems[:index]
				}

				encoder.MemoryPool.ReleaseINTPool(unmappedPoolIndex)

				return ordinalPoolIndex, -1, ordinals, nil

			} else {

				unmappedPoolIndex, unmappedItems := encoder.MemoryPool.AcquireINTPool(index)

				missingItems = missingItems[:index]

				index = 0

				bufferPoolIndex, bytes := encoder.MemoryPool.AcquireBytePool(8)

				defer encoder.MemoryPool.ReleaseBytePool(bufferPoolIndex)

				for _, i := range missingItems {

					codec.WriteBigEndianINT64Value(values[i], 0, bytes)

					if ordinal, found, _ := store.numericMapping.Get(bytes); found {

						ordinals[i] = int32(ordinal)

						size--
					} else {

						unmappedItems[index] = i

						index++
					}
				}

				if index > 0 {

					return ordinalPoolIndex, unmappedPoolIndex, ordinals, unmappedItems[:index]
				}

				encoder.MemoryPool.ReleaseINTPool(unmappedPoolIndex)

				return ordinalPoolIndex, -1, ordinals, nil
			}
		} else {

			unmappedPoolIndex, unmappedItems := encoder.MemoryPool.AcquireINTPool(size)

			missingItems = missingItems[:size]

			index = 0

			if size == len(values) {

				for i := range values {

					unmappedItems[i] = i
				}

				return ordinalPoolIndex, unmappedPoolIndex, ordinals, unmappedItems

			} else {

				for _, i := range missingItems {

					unmappedItems[index] = i

					index++
				}

				return ordinalPoolIndex, unmappedPoolIndex, ordinals, unmappedItems[:index]

			}

		}

	}

	return ordinalPoolIndex, -1, ordinals, nil

}

func repairNumericMappings(store *Store, usedMappingOrdinals *bitmap.Bitmap, encoder codec.Encoder, maxNumericMappingsOrdinal uint64) error {

	if store.numericMapping != nil {

		tempBackupFile, err := os.Create(store.path + utils.PathSeparator + FileBKPTemp112)

		if err != nil {

			mappingLogger.Error(fmt.Sprintf(utils.ErrorCreateTempMappingBackup, store.name, err.Error()))

		}

		iterator, err := store.numericMapping.Iterator(nil, nil)

		if err != nil {

			return err
		}

		availableNumericMappingOrdinals := &bitmap.Bitmap{}

		for err == nil {

			bytes, ordinal := iterator.Current()

			if usedMappingOrdinals.Contains(uint32(ordinal)) || ordinal >= maxNumericMappingsOrdinal || (store.tempMappingOrdinals != nil && store.tempMappingOrdinals.Contains(uint32(ordinal))) {

				index, encodedBytes := writeNumericMappingItem(codec.ReadBigEndianINT64Value(bytes), int32(ordinal), encoder)

				if _, err = tempBackupFile.Write(encodedBytes); err != nil {

					mappingLogger.Error(fmt.Sprintf(utils.ErrorWriteNumericMapping, err.Error()))
				}

				encoder.MemoryPool.ReleaseBytePool(index)
			} else {

				availableNumericMappingOrdinals.Set(uint32(ordinal))
			}

			err = iterator.Next()
		}

		_ = tempBackupFile.Close()

		if store.numericMappingBackupFile != nil {

			_ = store.numericMappingBackupFile.Close()

		}

		if err = os.Rename(store.path+utils.PathSeparator+FileBKPTemp112, store.path+utils.PathSeparator+FileBKP112); err != nil {

			mappingLogger.Error(fmt.Sprintf(utils.ErrorRenameTempMappingBackup, store.name, err.Error()))
		}

		if store.numericMappingBackupFile, err = os.OpenFile(store.path+utils.PathSeparator+FileBKP112, os.O_RDWR|os.O_APPEND, 0666); err != nil {

			mappingLogger.Error(fmt.Sprintf("failed to open bkp.112 file store : %s , reason : %s", store.name, err.Error()))
		}

		if err = rebuildNumericMapping(store, encoder, Full); err != nil {

			return err
		}

		availableNumericMappingOrdinals.Range(func(ordinal uint32) {

			store.availableNumericMappingOrdinals.Set(ordinal)

		})

		store.cacheNumericMappings.Clear()

	}

	return nil
}

func listStringMappings(store *Store, stringMappings utils.MotadataMap) {

	if store.stringMappings != nil {

		store.stringMappings.Iter(func(key string, _ int32) (stop bool) {

			stringMappings[key] = struct{}{}

			return stop
		})
	}

	if store.stringMapping != nil {

		iterator, err := store.stringMapping.Iterator(nil, nil)

		for err == nil {

			bytes, _ := iterator.Current()

			stringMappings[string(bytes)] = struct{}{}

			err = iterator.Next()
		}
	}
}

func doGreaterThanMapping(store *Store, conditionValue int64, inclusive bool, mappings *swiss.Map[int64, int32], encoder codec.Encoder) {

	if store.numericMappings != nil && store.numericMappings.Len() > 0 {

		if inclusive {

			for value, ordinal := range store.numericMappings.All() {

				if value >= conditionValue {

					mappings.Put(value, ordinal)

				}
			}
		} else {

			for value, ordinal := range store.numericMappings.All() {

				if value > conditionValue {

					mappings.Put(value, ordinal)

				}
			}
		}
	}

	if store.numericMapping != nil {

		poolIndex, bytes := encoder.MemoryPool.AcquireBytePool(8)

		defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

		if inclusive {

			codec.WriteBigEndianINT64Value(conditionValue, 0, bytes)

			iterator, err := store.numericMapping.Iterator(bytes, nil)

			for err == nil {

				bytes, ordinal := iterator.Current()

				mappings.Put(codec.ReadBigEndianINT64Value(bytes), int32(ordinal))

				err = iterator.Next()
			}

		} else if !inclusive {

			codec.WriteBigEndianINT64Value(conditionValue+1, 0, bytes)

			iterator, err := store.numericMapping.Iterator(bytes, nil)

			for err == nil {

				bytes, ordinal := iterator.Current()

				mappings.Put(codec.ReadBigEndianINT64Value(bytes), int32(ordinal))

				err = iterator.Next()
			}
		}

	}
}

func doLessThanMapping(store *Store, conditionValue int64, inclusive bool, mappings *swiss.Map[int64, int32], encoder codec.Encoder) {

	if store.numericMappings != nil && store.numericMappings.Len() > 0 {

		if inclusive {

			for value, ordinal := range store.numericMappings.All() {

				if value <= conditionValue {

					mappings.Put(value, ordinal)

				}
			}

		} else {

			for value, ordinal := range store.numericMappings.All() {

				if value < conditionValue {

					mappings.Put(value, ordinal)

				}
			}
		}
	}

	if store.numericMapping != nil {

		poolIndex, bytes := encoder.MemoryPool.AcquireBytePool(8)

		defer encoder.MemoryPool.ReleaseBytePool(poolIndex)

		if inclusive {

			codec.WriteBigEndianINT64Value(conditionValue+1, 0, bytes)

			iterator, err := store.numericMapping.Iterator(nil, bytes)

			for err == nil {

				bytes, ordinal := iterator.Current()

				mappings.Put(codec.ReadBigEndianINT64Value(bytes), int32(ordinal))

				err = iterator.Next()
			}

		} else if !inclusive {

			codec.WriteBigEndianINT64Value(conditionValue, 0, bytes)

			iterator, err := store.numericMapping.Iterator(nil, bytes)

			for err == nil {

				bytes, ordinal := iterator.Current()

				mappings.Put(codec.ReadBigEndianINT64Value(bytes), int32(ordinal))

				err = iterator.Next()
			}
		}

	}
}

func updateCacheNumericMapping(store *Store) error {

	if store.numericMapping != nil {

		store.cacheNumericMappings.Clear()

		iterator, err := store.numericMapping.Iterator(nil, nil)

		if err != nil {

			return err
		}

		for err == nil {

			key, ordinal := iterator.Current()

			if store.cacheNumericMappings.Len()+1 <= store.maxMappingCacheRecords {

				store.cacheNumericMappings.Put(int32(ordinal), codec.ReadBigEndianINT64Value(key))
			}

			err = iterator.Next()
		}

	}

	return nil
}

func updateCacheStringMapping(store *Store) error {

	if store.stringMapping != nil {

		store.cacheStringMappings.Clear()

		iterator, err := store.stringMapping.Iterator(nil, nil)

		if err != nil {

			return err
		}

		for err == nil {

			key, ordinal := iterator.Current()

			if store.cacheStringMappings.Count()+1 <= store.maxMappingCacheRecords {

				store.cacheStringMappings.Put(int32(ordinal), string(key))
			}

			err = iterator.Next()
		}

	}

	return nil
}

func constructStringMappingBackup(store *Store, encoder codec.Encoder) {

	defer func() {

		if r := recover(); r != nil {

			stackTraceBytes := make([]byte, 1<<20)

			mappingLogger.Error(fmt.Sprintf("error %v occurred while rebuilding string mapping for store %v", r, store.name))

			mappingLogger.Error(fmt.Sprintf("!!!STACK TRACE for store %v while rebuilding string mapping !!! \n %v", store.name, string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))
		}

	}()

	if store.stringMapping != nil {

		if err := os.RemoveAll(store.path + utils.PathSeparator + FileBKPTemp176); err != nil {

			mappingLogger.Error(fmt.Sprintf("failed to remove the temp mapping backup file store : %s , reason : %s", store.name, err.Error()))

		}

		tempBackupFile, err := os.Create(store.path + utils.PathSeparator + FileBKPTemp176)

		if err != nil {

			mappingLogger.Error(fmt.Sprintf(utils.ErrorCreateTempMappingBackup, store.name, err.Error()))

		}

		iterator, err := store.stringMapping.Iterator(nil, nil)

		if err != nil {

			mappingLogger.Error(fmt.Sprintf("failed to create iterator of FST for store %v while building backup file , reason : %s", store.name, err.Error()))
		}

		for err == nil {

			bytes, ordinal := iterator.Current()

			index, encodedBytes := writeStringMappingItem(string(bytes), int32(ordinal), encoder)

			if _, err = tempBackupFile.Write(encodedBytes); err != nil {

				mappingLogger.Error(fmt.Sprintf(utils.ErrorWriteStringMapping, err.Error()))

			}

			encoder.MemoryPool.ReleaseBytePool(index)

			err = iterator.Next()
		}

		_ = tempBackupFile.Close()

		if store.stringMappingBackupFile != nil {

			_ = store.stringMappingBackupFile.Close()

		}

		if err = os.Rename(store.path+utils.PathSeparator+FileBKPTemp176, store.path+utils.PathSeparator+FileBKP176); err != nil {

			mappingLogger.Error(fmt.Sprintf(utils.ErrorRenameTempMappingBackup, store.name, err.Error()))
		}

		if store.stringMappingBackupFile, err = os.OpenFile(store.path+utils.PathSeparator+FileBKP176, os.O_RDWR|os.O_APPEND, 0666); err != nil {

			mappingLogger.Error(fmt.Sprintf("failed to open bkp.176 file store : %s , reason : %s", store.name, err.Error()))
		}

		store.cacheStringMappings.Clear()
	}
}

func constructNumericMappingBackup(store *Store, encoder codec.Encoder) {

	defer func() {

		if r := recover(); r != nil {

			stackTraceBytes := make([]byte, 1<<20)

			mappingLogger.Error(fmt.Sprintf("error %v occurred while rebuilding numeric mapping for store %v", r, store.name))

			mappingLogger.Error(fmt.Sprintf("!!!STACK TRACE for store %v while rebuilding numeric mapping !!! \n %v", store.name, string(stackTraceBytes[:runtime.Stack(stackTraceBytes, false)])))
		}

	}()

	if store.numericMapping != nil {

		if err := os.RemoveAll(store.path + utils.PathSeparator + FileBKPTemp112); err != nil {

			mappingLogger.Error(fmt.Sprintf("failed to remove the temp mapping backup file store : %s , reason : %s", store.name, err.Error()))

		}

		tempBackupFile, err := os.Create(store.path + utils.PathSeparator + FileBKPTemp112)

		if err != nil {

			mappingLogger.Error(fmt.Sprintf(utils.ErrorCreateTempMappingBackup, store.name, err.Error()))

		}

		iterator, err := store.numericMapping.Iterator(nil, nil)

		if err != nil {

			mappingLogger.Error(fmt.Sprintf("failed to create iterator of FST for store %v while building backup file , reason : %s", store.name, err.Error()))
		}

		for err == nil {

			bytes, ordinal := iterator.Current()

			index, encodedBytes := writeNumericMappingItem(codec.ReadBigEndianINT64Value(bytes), int32(ordinal), encoder)

			if _, err = tempBackupFile.Write(encodedBytes); err != nil {

				mappingLogger.Error(fmt.Sprintf(utils.ErrorWriteNumericMapping, err.Error()))
			}

			encoder.MemoryPool.ReleaseBytePool(index)

			err = iterator.Next()
		}

		_ = tempBackupFile.Close()

		if store.numericMappingBackupFile != nil {

			_ = store.numericMappingBackupFile.Close()

		}

		if err = os.Rename(store.path+utils.PathSeparator+FileBKPTemp112, store.path+utils.PathSeparator+FileBKP112); err != nil {

			mappingLogger.Error(fmt.Sprintf(utils.ErrorRenameTempMappingBackup, store.name, err.Error()))
		}

		if store.numericMappingBackupFile, err = os.OpenFile(store.path+utils.PathSeparator+FileBKP112, os.O_RDWR|os.O_APPEND, 0666); err != nil {

			mappingLogger.Error(fmt.Sprintf("failed to open bkp.112 file store : %s , reason : %s", store.name, err.Error()))
		}

		store.cacheNumericMappings.Clear()

	}
}
