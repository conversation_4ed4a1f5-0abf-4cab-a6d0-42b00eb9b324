/*
 *   Copyright (c) Motadata 2025.  All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 */

package utils

import (
	"archive/zip"
	"encoding/json"
	"github.com/klauspost/compress/flate"
	"github.com/stretchr/testify/assert"
	"io"
	"os"
	"path/filepath"
	"testing"
)

func TestReconstructLogProfile(t *testing.T) {

	CleanUpStores()

	os.RemoveAll(CurrentDir + PathSeparator + reconstructDirectory + PathSeparator)

	assertions := assert.New(t)

	_ = os.Mkdir(CurrentDir+PathSeparator+DatastoreDir, 0777)

	err := CloneDirectory(filepath.Dir(filepath.Dir(filepath.Dir(CurrentDir)))+PathSeparator+"test-patch-files"+PathSeparator+"REPORT-DB-BACKUP-LOCAL-RECONSTRUCT", CurrentDir+PathSeparator+"REPORT-DB-BACKUP-LOCAL-RECONSTRUCT")

	if err != nil {

		panic(err)
	}
	Reconstruct("log")

	entries, err := os.ReadDir(tempBkpDirectory)

	assertions.Nil(err)

	assertions.Equal(len(entries), 1)

	entries, err = os.ReadDir(CurrentDir + PathSeparator + DatastoreDir)

	assertions.Nil(err)

	assertions.Equal(len(entries), 1)

}

func TestReconstructMetricProfileUnzipError(t *testing.T) {

	assertions := assert.New(t)

	_ = os.Mkdir(CurrentDir+PathSeparator+DatastoreDir, 0777)

	_ = os.Mkdir(CurrentDir+PathSeparator+reconstructDirectory, 0777)

	_ = os.Mkdir(CurrentDir+PathSeparator+reconstructDirectory+PathSeparator+"event.source-mappings", 0777)

	metadata := MotadataMap{

		"datastore.type": float64(PerformanceMetric),
	}

	bytes, err := json.MarshalIndent(metadata, "", "")

	assertions.Nil(err)

	err = os.WriteFile(CurrentDir+PathSeparator+reconstructDirectory+PathSeparator+"event.source-mappings"+PathSeparator+MetadataFile, bytes, 0777)

	file, err := os.Create(CurrentDir + PathSeparator + reconstructDirectory + PathSeparator + "event.source-mappings" + ZipExtension)

	writer := zip.NewWriter(file)

	writer.RegisterCompressor(zip.Deflate, func(out io.Writer) (io.WriteCloser, error) {
		return flate.NewWriter(out, flate.BestSpeed)
	})

	_ = writer.Close()

	_ = file.Close()

	assertions.Nil(err)

	err = Zip(writer, CurrentDir+PathSeparator+reconstructDirectory+PathSeparator+"event.source-mappings")

	assertions.Nil(err)

	Reconstruct("metric")

	AssertLogMessage(assertions, "Reconstruct", "utils", "failed to unzip the store")

}

func TestReconstructEmptyProfile(t *testing.T) {

	assertions := assert.New(t)

	Reconstruct(Empty)

	AssertLogMessage(assertions, "Reconstruct", "utils", "unknown backup profile provided in the input")

	assertions.True(qualifyStore("event.source-mappings", "metric"))

}
