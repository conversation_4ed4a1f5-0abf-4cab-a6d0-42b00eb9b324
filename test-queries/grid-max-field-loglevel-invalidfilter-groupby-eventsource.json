{"visualization.timeline": {"relative.timeline": "today", "visualization.timezone": "Asia/Calcutta", "from.datetime": 1659119400198, "to.datetime": 1659205799198, "duration": 86399}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.properties": {"gauge": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": []}}, "granularity": {"value": 5, "unit": "m"}, "id": -1, "ui.event.uuid": "8d887954-eeab-476e-9093-ac13982f477d", "session-id": "8ad73c55-9561-4f08-aef7-ca53f86ba675", "user.name": "admin", "query.id": 91198171542, "visualization.data.sources": {"type": "log", "filters": {"data.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "or", "conditions": [{"operand": "system.os", "operator": "=", "value": "linux"}]}, {"filter": "include", "operator": "or", "conditions": [{"operand": "system.os", "operator": "=", "value": "linux"}]}, {"filter": "include", "operator": "or", "conditions": [{"operand": "system.os", "operator": "=", "value": "linux"}]}, {"filter": "include", "operator": "or", "conditions": [{"operand": "system.os", "operator": "=", "value": "linux"}]}, {"filter": "include", "operator": "or", "conditions": [{"operand": "system.os", "operator": "=", "value": "linux"}]}, {"filter": "include", "operator": "or", "conditions": [{"operand": "system.os", "operator": "=", "value": "linux"}]}, {"filter": "include", "operator": "or", "conditions": [{"operand": "system.os", "operator": "=", "value": "linux"}]}, {"filter": "include", "operator": "or", "conditions": [{"operand": "system.os", "operator": "=", "value": "linux"}]}, {"filter": "include", "operator": "or", "conditions": [{"operand": "system.os", "operator": "=", "value": "linux"}]}, {"filter": "include", "operator": "or", "conditions": [{"operand": "system.os", "operator": "=", "value": "linux"}]}, {"filter": "include", "operator": "or", "conditions": [{"operand": "system.os", "operator": "=", "value": "linux"}]}, {"filter": "include", "operator": "or", "conditions": [{"operand": "system.os", "operator": "=", "value": "linux"}]}, {"filter": "include", "operator": "or", "conditions": [{"operand": "system.os", "operator": "=", "value": "linux"}]}]}, "result.filter": {}}, "visualization.result.by": ["event.source"], "data.points": [{"data.point": "log.level", "aggregator": "max", "entity.type": "event.source", "entities": {"10.20.40.140": "50909-windows.login.audit", "10.20.40.141": "50909-windows.login.audit", "10.20.40.142": "50909-windows.login.audit", "10.20.40.143": "50909-windows.login.audit"}, "plugins": ["50909-windows.login.audit"]}], "plugins": ["50909-windows.login.audit"], "entities": {"10.20.40.140": "50909-windows.login.audit", "10.20.40.141": "50909-windows.login.audit", "10.20.40.142": "50909-windows.login.audit", "10.20.40.143": "50909-windows.login.audit"}}, "admin.role": "yes", "sub.query.id": 91198171543}