{"visualization.timeline": {"relative.timeline": "today", "visualization.timezone": "Asia/Calcutta", "from.datetime": 1672790400000, "to.datetime": 1672790699000, "duration": 300, "visualization.time.range.inclusive": "yes"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": []}}, "visualization.result.by": ["event.source"], "granularity": {"value": 5, "unit": "m"}, "id": -1, "ui.event.uuid": "95701cf5-a859-49c1-9ee6-caaf6a7119e0", "session-id": "1bbdc8e0-7812-4d14-a555-422e5a8e86e4", "user.name": "admin", "visualization.data.sources": {"type": "health", "filters": {"data.filter": {"filter": "include", "groups": [{"filter": "include", "conditions": [{"operand": "engine.type", "operator": "=", "value": "discovery"}], "operator": "and"}], "operator": "and"}, "result.filter": {}}, "visualization.result.by": ["event.source"], "data.points": [{"data.point": "pending.events", "aggregator": "sum", "entities": {"10.20.40.140": "500014-health.metric", "10.20.40.141": "500014-health.metric", "10.20.40.142": "500014-health.metric", "10.20.40.143": "500014-health.metric", "10.20.40.144": "500014-health.metric"}, "entity.type": "all", "plugins": ["500014-health.metric"]}, {"data.point": "total.events", "aggregator": "sum", "entities": {"10.20.40.140": "500014-health.metric", "10.20.40.141": "500014-health.metric", "10.20.40.142": "500014-health.metric", "10.20.40.143": "500014-health.metric", "10.20.40.144": "500014-health.metric"}, "entity.type": "all", "plugins": ["500014-health.metric"]}], "entities": {"10.20.40.140": "500014-health.metric", "10.20.40.141": "500014-health.metric", "10.20.40.142": "500014-health.metric", "10.20.40.143": "500014-health.metric", "10.20.40.144": "500014-health.metric"}, "plugins": ["500014-health.metric"]}, "admin.role": "yes", "query.id": 157047436842476, "sub.query.id": 157047436842477}