{"visualization.granularity": "5 m", "visualization.timeline": {"relative.timeline": "today", "visualization.timezone": "Asia/Calcutta", "from.datetime": 1659810600118, "to.datetime": 1659896999018, "duration": 86399}, "visualization.category": "Chart", "visualization.type": "Area", "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "no", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc"}}}, "granularity": {"value": 5, "unit": "m"}, "visualization.result.by": ["monitor"], "id": -1, "ui.event.uuid": "d4fff2ab-c040-470a-a47a-fab1ca554a40", "session-id": "8e97a837-a679-4bc0-aa22-bf5abebc4b91", "user.name": "admin", "visualization.data.sources": {"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "visualization.result.by": ["monitor"], "data.points": [{"data.point": "dummy.int8Float.column", "aggregator": "avg", "entity.type": "Monitor", "entities": {"1": "9990-int8-float", "2": "9990-int8-float", "3": "9990-int8-float", "4": "9990-int8-float", "5": "9990-int8-float", "6": "9990-int8-float", "7": "9990-int8-float", "8": "9990-int8-float", "9": "9990-int8-float", "10": "9990-int8-float", "11": "9990-int8-float", "12": "9990-int8-float", "13": "9990-int8-float", "14": "9990-int8-float", "15": "9990-int8-float", "16": "9990-int8-float", "17": "9990-int8-float", "18": "9990-int8-float", "19": "9990-int8-float", "20": "9990-int8-float", "21": "9990-int8-float", "22": "9990-int8-float", "23": "9990-int8-float", "24": "9990-int8-float", "25": "9990-int8-float", "26": "9990-int8-float", "27": "9990-int8-float", "28": "9990-int8-float", "29": "9990-int8-float", "30": "9990-int8-float", "31": "9990-int8-float", "32": "9990-int8-float", "33": "9990-int8-float", "34": "9990-int8-float", "35": "9990-int8-float", "36": "9990-int8-float", "37": "9990-int8-float", "38": "9990-int8-float", "39": "9990-int8-float", "40": "9990-int8-float", "41": "9990-int8-float", "42": "9990-int8-float", "43": "9990-int8-float", "44": "9990-int8-float", "45": "9990-int8-float", "46": "9990-int8-float", "47": "9990-int8-float", "48": "9990-int8-float", "49": "9990-int8-float", "50": "9990-int8-float", "51": "9990-int8-float", "52": "9990-int8-float"}, "entity.keys": {"1^dummy.int8Float.column": "9990-int8-float", "2^dummy.int8Float.column": "9990-int8-float"}, "plugins": ["9990-int8-float"]}], "entities": {"1": "9990-int8-float", "2": "9990-int8-float", "3": "9990-int8-float", "4": "9990-int8-float", "5": "9990-int8-float", "6": "9990-int8-float", "7": "9990-int8-float", "8": "9990-int8-float", "9": "9990-int8-float", "10": "9990-int8-float", "11": "9990-int8-float", "12": "9990-int8-float", "13": "9990-int8-float", "14": "9990-int8-float", "15": "9990-int8-float", "16": "9990-int8-float", "17": "9990-int8-float", "18": "9990-int8-float", "19": "9990-int8-float", "20": "9990-int8-float", "21": "9990-int8-float", "22": "9990-int8-float", "23": "9990-int8-float", "24": "9990-int8-float", "25": "9990-int8-float", "26": "9990-int8-float", "27": "9990-int8-float", "28": "9990-int8-float", "29": "9990-int8-float", "30": "9990-int8-float", "31": "9990-int8-float", "32": "9990-int8-float", "33": "9990-int8-float", "34": "9990-int8-float", "35": "9990-int8-float", "36": "9990-int8-float", "37": "9990-int8-float", "38": "9990-int8-float", "39": "9990-int8-float", "40": "9990-int8-float", "41": "9990-int8-float", "42": "9990-int8-float", "43": "9990-int8-float", "44": "9990-int8-float", "45": "9990-int8-float", "46": "9990-int8-float", "47": "9990-int8-float", "48": "9990-int8-float", "49": "9990-int8-float", "50": "9990-int8-float", "51": "9990-int8-float", "52": "9990-int8-float"}, "status": [], "instance.type": "", "plugins": ["9990-int8-float"]}, "admin.role": "yes", "query.id": 56278467306325, "sub.query.id": 56278467306326}