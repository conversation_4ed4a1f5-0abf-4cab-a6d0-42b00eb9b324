{"visualization.timeline": {"relative.timeline": "today", "visualization.timezone": "Asia/Calcutta", "from.datetime": 1659033000403, "to.datetime": 1659119399403, "duration": 86399}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.properties": {"grid": {"column.selection": "no", "columns": [], "sorting": {"limit": 10, "column": "packets.avg", "order": "desc"}, "header": "yes", "style": {"header.font.size": "medium"}, "searchable": "yes"}}, "granularity": {"value": 5, "unit": "m"}, "id": -1, "ui.event.uuid": "0e5f2af1-7bc3-4dfb-a1f2-df9efe5d9536", "session-id": "a877490b-6d0b-4cb0-8749-6a57bdde385c", "user.name": "admin", "query.id": 340779927015837, "visualization.data.sources": {"type": "flow", "filters": {"data.filter": {"filter": "include", "groups": [{"filter": "include", "conditions": [{"operand": "protocol", "operator": "in", "value": ["TCP", "SNMP", "IP"]}], "operator": "or"}], "operator": "and"}, "result.filter": {}}, "visualization.result.by": ["source.ip", "source.port", "destination.ip", "destination.port"], "data.points": [{"data.point": "packets", "aggregator": "avg", "entity.type": "source", "entities": {"************": "21003-flow", "************": "21003-flow", "************": "21003-flow", "************": "21003-flow", "************": "21003-flow"}, "plugins": ["21003-flow"]}], "plugins": ["21003-flow"], "entities": {"************": "21003-flow", "************": "21003-flow", "************": "21003-flow", "************": "21003-flow", "************": "21003-flow"}}, "admin.role": "yes", "sub.query.id": 340779927015838}