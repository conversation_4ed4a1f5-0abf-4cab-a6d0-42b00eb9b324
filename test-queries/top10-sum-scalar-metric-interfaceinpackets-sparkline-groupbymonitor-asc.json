{"visualization.timeline": {"relative.timeline": "today", "visualization.timezone": "Asia/Calcutta", "from.datetime": 1659033000403, "to.datetime": 1659119399403, "duration": 86399}, "visualization.category": "TopN", "visualization.type": "Grid", "visualization.properties": {"grid": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}, "sorting": {"limit": 10, "order": "asc", "column": "interface~in.packets.sum"}}, "sparkline": "yes"}, "visualization.result.by": ["monitor"], "granularity": {"value": 5, "unit": "m"}, "id": -1, "ui.event.uuid": "0e5f2af1-7bc3-4dfb-a1f2-df9efe5d9536", "session-id": "a877490b-6d0b-4cb0-8749-6a57bdde385c", "user.name": "admin", "query.id": 340779927015837, "visualization.data.sources": {"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "visualization.result.by": ["monitor"], "data.points": [{"data.point": "interface~in.packets", "aggregator": "sum", "entity.type": "all", "entities": {"1": "2000-interface", "2": "2000-interface", "3": "2000-interface", "4": "2000-interface", "5": "2000-interface", "6": "2000-interface", "7": "2000-interface", "8": "2000-interface", "9": "2000-interface", "10": "2000-interface"}, "entity.keys": {"1^x^interface~in.packets": "2000-interface", "2^x^interface~in.packets": "2000-interface", "3^x^interface~in.packets": "2000-interface", "4^x^interface~in.packets": "2000-interface", "5^x^interface~in.packets": "2000-interface", "6^x^interface~in.packets": "2000-interface", "7^x^interface~in.packets": "2000-interface", "8^x^interface~in.packets": "2000-interface", "9^x^interface~in.packets": "2000-interface", "10^x^interface~in.packets": "2000-interface", "1^y^interface~in.packets": "2000-interface", "2^y^interface~in.packets": "2000-interface", "3^y^interface~in.packets": "2000-interface", "4^y^interface~in.packets": "2000-interface", "5^y^interface~in.packets": "2000-interface", "6^y^interface~in.packets": "2000-interface", "7^y^interface~in.packets": "2000-interface", "8^y^interface~in.packets": "2000-interface", "9^y^interface~in.packets": "2000-interface", "10^y^interface~in.packets": "2000-interface", "1^z^interface~in.packets": "2000-interface", "2^z^interface~in.packets": "2000-interface", "3^z^interface~in.packets": "2000-interface", "4^z^interface~in.packets": "2000-interface", "5^z^interface~in.packets": "2000-interface", "6^z^interface~in.packets": "2000-interface", "7^z^interface~in.packets": "2000-interface", "8^z^interface~in.packets": "2000-interface", "9^z^interface~in.packets": "2000-interface", "10^z^interface~in.packets": "2000-interface", "1^w^interface~in.packets": "2000-interface", "2^w^interface~in.packets": "2000-interface", "3^w^interface~in.packets": "2000-interface", "4^w^interface~in.packets": "2000-interface", "5^w^interface~in.packets": "2000-interface", "6^w^interface~in.packets": "2000-interface", "7^w^interface~in.packets": "2000-interface", "8^w^interface~in.packets": "2000-interface", "9^w^interface~in.packets": "2000-interface", "10^w^interface~in.packets": "2000-interface", "1^a^interface~in.packets": "2000-interface", "2^a^interface~in.packets": "2000-interface", "3^a^interface~in.packets": "2000-interface", "4^a^interface~in.packets": "2000-interface", "5^a^interface~in.packets": "2000-interface", "6^a^interface~in.packets": "2000-interface", "7^a^interface~in.packets": "2000-interface", "8^a^interface~in.packets": "2000-interface", "9^a^interface~in.packets": "2000-interface", "10^a^interface~in.packets": "2000-interface", "11^a^interface~dummy.key": "2000-interface", "12^a^interface~dummy.key": "2000-interface"}, "plugins": ["2000-interface"]}], "plugins": ["2000-interface"], "entities": {"1": "2000-interface", "2": "2000-interface", "3": "2000-interface", "4": "2000-interface", "5": "2000-interface", "6": "2000-interface", "7": "2000-interface", "8": "2000-interface", "9": "2000-interface", "10": "2000-interface"}}, "admin.role": "yes", "sub.query.id": 340779927015838}